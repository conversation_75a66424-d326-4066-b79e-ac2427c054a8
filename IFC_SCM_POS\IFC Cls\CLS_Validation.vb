﻿Public Class CLS_Validation
    Public ErrorValidation As Boolean
    '  Dim Clsmsg As New Cls_Msg

    Public Sub TextOnlyNumberAndDot(Obh As Object, exx As KeyPressEventArgs)
        Try
            Dim TxtBx As TextBox = CType(Obh, TextBox)
            If (Not Char.IsControl(exx.KeyChar) AndAlso Not Char.IsDigit(exx.KeyChar) AndAlso exx.KeyChar <> ".") Then
                exx.Handled = True
            End If

            If (exx.KeyChar = "." AndAlso TxtBx.Text.IndexOf(".") > -1) Then
                exx.Handled = True
            End If
        Catch ex As Exception
            MsgBox(ex.Message)
        End Try
    End Sub

    'Public Function Empty_TxtValidations(Obj As Object, ValidId_ As String) As Boolean
    '    Dim Txt As TextBox = CType(Obj, TextBox)
    '    ErrorValidation = False
    '    If String.IsNullOrWhiteSpace(Txt.Text) Then
    '        Dim Dt As New DataTable
    '        Dt.Clear()
    '        Dt = Clsmsg.SelectMsg(ValidId_)
    '        MessageBox.Show(Dt.Rows(0)(1).ToString(), Dt.Rows(0)(2).ToString(), MessageBoxButtons.OK, MessageBoxIcon.Error)
    '        Txt.Focus()
    '        ErrorValidation = True
    '    End If

    '    Return ErrorValidation
    'End Function
    Public Sub FillCombo(Obj As Object, Dt As DataTable, Value As String, Name As String)
        Dim Comp As ComboBox = CType(Obj, ComboBox)
        Comp.DataSource = Nothing
        Comp.DataSource = Dt
        Comp.DisplayMember = Name
        Comp.ValueMember = Value
        Comp.Text = Nothing
        Comp.SelectedIndex = -1
    End Sub

    'Public Function Empty_CompValidations(Obj As Object, ValidId_ As String) As Boolean
    '    Dim Comp As ComboBox = CType(Obj, ComboBox)
    '    ErrorValidation = False
    '    If Comp.SelectedIndex = -1 Or String.IsNullOrWhiteSpace(Comp.Text) Then
    '        Dim Dt As New DataTable
    '        Dt.Clear()
    '        Dt = Clsmsg.SelectMsg(ValidId_)
    '        MessageBox.Show(Dt.Rows(0)(1).ToString(), Dt.Rows(0)(2).ToString(), MessageBoxButtons.OK, MessageBoxIcon.Error)
    '        Comp.Focus()
    '        ErrorValidation = True
    '    End If

    '    Return ErrorValidation
    'End Function

    Public Sub TextLeav_Number(obj As Object, ex As EventArgs)
        Dim txtbx As TextBox = CType(obj, TextBox)

        If (String.IsNullOrEmpty(txtbx.Text)) Or txtbx.Text = "." Then
            txtbx.Text = 0
        End If

        If (txtbx.Text(0) = ".") Then
            txtbx.Text = "0" + txtbx.Text
        End If
    End Sub

    Public Sub TxtEnter_Number(obj As Object, ex As EventArgs)
        Dim txtbx As TextBox = CType(obj, TextBox)
        If (Double.Parse(txtbx.Text) = 0) Then
            txtbx.Text = String.Empty
        End If
    End Sub


End Class
