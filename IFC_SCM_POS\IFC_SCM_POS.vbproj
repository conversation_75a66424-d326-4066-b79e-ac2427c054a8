﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{5AE86ED3-D880-41FB-AA6B-174BFAC53B9E}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <StartupObject>IFC_SCM_POS.My.MyApplication</StartupObject>
    <RootNamespace>IFC_SCM_POS</RootNamespace>
    <AssemblyName>IFC_SCM_POS</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <MyType>WindowsForms</MyType>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\Debug\</OutputPath>
    <DocumentationFile>IFC_SCM_POS.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DocumentationFile>IFC_SCM_POS.xml</DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <PropertyGroup>
    <OptionExplicit>On</OptionExplicit>
  </PropertyGroup>
  <PropertyGroup>
    <OptionCompare>Binary</OptionCompare>
  </PropertyGroup>
  <PropertyGroup>
    <OptionStrict>Off</OptionStrict>
  </PropertyGroup>
  <PropertyGroup>
    <OptionInfer>On</OptionInfer>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>POS.ico</ApplicationIcon>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="C1.Win.C1Command.2, Version=2.0.20152.74, Culture=neutral, PublicKeyToken=e808566f358766d8" />
    <Reference Include="C1.Win.C1FlexGrid.2, Version=2.0.20152.74, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.C1Input.2, Version=2.0.20152.74, Culture=neutral, PublicKeyToken=7e7ff60f0c214f9a, processorArchitecture=MSIL" />
    <Reference Include="C1.Win.C1Sizer.2, Version=2.0.20152.74, Culture=neutral, PublicKeyToken=79882d576c6336da, processorArchitecture=MSIL" />
    <Reference Include="System" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Design" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Drawing.Design" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
  </ItemGroup>
  <ItemGroup>
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Drawing" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.Windows.Forms" />
    <Import Include="System.Linq" />
    <Import Include="System.Xml.Linq" />
    <Import Include="System.Threading.Tasks" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Frm_Progress.Designer.vb">
      <DependentUpon>Frm_Progress.vb</DependentUpon>
    </Compile>
    <Compile Include="Frm_Progress.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="IFC Cls\CLS_Validation.vb" />
    <Compile Include="IFC Cls\OnHand_Cls.vb" />
    <Compile Include="IFC Cls\Conn_Cls.vb" />
    <Compile Include="IFC Cls\Encrypt_Cls.vb" />
    <Compile Include="IFC Cls\IFC_Transaction.vb" />
    <Compile Include="IFC Cls\Setting_Cls.vb" />
    <Compile Include="IFC Cls\Validations.vb" />
    <Compile Include="IFC Setup Form\AdminPass_Frm.Designer.vb">
      <DependentUpon>AdminPass_Frm.vb</DependentUpon>
    </Compile>
    <Compile Include="IFC Setup Form\AdminPass_Frm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="IFC Setup Form\LoginServer_Frm.Designer.vb">
      <DependentUpon>LoginServer_Frm.vb</DependentUpon>
    </Compile>
    <Compile Include="IFC Setup Form\LoginServer_Frm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="IFC Setup Form\Setting_Frm.Designer.vb">
      <DependentUpon>Setting_Frm.vb</DependentUpon>
    </Compile>
    <Compile Include="IFC Setup Form\Setting_Frm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="IFC Setup Form\UploadMSG_Frm.designer.vb">
      <DependentUpon>UploadMSG_Frm.vb</DependentUpon>
    </Compile>
    <Compile Include="IFC Setup Form\UploadMSG_Frm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="IFC_Main_Frm.Designer.vb">
      <DependentUpon>IFC_Main_Frm.vb</DependentUpon>
    </Compile>
    <Compile Include="IFC_Main_Frm.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Frm_Progress.resx">
      <DependentUpon>Frm_Progress.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IFC Setup Form\AdminPass_Frm.resx">
      <DependentUpon>AdminPass_Frm.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IFC Setup Form\LoginServer_Frm.resx">
      <DependentUpon>LoginServer_Frm.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IFC Setup Form\Setting_Frm.resx">
      <DependentUpon>Setting_Frm.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IFC Setup Form\UploadMSG_Frm.resx">
      <DependentUpon>UploadMSG_Frm.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="IFC_Main_Frm.resx">
      <DependentUpon>IFC_Main_Frm.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="My Project\licenses.licx" />
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="POS.ico" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.VisualBasic.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>