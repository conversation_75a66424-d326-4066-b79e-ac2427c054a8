﻿Public Class LoginServer_Frm
    Public IsSelect, IsEnd As Boolean
    Public SourcePathFile, ConnectToServer As String
    Dim Conn As New Conn_Cls
    Dim Encrypt As New Encrypt_Cls
    Private Sub Button2_Click(sender As Object, e As EventArgs) Handles Button2.Click
        Try

            If String.IsNullOrEmpty(TxtServerName.Text) Then
                '   TxtDatabaseName.err = "Please Fill Data"
                MessageBox.Show("Please Enter Server Name", Conn.MSGTitle, MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
                TxtServerName.Focus()
                Return
            End If

            If String.IsNullOrEmpty(TxtUserName.Text) Then
                '   TxtDatabaseName.err = "Please Fill Data"
                MessageBox.Show("Please Enter User Name", Conn.MSGTitle, MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
                TxtUserName.Focus()
                Return
            End If

            If String.IsNullOrEmpty(TxtServerPass.Text) Then
                '   TxtDatabaseName.err = "Please Fill Data"
                MessageBox.Show("Please Enter Server Password", Conn.MSGTitle, MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
                TxtServerPass.Focus()
                Return
            End If

            If String.IsNullOrEmpty(TxtDatabaseName.Text) Then
                '   TxtDatabaseName.err = "Please Fill Data"
                MessageBox.Show("Please Enter DataBase Name", Conn.MSGTitle, MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
                TxtDatabaseName.Focus()
                Return
            End If

            If Comp_POS.SelectedIndex = -1 Then
                '   TxtDatabaseName.err = "Please Fill Data"
                MessageBox.Show("Please Choose POS Sysytem", Conn.MSGTitle, MessageBoxButtons.OK, MessageBoxIcon.Exclamation)
                Comp_POS.Focus()
                Return
            End If

            Dim Checktest As Boolean
            Checktest = Conn.TestConnectionPOS(TxtServerName.Text, TxtDatabaseName.Text, TxtUserName.Text, TxtServerPass.Text)
            If Checktest Then
                MessageBox.Show("Connection SuccessFull", Conn.MSGTitle, MessageBoxButtons.OK, MessageBoxIcon.Information)
                If My.Computer.FileSystem.FileExists(Me.SourcePathFile) Then
                    Kill(Me.SourcePathFile)
                End If

                ConnectToServer = Encrypt.Encrypt(TxtServerName.Text, Encrypt.EncryptKey) & Environment.NewLine
                ConnectToServer = ConnectToServer & Encrypt.Encrypt(TxtServerPass.Text, Encrypt.EncryptKey) & Environment.NewLine
                ConnectToServer = ConnectToServer & Encrypt.Encrypt(TxtDatabaseName.Text, Encrypt.EncryptKey) & Environment.NewLine
                ConnectToServer = ConnectToServer & Encrypt.Encrypt(TxtUserName.Text, Encrypt.EncryptKey) & Environment.NewLine
                ConnectToServer = ConnectToServer & Encrypt.Encrypt(Comp_POS.Text, Encrypt.EncryptKey) & Environment.NewLine

                System.IO.File.AppendAllText(AppDomain.CurrentDomain.BaseDirectory & "PathPOS.txt", ConnectToServer)


                IsSelect = True
                Close()
            Else
                MessageBox.Show("Connection Faild Please Try Angain", Conn.MSGTitle, MessageBoxButtons.OK, MessageBoxIcon.Warning)
                Return
            End If


        Catch ex As Exception
            MessageBox.Show(ex.ToString())
        End Try
    End Sub
    Sub New(Str As String, Optional IsClose As Boolean = False)

        ' This call is required by the designer.
        InitializeComponent()

        ' Add any initialization after the InitializeComponent() call.
        SourcePathFile = Str
        FillComp()
        IsEnd = IsClose

    End Sub
    Public Sub FillComp()
        Comp_POS.DataSource = Nothing
        Dim MyDic As Dictionary(Of Integer, String)
        'MyDic = Conn.FillDictionary
        MyDic = FillDictionary()
        Comp_POS.DataSource = New BindingSource(MyDic, Nothing)
        Comp_POS.DisplayMember = "Value" 'MyDic.KeyCollection
        Comp_POS.ValueMember = "Key" 'MyDic.Values
    End Sub
    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        Close()
    End Sub

    Private Sub LoginServer_Frm_FormClosing(sender As Object, e As FormClosingEventArgs) Handles Me.FormClosing
        If IsSelect = False Then
            If IsEnd Then Process.GetCurrentProcess.Kill()
        End If
    End Sub

    Public Function FillDictionary() As Dictionary(Of Integer, String)
        Dim Dic As New Dictionary(Of Integer, String)
        Dic.Clear()

        Dic.Add(1, "Matrix POS")
        Dic.Add(2, "Smart POS")
        Return Dic

    End Function
End Class