﻿Module Validations
    Public IsUps As Boolean
    Public Sub TxtEnter_Number(obj As Object, ex As EventArgs)
        Dim txtbx As TextBox = CType(obj, TextBox)
        If (Int32.Parse(txtbx.Text) = 0) Then
            txtbx.Text = String.Empty
        End If
    End Sub

    Public Sub TxtKeyPress_Number(Obh As Object, exx As KeyPressEventArgs)
        Try
            Dim TxtBx As TextBox = CType(Obh, TextBox)
            'AndAlso exx.KeyChar <> "."
            If (Not Char.IsControl(exx.KeyChar) AndAlso Not Char.IsDigit(exx.KeyChar)) Then
                exx.Handled = True
            End If

            'If (exx.KeyChar = "." AndAlso TxtBx.Text.IndexOf(".") > -1) Then
            '    exx.Handled = True
            'End If
        Catch ex As Exception
            MsgBox(ex.Message)
        End Try
    End Sub

    Public Sub TxtLeav_Number(obj As Object, ex As EventArgs)
        Dim txtbx As TextBox = CType(obj, TextBox)
        'Or txtbx.Text = "."
        If (String.IsNullOrEmpty(txtbx.Text)) Then
            txtbx.Text = 0
        End If

        'If (txtbx.Text(0) = ".") Then
        '    txtbx.Text = "0" + txtbx.Text
        'End If
    End Sub
End Module
