<?xml version="1.0"?>
<doc>
  <assembly>
    <name>C1.Win.C1Input.2</name>
  </assembly>
  <members>
    <member name="T:C1.Framework.ElementWrapper">
      <summary>
            Public class that derives from <see cref="T:System.ComponentModel.Component" /> and wraps a C1Framework Element.
            Add whatever object model you want, map to the inner Element as needed.
            The <see cref="T:C1.Framework.XViewDesigner" /> class adds design-time support to controls that 
            contain <see cref="T:C1.Framework.ElementWrapper" /> objects, so you can select and edit them using the 
            property window. Serialization is also supported.
            </summary>
    </member>
    <member name="T:C1.Framework.ElementWrapperCollection`1">
      <summary>
            Public collection of <see cref="T:C1.Framework.ElementWrapper" /> objects. 
            The ElementWrapperCollection is a public wrapper for the Element.Children collection.
            This class exposes methods that allow you to retrieve, add, and remove ElementWrappers
            from the collection. Any changes made to this collection are automatically reflected
            in the wrapped Element's Children collection.
            </summary>
    </member>
    <member name="T:C1.Framework.ResourceLoader">
      <summary>
            Class with static methods used for enumerating and retrieving application
            resources.
            </summary>
    </member>
    <member name="M:C1.Framework.ResourceLoader.GetImages">
      <summary>
            Returns all images from the entry assembly. 
            </summary>
      <returns>A collection of name/image pairs.</returns>
    </member>
    <member name="M:C1.Framework.ResourceLoader.GetImages(System.Reflection.Assembly)">
      <summary>
            Returns all images from the specified assembly. 
            </summary>
      <param name="a">An <see cref="T:System.Reflection.Assembly" /> to load images from.</param>
      <returns>A collection of name/image pairs.</returns>
    </member>
    <member name="M:C1.Framework.ResourceLoader.GetImage(System.String)">
      <summary>
            Loads an image from the entry assembly. 
            </summary>
      <param name="name">The case-insensitive name of the requested image.</param>
      <returns>The <see cref="T:System.Drawing.Image" /> if it is found; null otherwise.</returns>
    </member>
    <member name="M:C1.Framework.ResourceLoader.GetImage(System.Reflection.Assembly,System.String)">
      <summary>
            Loads an image from the specified assembly. 
            </summary>
      <param name="a">An <see cref="T:System.Reflection.Assembly" /> to load image from.</param>
      <param name="name">The case-insensitive name of the requested image.</param>
      <returns>The <see cref="T:System.Drawing.Image" /> if it is found; null otherwise.</returns>
    </member>
    <member name="M:C1.Framework.ResourceLoader.GetStream(System.String)">
      <summary>
            Loads the specified manifest resource from the entry assembly. 
            </summary>
      <param name="name">The case-insensitive name of the manifest resource being requested.</param>
      <returns>A <see cref="T:System.IO.Stream" /> representing this manifest resource.</returns>
    </member>
    <member name="M:C1.Framework.ResourceLoader.GetStream(System.Reflection.Assembly,System.String)">
      <summary>
            Loads the specified manifest resource from the specified assembly. 
            </summary>
      <param name="a">An <see cref="T:System.Reflection.Assembly" /> to load resource from.</param>
      <param name="name">The case-insensitive name of the manifest resource being requested.</param>
      <returns>A <see cref="T:System.IO.Stream" /> representing this manifest resource.</returns>
    </member>
    <member name="M:C1.Framework.ResourceLoader.DecodeImage(System.String)">
      <summary>
            Decodes an image from a base-64-encoded string.
            </summary>
      <param name="data">String that contains the encoded image data.</param>
      <returns>The <see cref="T:System.Drawing.Image" /> encoded in the string.</returns>
    </member>
    <member name="T:C1.Framework.ScrollableControl">
      <summary>
            Defines an abstract base class for controls that support auto-scrolling behavior. 
            </summary>
    </member>
    <member name="M:C1.Framework.ScrollableControl.#ctor">
      <summary>
            Initializes the <see cref="T:C1.Framework.ScrollableControl" /> properties.
            </summary>
    </member>
    <member name="M:C1.Framework.ScrollableControl.OnScroll(System.Windows.Forms.ScrollBars)">
      <summary>
            Override this method to get notifications when the control scrolls.
            </summary>
      <param name="sb">
      </param>
    </member>
    <member name="M:C1.Framework.ScrollableControl.BuildScrollableRectangle(System.Int32,System.Int32)">
      <summary>
            This method is used to set the scroll rectangle.
            Override it to customize the scrolling behavior.
            </summary>
      <param name="dx">
      </param>
      <param name="dy">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Framework.ScrollableControl.HandleScrollMessage(System.Windows.Forms.Message)">
      <summary>
            Handles scroll messages.
            </summary>
      <param name="m">
      </param>
    </member>
    <member name="M:C1.Framework.ScrollableControl.SetClientSizeCore(System.Int32,System.Int32)">
      <summary>
            This member overrides <see cref="M:System.Windows.Forms.Control.SetClientSizeCore(System.Int32,System.Int32)" />.
            Override this method to account for scrollbars size.
            </summary>
      <param name="x">
      </param>
      <param name="y">
      </param>
    </member>
    <member name="M:C1.Framework.ScrollableControl.OnInvalidated(System.Windows.Forms.InvalidateEventArgs)">
      <summary>
            This member overrides <see cref="M:System.Windows.Forms.Control.OnInvalidated(System.Windows.Forms.InvalidateEventArgs)" />.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Framework.ScrollableControl.OnPaint(System.Windows.Forms.PaintEventArgs)">
      <summary>
            This member overrides <see cref="M:System.Windows.Forms.Control.OnPaint(System.Windows.Forms.PaintEventArgs)" />.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Framework.ScrollableControl.OnSizeChanged(System.EventArgs)">
      <summary>
            This member overrides <see cref="M:System.Windows.Forms.Control.OnSizeChanged(System.EventArgs)" />.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Framework.ScrollableControl.OnMouseWheel(System.Windows.Forms.MouseEventArgs)">
      <summary>
            This member overrides <see cref="M:System.Windows.Forms.Control.OnMouseWheel(System.Windows.Forms.MouseEventArgs)" />.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Framework.ScrollableControl.WndProc(System.Windows.Forms.Message@)">
      <summary>
            This member overrides <see cref="M:System.Windows.Forms.Control.WndProc(System.Windows.Forms.Message@)" />.
            </summary>
      <param name="m">
      </param>
    </member>
    <member name="M:C1.Framework.ScrollableControl.GetScrollSize(System.Int32)">
      <summary>
            Retrieves the specified scroll bar range.
            </summary>
      <param name="nBar">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Framework.ScrollableControl.GetScrollPos(System.Int32)">
      <summary>
            Gets the scroll position of the specified scroll bar.
            </summary>
      <param name="nBar">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Framework.ScrollableControl.GetTrackPos(System.Int32)">
      <summary>
            Gets the immediate position of the specified scroll box that the user is dragging.
            </summary>
      <param name="nBar">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Framework.ScrollableControl.SetScrollPos(System.Int32,System.Int32)">
      <summary>
            Sets the position of the specified scroll bar.
            </summary>
      <param name="nBar">
      </param>
      <param name="pos">Specifies the new position of the scroll box. 
            The position must be within the scrolling range. </param>
      <returns>The previous position of the scroll box indicates success. 
            Zero indicates failure</returns>
    </member>
    <member name="P:C1.Framework.ScrollableControl.Text">
      <summary>
            Gets or sets the text associated with the control.
            </summary>
    </member>
    <member name="P:C1.Framework.ScrollableControl.ScrollBars">
      <summary>
            Gets or sets which scroll bars should appear on the control.
            </summary>
    </member>
    <member name="P:C1.Framework.ScrollableControl.ScrollPosition">
      <summary>
            Gets or sets the <see cref="T:System.Drawing.Point" /> value determining the current scroll position.
            </summary>
    </member>
    <member name="P:C1.Framework.ScrollableControl.ScrollSize">
      <summary>
            Gets or sets the size of the document.
            </summary>
    </member>
    <member name="P:C1.Framework.ScrollableControl.ScrollTrack">
      <summary>
            Gets or sets whether the control should scroll as the user drags the scrollbar thumb.
            </summary>
    </member>
    <member name="P:C1.Framework.ScrollableControl.BorderStyle">
      <summary>
            Gets or sets the <see cref="P:C1.Framework.ScrollableControl.BorderStyle" /> value determining 
            the type of border around the control.
            </summary>
    </member>
    <member name="P:C1.Framework.ScrollableControl.Cursor">
      <summary>
            Gets or sets the <see cref="P:C1.Framework.ScrollableControl.Cursor" /> that appears when 
            the mouse moves over the control.
            </summary>
    </member>
    <member name="P:C1.Framework.ScrollableControl.ScrollableRectangle">
      <summary>
            This property is used to set the scroll page size.
            Override it to customize the scrolling behavior.
            </summary>
    </member>
    <member name="P:C1.Framework.ScrollableControl.CreateParams">
      <summary>
            This member overrides <see cref="P:System.Windows.Forms.Control.CreateParams" />.
            </summary>
    </member>
    <member name="P:C1.Framework.ScrollableControl.SmallChange">
      <summary>
            Gets or sets the distance to move a scroll bar in response to 
            a small scroll command.
            </summary>
    </member>
    <member name="P:C1.Framework.ScrollableControl.UseScrollWindow">
      <summary>
            Gets or sets a <see cref="T:System.Boolean" /> value determining whether control shows scroll bars.
            </summary>
    </member>
    <member name="T:C1.Framework.XView">
      <summary>
        <para>Control that hosts X elements.</para>
        <para>The control has an <see cref="P:C1.Framework.XView.Element" /> property that gets or
            sets the element to be displayed.</para>
        <para>The control is responsible for displaying the element, providing
            scrolling, and routing mouse and keyboard events to the hosted element.</para>
      </summary>
    </member>
    <member name="M:C1.Framework.XView.#ctor">
      <summary>
            Initializes a new instance of the <see cref="T:C1.Framework.XView" /> control.
            </summary>
    </member>
    <member name="M:C1.Framework.XView.CreateGraphics">
      <summary>
            Creates a <see cref="T:System.Drawing.Graphics" /> object for the control.
            </summary>
      <returns>A <see cref="T:System.Drawing.Graphics" /> object for the control.</returns>
      <remarks>
            The <see cref="T:System.Drawing.Graphics" /> object returned by this method reflects the current
            settings for the <see cref="P:C1.Framework.XView.SmoothingMode" /> and <see cref="P:C1.Framework.XView.TextRenderingHint" />
            properties.
            </remarks>
    </member>
    <member name="M:C1.Framework.XView.ScrollIntoView(System.Drawing.Point)">
      <summary>
            Scrolls a given point into view.
            </summary>
      <param name="pt">Point to scroll into view.</param>
    </member>
    <member name="M:C1.Framework.XView.ScrollIntoView(System.Drawing.Rectangle)">
      <summary>
            Scrolls a given rectangle into view.
            </summary>
      <param name="r">Rectangle to scroll into view.</param>
    </member>
    <member name="M:C1.Framework.XView.Dispose(System.Boolean)">
      <summary>
            Releases all resources used by the control. 
            </summary>
      <param name="disposing">
      </param>
    </member>
    <member name="M:C1.Framework.XView.IsInputKey(System.Windows.Forms.Keys)">
      <summary>
            Determines whether the specified key is a regular input key or 
            a special key that requires preprocessing.
            </summary>
      <param name="keyData">One of the <see cref="T:System.Windows.Forms.Keys" /> values.</param>
      <returns>True if the specified key is a regular input key; otherwise, false.</returns>
    </member>
    <member name="M:C1.Framework.XView.OnGotFocus(System.EventArgs)">
      <summary>
            Raises the <see cref="E:System.Windows.Forms.Control.GotFocus" /> event. 
            </summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:C1.Framework.XView.OnLeave(System.EventArgs)">
      <summary>
            Raises the <see cref="E:System.Windows.Forms.Control.Leave" /> event. 
            </summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:C1.Framework.XView.OnValidating(System.ComponentModel.CancelEventArgs)">
      <summary>
            Raises the <see cref="E:System.Windows.Forms.Control.Validating" /> event.
            </summary>
      <param name="e">A <see cref="T:System.ComponentModel.CancelEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:C1.Framework.XView.OnValidated(System.EventArgs)">
      <summary>
            Raises the <see cref="E:System.Windows.Forms.Control.Validated" /> event.
            </summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:C1.Framework.XView.OnKeyDown(System.Windows.Forms.KeyEventArgs)">
      <summary>
            Raises the <see cref="E:System.Windows.Forms.Control.KeyDown" /> event. 
            </summary>
      <param name="e">An <see cref="T:System.Windows.Forms.KeyEventArgs" /> 
            that contains the event data.</param>
    </member>
    <member name="M:C1.Framework.XView.OnKeyUp(System.Windows.Forms.KeyEventArgs)">
      <summary>
            Raises the <see cref="E:System.Windows.Forms.Control.KeyUp" /> event. 
            </summary>
      <param name="e">An <see cref="T:System.Windows.Forms.KeyEventArgs" /> 
            that contains the event data.</param>
    </member>
    <member name="M:C1.Framework.XView.OnKeyPress(System.Windows.Forms.KeyPressEventArgs)">
      <summary>
            Raises the <see cref="E:System.Windows.Forms.Control.KeyPress" /> event. 
            </summary>
      <param name="e">An <see cref="T:System.Windows.Forms.KeyPressEventArgs" /> 
            that contains the event data.</param>
    </member>
    <member name="M:C1.Framework.XView.OnMouseWheel(System.Windows.Forms.MouseEventArgs)">
      <summary>
            Raises the <see cref="E:System.Windows.Forms.Control.MouseWheel" /> event. 
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Framework.XView.OnMouseMove(System.Windows.Forms.MouseEventArgs)">
      <summary>
            Raises the <see cref="E:System.Windows.Forms.Control.MouseMove" /> event. 
            </summary>
      <param name="e">An <see cref="T:System.Windows.Forms.MouseEventArgs" /> 
            that contains the event data.</param>
    </member>
    <member name="M:C1.Framework.XView.OnClick(System.EventArgs)">
      <summary>
            Raises the <see cref="E:System.Windows.Forms.Control.Click" /> event. 
            </summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:C1.Framework.XView.OnDoubleClick(System.EventArgs)">
      <summary>
            Raises the <see cref="E:System.Windows.Forms.Control.DoubleClick" /> event. 
            </summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:C1.Framework.XView.OnMouseDown(System.Windows.Forms.MouseEventArgs)">
      <summary>
            Raises the <see cref="E:System.Windows.Forms.Control.MouseDown" /> event. 
            </summary>
      <param name="e">An <see cref="T:System.Windows.Forms.MouseEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:C1.Framework.XView.OnMouseUp(System.Windows.Forms.MouseEventArgs)">
      <summary>
            Raises the <see cref="E:System.Windows.Forms.Control.MouseUp" /> event. 
            </summary>
      <param name="e">An <see cref="T:System.Windows.Forms.MouseEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:C1.Framework.XView.OnMouseEnter(System.EventArgs)">
      <summary>
            Raises the <see cref="E:System.Windows.Forms.Control.MouseEnter" /> event. 
            </summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:C1.Framework.XView.OnMouseLeave(System.EventArgs)">
      <summary>
            Raises the <see cref="E:System.Windows.Forms.Control.MouseLeave" /> event. 
            </summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:C1.Framework.XView.IsMouseInsideXView">
      <summary>
            Returns True if the mouse pointer is inside the XView control.
            </summary>
    </member>
    <member name="M:C1.Framework.XView.OnPaint(System.Windows.Forms.PaintEventArgs)">
      <summary>
            Raises the <see cref="E:System.Windows.Forms.Control.Paint" /> event. 
            </summary>
      <param name="e">An <see cref="T:System.Windows.Forms.PaintEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:C1.Framework.XView.OnInvalidated(System.Windows.Forms.InvalidateEventArgs)">
      <summary>
            Remove hosted controls that belong to elements which became invisible or
            belong to a different control now.
            </summary>
      <remarks>
        <para>The host element is responsible for restoring the control's Parent property
            when it updates its position.</para>
        <para>Note: <see cref="T:C1.Framework.XView" /> determines that a <see cref="T:System.Windows.Forms.Control" />belongs to an 
            <see cref="P:C1.Framework.XView.Element" /> if the control's <see cref="P:System.Windows.Forms.Control.Tag" /> property contains 
            a reference to a <see cref="T:System.Windows.Forms.Control" /> object.</para>
      </remarks>
      <param name="e">
        <see cref="T:System.Windows.Forms.InvalidateEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:C1.Framework.XView.OnHandleDestroyed(System.EventArgs)">
      <summary>
            Remove hosted controls before disposing of the parent control.
            </summary>
      <remarks>
        <para>This allows the host element to remain valid even if their parent view is disposed.
            To dispose of the hosted control, call Dispose on the hosted control directly.</para>
        <para>Note: <see cref="T:C1.Framework.XView" /> determines that a <see cref="T:System.Windows.Forms.Control" />belongs to an 
            <see cref="P:C1.Framework.XView.Element" /> if the control's <see cref="P:System.Windows.Forms.Control.Tag" /> property contains 
            a reference to a <see cref="T:System.Windows.Forms.Control" /> object.</para>
      </remarks>
      <param name="e">
        <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:C1.Framework.XView.BuildScrollableRectangle(System.Int32,System.Int32)">
      <summary>
             Sets the scroll rectangle.
            </summary>
      <param name="dx">Amount to scroll in the x direction, in pixels.</param>
      <param name="dy">Amount to scroll in the y direction, in pixels.</param>
      <returns>
      </returns>
    </member>
    <member name="T:C1.Win.C1Input.UIStringsItemEventHandler">
      <summary>
            Represents a handler for an <see cref="T:C1.Win.C1Input.UIStrings" /> item related event.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.UIStringsItemEventArgs">
      <summary>
            Provides data for an <see cref="T:C1.Win.C1Input.UIStrings" /> item related event.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.UIStringsItemEventArgs.Key">
      <summary>
            Gets key of the item being added or changed.
            </summary>
      <value>The key.</value>
    </member>
    <member name="P:C1.Win.C1Input.UIStringsItemEventArgs.Value">
      <summary>
            Gets the string value.
            </summary>
      <value>The value.</value>
    </member>
    <member name="P:C1.Win.C1Input.UIStringsItemEventArgs.IsDefault">
      <summary>
            Gets a value indicating whether this instance is default.
            </summary>
      <value>
        <c>true</c> if this instance is default; otherwise, <c>false</c>.
            </value>
    </member>
    <member name="P:C1.Win.C1Input.UIStringsItemEventArgs.Description">
      <summary>
            Gets the description.
            </summary>
      <value>The description.</value>
    </member>
    <member name="T:C1.Win.C1Input.UIStrings">
      <summary>
            Represents a collection of end user visible UI strings.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.UIStrings.Add(System.Object,System.Int32,System.String,System.String)">
      <summary>
            Adds a string to the collection, specifying the ordinal.
            </summary>
      <param name="key">The key of the string.</param>
      <param name="ordinal">The ordinal of the string.</param>
      <param name="value">The string.</param>
      <param name="description">The description of the string.</param>
    </member>
    <member name="M:C1.Win.C1Input.UIStrings.Add(System.Object,System.String,System.String)">
      <summary>
            Adds a string to the collection in alphabetical order.
            </summary>
      <param name="key">The key of the string.</param>
      <param name="value">The string.</param>
      <param name="description">The description of the string.</param>
    </member>
    <member name="M:C1.Win.C1Input.UIStrings.AddInOrder(System.Object,System.String,System.String)">
      <summary>
            Adds a string to the collection, preserving the order.
            </summary>
      <param name="key">The key of the string.</param>
      <param name="value">The string.</param>
      <param name="description">The description of the string.</param>
    </member>
    <member name="M:C1.Win.C1Input.UIStrings.Reset">
      <summary>
            Sets all strings in collection to their default values.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.UIStrings.ShouldSerialize">
      <summary>
            Indicates whether any of the strings in the current collection
            have non-default values.
            </summary>
      <returns>
        <c>true</c> if any of the strings have non-default values, <c>false</c> otherwise.</returns>
    </member>
    <member name="M:C1.Win.C1Input.UIStrings.IsDefault(System.Object)">
      <summary>
            Tests whether a string in the collection has default value.
            </summary>
      <param name="key">The key of the string to test.</param>
      <returns>
        <c>true</c> if the string has default value, <c>false</c> otherwise.</returns>
    </member>
    <member name="M:C1.Win.C1Input.UIStrings.GetDescription(System.Object)">
      <summary>
            Returns the description of a string.
            </summary>
      <param name="key">The key of the string to get the description of.</param>
      <returns>The string's description</returns>
    </member>
    <member name="M:C1.Win.C1Input.UIStrings.Reset(System.Object)">
      <summary>
            Resets a string to its default value.
            </summary>
      <param name="key">The key of the string to reset.</param>
    </member>
    <member name="M:C1.Win.C1Input.UIStrings.GetKeyAt(System.Int32)">
      <summary>
            Returns the key of an item with the specified index.
            </summary>
      <param name="index">The item index.</param>
      <returns>The item's key.</returns>
    </member>
    <member name="M:C1.Win.C1Input.UIStrings.GetValueAt(System.Int32)">
      <summary>
            Gets the string by its index.
            </summary>
      <param name="index">The string index.</param>
      <returns>The string.</returns>
    </member>
    <member name="M:C1.Win.C1Input.UIStrings.SetValueAt(System.Int32,System.String)">
      <summary>
            Sets the value of a string with the specified index.
            </summary>
      <param name="index">The string index.</param>
      <param name="value">The new string value.</param>
    </member>
    <member name="M:C1.Win.C1Input.UIStrings.OnItemAdded(C1.Win.C1Input.UIStringsItemEventArgs)">
      <summary>
            Fires the <see cref="E:C1.Win.C1Input.UIStrings.ItemAdded" /> event.
            </summary>
      <param name="e">The event data.</param>
    </member>
    <member name="M:C1.Win.C1Input.UIStrings.OnItemChanged(C1.Win.C1Input.UIStringsItemEventArgs)">
      <summary>
            Fires the <see cref="E:C1.Win.C1Input.UIStrings.ItemChanged" /> event.
            </summary>
      <param name="e">The event data.</param>
    </member>
    <member name="M:C1.Win.C1Input.UIStrings.OnCollectionChanged(System.EventArgs)">
      <summary>
            Fires the <see cref="E:C1.Win.C1Input.UIStrings.CollectionChanged" /> event.
            </summary>
      <param name="e">The event data.</param>
    </member>
    <member name="P:C1.Win.C1Input.UIStrings.Count">
      <summary>
            Gets the number of elements contained in the collection.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.UIStrings.ItemAdded">
      <summary>
            Occurs when a new item is added to the collection.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.UIStrings.ItemChanged">
      <summary>
            Occurs when an item in the collection is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.UIStrings.CollectionChanged">
      <summary>
            Occurs when the collection has been changed.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.UIStrings.TypeConverter">
      <summary>
            Provides type conversion for the <see cref="T:C1.Win.C1Input.UIStrings" /> type.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.UIStrings.TypeConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
      <summary>
            For internal use.
            </summary>
      <param name="context">
      </param>
      <param name="culture">
      </param>
      <param name="value">
      </param>
      <param name="type">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.UIStrings.TypeConverter.GetProperties(System.ComponentModel.ITypeDescriptorContext,System.Object,System.Attribute[])">
      <summary>
            For internal use.
            </summary>
      <param name="context">
      </param>
      <param name="value">
      </param>
      <param name="attrFilter">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.UIStrings.TypeConverter.GetPropertiesSupported(System.ComponentModel.ITypeDescriptorContext)">
      <summary>
            For internal use.
            </summary>
      <param name="context">
      </param>
      <returns>
      </returns>
    </member>
    <member name="T:C1.Win.C1Input.C1BasePicker">
      <summary>
            Base class for C1ColorPicker and C1FontPicker controls.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.C1DropDownControl">
      <summary>
            Base class for C1DateEdit and C1NumericEdit controls. Includes support for dropdown and up/down buttons. 
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.C1TextBox">
      <summary>
            The main data bound control used for entering and editing information in a text form. 
            Supports data formatting for all data types, including special features for date-time formats. 
            Also supports edit mask, data validation and other features.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.#ctor">
      <summary>
            Initializes a new instance of the C1TextBox class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.Dispose(System.Boolean)">
      <summary>
            Releases the resources used by the C1TextBox.
            </summary>
      <param name="disposing">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.ResetVisualStyle">
      <summary>
            Resets <see cref="P:C1.Win.C1Input.C1TextBox.VisualStyle" /> to the default value.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.ShouldSerializeVisualStyle">
      <summary>
            Tests whether <see cref="P:C1.Win.C1Input.C1TextBox.VisualStyle" /> should be serialized.
            </summary>
      <returns>True if <see cref="P:C1.Win.C1Input.C1TextBox.VisualStyle" /> should be serialized, false otherwise.</returns>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.SetProposedValue(System.Object)">
      <summary>
            This method works exactly as setting the Value property, except that it does not change the internal variable
            storing the value before editing, so the user can press Esc and restore the previous value. 
            This method works only in edit mode. If the control is not in edit mode, this methods does nothing.
            </summary>
      <param name="value">Value to set to the control.</param>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.SuspendNumericInput(System.Boolean,System.Boolean)">
      <summary>
            This method temporarily disables numeric input mode
            </summary>
      <param name="suspend">True to suspend numeric input, False to resume it</param>
      <param name="updateValue">If True, control's Value is updated, obtained from the current control text.</param>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.ParseContent(System.Object@,C1.Win.C1Input.ErrorInfo)">
      <summary>
            Parses the Text string.
            </summary>
      <param name="parsedValue">Output parameter: Parsing result</param>
      <param name="errorInfo">Output parameter: Object detailing error information, if an error occurred.</param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.SetCutureInfo(System.Globalization.CultureInfo)">
      <summary>
            Sets culture.
            </summary>
      <param name="cultureInfo">New culture.</param>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.CheckValidationResult(C1.Win.C1Input.ErrorReasonEnum,System.Object@,C1.Win.C1Input.ErrorInfo)">
      <summary>
            Checks the result of the ParseContent method.
            </summary>
      <param name="reason">Value indicating the reason of validation failure</param>
      <param name="parsedValue">Parsed value of type DataType that was undergoing validation</param>
      <param name="errorInfo">Object detailing error information, if an error occurred</param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.UpdateValueWithCurrentText">
      <summary>
            Triggers parsing of the Text property and updating the Value.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.IncDateTimeInput">
      <summary>
            Increments the currently selected field in DateTimeInput mode.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.DecDateTimeInput">
      <summary>
            Decrements the currently selected field in DateTimeInput mode.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.ValidateText">
      <summary>
            Validates current Text property in the same manner as it usually validated after the control loses focus.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.SafeRecreateHandle">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.UpdateNC">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.DrawBorder(System.Drawing.Graphics,System.IntPtr,System.Boolean)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="g">
      </param>
      <param name="dc">
      </param>
      <param name="clear">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.DrawFocusRectangle">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.OnVisualStyleChanged(System.EventArgs)">
      <summary>
            Invokes the VisualStyleChanged event.
            </summary>
      <param name="e">The event arguments.</param>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.CalcSizeInternal">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.WmNCPaintSingleline(System.Windows.Forms.Message@)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="m">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.BeginInit">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.EndInit">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.UpdateDataType(System.Type)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="propertyType">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.OnCultureInfoSetup(C1.Win.C1Input.CultureInfoSetupEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.OnFormatError(C1.Win.C1Input.FormatErrorEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.OnFormatting(C1.Win.C1Input.FormatEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.OnFormatted(C1.Win.C1Input.FormatEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.OnParsing(C1.Win.C1Input.ParseEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.OnParsed(C1.Win.C1Input.ParseEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.OnPreValidating(C1.Win.C1Input.PreValidationEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.OnPreValidated(C1.Win.C1Input.PreValidationEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.OnPostValidating(C1.Win.C1Input.PostValidationEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.OnPostValidated(C1.Win.C1Input.PostValidationEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.OnDataTypeChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.OnValueChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.OnValidationError(C1.Win.C1Input.ValidationErrorEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.OnBindingFormatting(System.Windows.Forms.ConvertEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.OnBindingParsing(System.Windows.Forms.ConvertEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.OnVerticalAlignChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.OnMarginsChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.OnBorderColorChanged(System.EventArgs)">
      <summary>
            Raises the <see cref="E:C1.Win.C1Input.C1TextBox.BorderColorChanged" /> event.
            </summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
      <remarks>
        <para>Raising an event invokes the event handler through a delegate. </para>
        <para>The <b>OnBorderColorChanged</b> method also allows derived classes to handle the event without attaching a delegate. 
            This is the preferred technique for handling the event in a derived class.</para>
        <para>
          <b>Notes to Inheritors.</b> When overriding <b>OnBorderColorChanged</b> in a derived class, 
            be sure to call the base class's <b>OnBorderColorChanged</b> method so that registered delegates receive the event. 
            </para>
      </remarks>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.ResetText">
      <summary>
            Updates the control's Text with the current Value.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.C1TextBox.m_c1embedded">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.C1TextBox.m_c1embedEndOnSideArrows">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1TextBox.OnParentChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.FlatSystemBorder">
      <summary>
            Makes Fixed3D borders old-style looking (flat) 
            for System VisualStyle.
            This property is only for compatibility with legacy applications only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.MarkEmpty">
      <summary>
            Draws a red dotted line if Value is empty.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.BorderColor">
      <summary>
            Gets or sets the border color of the control.
            </summary>
      <remarks>
        <para>Use the <b>BorderColor</b> property to specify the border color of the control. 
            This property is set using a <see cref="T:System.Drawing.Color" /> object.</para>
        <para>The <b>BorderColor</b> property has an effect only when the <see cref="P:C1.Win.C1Input.C1TextBox.BorderStyle" /> property is set to <b>FixedSingle</b>. </para>
      </remarks>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.VisualStyle">
      <summary>
            Gets or sets the visual style of the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.DisabledForeColor">
      <summary>
            Gets or sets the foreground color of the disabled control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.NegativeForeColor">
      <summary>
            Gets or sets the foreground color of the control which contains negative value.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.UseColumnStyles">
      <summary>
            Indicates whether to honor the ColumnStyle properties defined on the
            C1ViewColumn object that this control is bound to.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.Culture">
      <summary>
            The culture ID.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.CalendarType">
      <summary>
            Calendar, used to format date.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.CultureName">
      <summary>
            The name selected for the Culture property in the Properties grid
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.UserCultureOverride">
      <summary>
            If True, current user regional settings override the selected culture.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.CultureInfo">
      <summary>
            The current CultureInfo object (available at runtime).
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.DisplayFormat">
      <summary>
            Format used to display the value when the control is not in edit mode (does not have input focus or is read-only).
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.EditFormat">
      <summary>
            Format used to display the value when the control is in edit mode (has input focus and is not read-only).
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.CharCategory">
      <summary>
            Defines rules to filter keyboard input.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.ParseInfo">
      <summary>
            Settings affecting parsing, that is, converting the string entered by the user to the data type.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.MaskInfo">
      <summary>
            Edit mask settings.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.MaxLength">
      <summary>
              Gets or sets the maximum number of characters or bytes that the control can hold.
            </summary>
      <value>
              The number of characters or bytes (determined by the 
              <b>LengthAsByte</b> property) 
              that can be entered into the control. <br />The default is <b>zero</b>.
            </value>
      <remarks>
              The <b>MaxLength</b> property allows you to limit the number of characters 
              a user can enter in the control. The default value is 0, 
              which does not limit the number of characters. Any number greater than 0 indicates 
              the maximum number of characters.
               The <see cref="P:C1.Win.C1Input.MaxLengthUnit" /> effects the displayed result along with the <b>MaxLength</b> property.
            </remarks>
      <exception cref="T:System.ArgumentOutOfRangeException">
              If value is less than zero, an ArgumentOutOfRangeException is thrown.
            </exception>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.MaxLineCount">
      <summary>
              Gets or sets the maximum number of lines.
            </summary>
      <value>
              An <b>int</b> value that indicates the maximum number of lines.
              <br />The default is <b>zero</b>.
            </value>
      <remarks>
        <para>
          <b>MaxLineCount</b> limits the acceptable lines of text when the <see cref="P:C1.Win.C1Input.C1TextBox.Multiline" />
              property is <see langword="true" />.
              </para>
        <para>
              This property does not work with wrapped lines, so preferrable is to use <b>MaxLineCount</b> when <see cref="P:System.Windows.Forms.TextBoxBase.WordWrap" />
              property is <see langword="true" />.
              </para>
        <para>
              The default value of this property, zero, means no limit.
              </para>
      </remarks>
      <seealso cref="P:C1.Win.C1Input.C1TextBox.Multiline" />
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.MaxLengthUnit">
      <summary>
              Gets or sets whether the maximum number of characters that fit in the control 
              are handled based on bytes, characters, or text elements.
            </summary>
      <remarks>
              Determines the unit for counting for the <see cref="P:C1.Win.C1Input.C1TextBox.MaxLength" /> property. 
              The value <b>LengthUnit.Byte</b> means the MaxLength is counted by Byte.
              The value <b>LengthUnit.Char</b> means the MaxLength is counted by Char.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.CaseSensitive">
      <summary>
            True if string comparisons are case-sensitive; otherwise, False. Default: False
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.FormatType">
      <summary>
            Enumeration value determining the formatting method, including standard .NET format specifiers, custom and programmatic formatting.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.CustomFormat">
      <summary>
            Custom format specifier used if FormatType = FormatTypeEnum.CustomFormat.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.NullText">
      <summary>
            String representing a DBNull value.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.EmptyAsNull">
      <summary>
            If True, empty strings are interpreted as null values (DBNull).
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.TrimStart">
      <summary>
            If True, leading spaces are removed.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.TrimEnd">
      <summary>
            If True, trailing spaces are removed.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.EditMask">
      <summary>
            The edit mask string restricting user input.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.NumericInput">
      <summary>
            Sets or gets a value indicating whether a special edit mode is used for numeric values.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.NumericInputKeys">
      <summary>
            Enables/disables the special keys used when NumericInput = True. Default: NumericInputKeyFlags.Standard.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.DateTimeInput">
      <summary>
            Whether or not a special edit mode is applied to DateTime values.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.CurrentTimeZone">
      <summary>
            If True, the control’s stored Value is in current time zone; otherwise, the Value is adjusted to the time zone defined by the GMTOffset property
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.GMTOffset">
      <summary>
            The time zone of the Value property used if CurrentTimeZone = False.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.WrapDateTimeFields">
      <summary>
            If this property is False the month will be automatically incremented when the user spins the date past the end of the month. The same about other parts of the date/time value.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.LoopPosition">
      <summary>
            Gets or sets a value indicating whether the position of the caret is automatically moved to the first field when the last field is filled.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.AutoChangePosition">
      <summary>
            Gets or sets a value indicating whether the position of the character is automatically changed
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.DaylightTimeAdjustment">
      <summary>
            The value specified in the GMTOffset property can be advanced or set back due to daylight-saving time changes. Default: NoAdjustments.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.MinShortYear">
      <summary>
            The minimum year that can be entered without leading zeros.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.PreValidation">
      <summary>
            Validation rules applied before parsing, that is, before converting the string entered by the user to the DataType (raw string validation).
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.PostValidation">
      <summary>
            Validation rules applied after parsing, that is, after converting the string entered by the user to the DataType (typed validation).
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.CausesValidation">
      <summary>
            Gets or sets a value indicating whether the control causes validation (i.e. calls Validated and Validating events)
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.Tag">
      <summary>
            Gets or sets the object that contains data about the control
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.BaseText">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.TextLength">
      <summary>
            Gets the length of text in the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.Lines">
      <summary>
            The lines of text in a multiline control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.TextDetached">
      <summary>
            Specifies whether the Value and Text properties can be set independently.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.DataSource">
      <summary>
            The data source object to which the control is bound.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.DataField">
      <summary>
            The field of the data source object to which the control is bound.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.DataType">
      <summary>
            Type of the Value property.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.ValueIsDbNull">
      <summary>
            Gets or sets a value indicating whether the Value is DBNull.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.Value">
      <summary>
            The main bindable property of a C1Input control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.ErrorInfo">
      <summary>
            Settings affecting error handling in the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.AcceptsEscape">
      <summary>
            Indicates if the Escape key that cancels modified value is handled by the control without passing it to the owner form (AcceptsEscape=True), or it is then passed to the form and can close it if the form has CancelButton (AcceptsEscape=False).
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.AcceptsTab">
      <summary>
            Indicates if Tab and Shift+Tab keys are accepted by control and move the focus to the next (previous) group of input characters in edit mask and in DateTimeInput mode.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.Modified">
      <summary>
            Gets or sets a value that indicates that the control has been modified by the user since the control received the input focus or its Value last set.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.EditMode">
      <summary>
            Returns True, if the control has input focus and TextDetached = False.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.Label">
      <summary>
            Label control associated with this input field.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.InitialSelection">
      <summary>
            Determines the selection position when a control receives input focus. Default: InitialSelectionEnum.SelectAll.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.DisableOnNoData">
      <summary>
            Gets or sets whether the text box should be disabled when it is bound to an empty data source, i.e. CurrencyManager.Count = 0.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.ShowFocusRectangle">
      <summary>
            Specifies whether a focus rectangle should be displayed around the client area when the control has the input focus.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.ShowContextMenu">
      <summary>
            Specifies whether the context menu can be displayed for the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.AutoSize">
      <summary>
            Gets or sets a value indicating whether the height of the control automatically adjusts when the font assigned to the control is changed.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.Enabled">
      <summary>
            Gets or sets a value indicating whether the control can respond to user interaction.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.VerticalAlign">
      <summary>
            Gets or sets the vertical alignment of the content in the control. The default is VerticalAlignEnum.Top.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.Margins">
      <summary>
            Gets or sets the spacing between the input control content and its edges, in pixels. Default: all margins are set to 0.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.ValueChangedBehavior">
      <summary>
            Allows backward compatibility with the old behavior, when the ValueChanged event previously fired on any text change.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.ExitOnLastChar">
      <summary>
              Gets or sets whether the next control in the tab order receives the focus as 
              soon as the control is filled at the last character. 
            </summary>
      <value>
        <see langword="true" /> if the focus is moved to the next control in the tab order as soon as the control
              is filled at the last character defined by the input control; otherwise, <see langword="false" />. 
               <br />
              The default is <see langword="false" />.
            </value>
      <remarks>
              The input focus moves to the next control when the last entered character causes the text to exceed the limit of the control.
            </remarks>
      <seealso cref="P:C1.Win.C1Input.C1TextBox.ExitOnLeftRightKey" />
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.ExitOnLeftRightKey">
      <summary>
              Gets or sets whether the focus automatically moves to the previous or next control in the tab order 
              when pressing the left or right arrow keys.
            </summary>
      <value>
              One of the <see cref="P:C1.Win.C1Input.C1TextBox.ExitOnLeftRightKey" /> enumeration values.  
              <br /> 
              The default is <see cref="F:C1.Win.C1Input.ExitOnLeftRightKey.None" />.
            </value>
      <remarks>
              The value of this property indicates the key which moves the input focus to the next control. 
              When the caret is at the last character of this control, focus moves out by pressing the right arrow key. 
              When the caret is before the first character of this control, focus moves out by pressing the left key.
            </remarks>
      <seealso cref="P:C1.Win.C1Input.C1TextBox.ExitOnLastChar" />
    </member>
    <member name="E:C1.Win.C1Input.C1TextBox.BorderColorChanged">
      <summary>
            Occurs when the BorderColor property has changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1TextBox.VisualStyleChanged">
      <summary>
            Occurs when the VisualStyle property has changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1TextBox.CultureInfoSetup">
      <summary>
            CultureInfo can be set up (on startup) or has been changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1TextBox.FormatError">
      <summary>
            Occurs when the control receives data that cannot be formatted according to the current format and edit mask settings.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1TextBox.Formatting">
      <summary>
            Formatting the value programmatically.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1TextBox.Formatted">
      <summary>
            Occurs after the value has been formatted.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1TextBox.Parsing">
      <summary>
            Programmatic parsing of the input string.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1TextBox.Parsed">
      <summary>
            Occurs after the input string value has been converted to the DataType.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1TextBox.PreValidating">
      <summary>
            Programmatic input string validation.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1TextBox.PreValidated">
      <summary>
            Occurs after the input string value has been validated.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1TextBox.PostValidating">
      <summary>
            Programmatic typed value validation.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1TextBox.PostValidated">
      <summary>
            Occurs after the typed value has been validated
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1TextBox.DataTypeChanged">
      <summary>
            Occurs when the DataType property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1TextBox.ValueChanged">
      <summary>
            Occurs when the Value changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1TextBox.ValidationError">
      <summary>
            Error occurred while parsing or validating the input string.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1TextBox.BindingFormatting">
      <summary>
            Occurs when Value is retrieved from the data source.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1TextBox.BindingParsing">
      <summary>
            Occurs when Value is stored in the data source.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1TextBox.VerticalAlignChanged">
      <summary>
            Occurs when the value of the VerticalAlign property has changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1TextBox.MarginsChanged">
      <summary>
            Occurs when the Margins property or one of the margins has changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1TextBox.InvalidInput">
      <summary>
              Occurs when invalid character is in input.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1TextBox.DefaultThemeRootPath">
      <summary>
            Default path for the control in xml theme file.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.#ctor">
      <summary>
            Initializes a new instance of the C1DropDownControl class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.Dispose(System.Boolean)">
      <summary>
            Releases the resources used by the C1DropDownControl.
            </summary>
      <param name="disposing">
        <b>true</b> to release both managed and unmanaged resources; <b>false</b> to release only unmanaged resources.</param>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.ShouldSerializeDropDownFormClassName">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.ResetDropDownFormClassName">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.ShouldSerializeButtonCursor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.ResetButtonCursor">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.ShouldSerializeButtonImages">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.ResetButtonImages">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.OpenDropDown">
      <summary>
            Shows the dropdown.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.CloseDropDown">
      <summary>
            Closes the dropdown.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.CloseDropDown(System.Boolean)">
      <summary>
            Closes the dropdown.
            </summary>
      <param name="doPost">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.SpinUp">
      <summary>
              Performs the spin up action.
            </summary>
      <remarks>
              This method has the same behavior as pressing the up key.
            </remarks>
      <seealso cref="M:C1.Win.C1Input.C1DropDownControl.SpinDown" />
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.SpinDown">
      <summary>
              Performs the spin down action.
            </summary>
      <remarks>
              This method has the same behavior as pressing the down key.
            </remarks>
      <seealso cref="M:C1.Win.C1Input.C1DropDownControl.SpinUp" />
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.OnDropDownOpening(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.OnBeforeDropDownOpen(System.ComponentModel.CancelEventArgs)">
      <summary>
      </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.OnDropDownOpened(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.OnDropDownClosed(C1.Win.C1Input.DropDownClosedEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.OnUpDownButtonClick(C1.Win.C1Input.UpDownButtonClickEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.OnModalButtonClick(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.OnCustomButtonClick(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.OnDropDownFormAlignChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.OnDropDownAlignChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.OnButtonCursorChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.OnCreateDropDownFrom(C1.Win.C1Input.DropDownForm)">
      <summary>
            Called when a drop down form is created.
            </summary>
      <param name="dropDownForm">The new dropdown form.</param>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.CalcSizeInternal">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.WmNCPaintSingleline(System.Windows.Forms.Message@)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="m">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.GetImageSize">
      <summary>
            Returns image size.
            </summary>
      <returns>The size object.</returns>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.DrawCustomArea(System.Drawing.Graphics)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="g">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.OnReadOnlyChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.OnEnabledChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.WndProc(System.Windows.Forms.Message@)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="m">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.OnMouseLeave(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.OnMouseMove(System.Windows.Forms.MouseEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.OnGotFocus(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.OnLostFocus(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.ProcessKeyPress(System.Windows.Forms.KeyPressEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.ProcessKeyDown(System.Windows.Forms.KeyEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.ProcessKeyUp(System.Windows.Forms.KeyEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.OnTextChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.OnValueChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.OnMouseWheel(System.Windows.Forms.MouseEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.OnCursorChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.ShouldHandleEscape">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.PreProcessMessage(System.Windows.Forms.Message@)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="m">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.C1DropDownControl.DrawImage(System.Drawing.Graphics,System.IntPtr,System.Drawing.Rectangle)">
      <summary>
            Draws image in the control.
            </summary>
      <param name="g">Grapthics object.</param>
      <param name="dc">Device context.</param>
      <param name="bounds">Rectangle to draw image in.</param>
    </member>
    <member name="P:C1.Win.C1Input.C1DropDownControl.AutoOpen">
      <summary>
            Gets or sets a value indicating whether to open the combo box when the control receives the focus.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DropDownControl.DropDownFormCreationTime">
      <summary>
            Determines the time when drop down form is created.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DropDownControl.ImagePlace">
      <summary>
            Has valid value only if ShowValueImage returns true.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DropDownControl.DropDownStyle">
      <summary>
            Gets or sets the style of the combo box.
            </summary>
      <remarks>
            The DropDownStyle property specifies whether the text portion can be edited.
            <para>
            If the value is Default, the text is editable and the dropdown list is displayed by clicking the down arrow.
            </para><para>
            If the value is DropDownList, the text is not editable and the dropdown list is displayed by clicking any part of the control. 
            </para></remarks>
    </member>
    <member name="P:C1.Win.C1Input.C1DropDownControl.Image">
      <summary>
            Gets or sets the button image.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DropDownControl.ImageIndex">
      <summary>
            Gets or sets the index of the button image in the ImageList.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DropDownControl.ImageKey">
      <summary>
            Gets or sets the key of the button image in the ImageList.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DropDownControl.ImageList">
      <summary>
            Gets or sets the ImageList to get the images to display in the drop down items.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DropDownControl.ImagePadding">
      <summary>
            Gets or sets the image padding.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DropDownControl.VisibleButtons">
      <summary>
            Gets or sets a value specifying which buttons are visible. The default is (UpDown Or DropDown).
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DropDownControl.ShowDropDownButton">
      <summary>
            Determines if the dropdown button is visible.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DropDownControl.ShowUpDownButtons">
      <summary>
            Determines if the up/down buttons are visible.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DropDownControl.ShowModalButton">
      <summary>
            Determines if the modal button is visible.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DropDownControl.ShowCustomButton">
      <summary>
            Determines if the modal button is visible.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DropDownControl.InterceptArrowKeys">
      <summary>
            Determines if the up/down buttons intercept and handle Up and Down arrow keys.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DropDownControl.DropDownFormClassName">
      <summary>
            Specifies the class name of a Form serving as the dropdown window.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DropDownControl.DropDownForm">
      <summary>
            Returns dropdown form instance associated with the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DropDownControl.DefaultDropDownFormClassName">
      <summary>
            Specifies form class that is used by default as the dropdown for the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DropDownControl.DefaultDropDownForm">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DropDownControl.DefaultSize">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DropDownControl.DroppedDown">
      <summary>
            Run-time read-only property indicating if the dropdown is currently open.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DropDownControl.ButtonWidth">
      <summary>
            Specifies the button width instead of using the default width.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DropDownControl.GapHeight">
      <summary>
            Distance in pixels between the control edge and the dropdown.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DropDownControl.MouseClickPassThrough">
      <summary>
            Gets or sets whether the mouse click event is consumed or passed through after closing the dropdown.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DropDownControl.DropDownFormAlign">
      <summary>
            Alignment of the dropdown form relative to the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DropDownControl.ButtonCursor">
      <summary>
            The cursor that is displayed when the mouse is over a button.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DropDownControl.ButtonImages">
      <summary>
            Gets or sets the images for buttons
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DropDownControl.Cursor">
      <summary>
            Gets or sets the cursor that is displayed when the mouse pointer is over the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DropDownControl.CustomAreaPlace">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DropDownControl.CustomAreaWidth">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DropDownControl.UpDownButtonAlignment">
      <summary>
            Gets or sets the alignment of the spin button. 
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1DropDownControl.DropDownOpening">
      <summary>
            Occurs just before the dropdown is opened.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1DropDownControl.BeforeDropDownOpen">
      <summary>
            Occurs just before the dropdown is opened.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1DropDownControl.DropDownOpened">
      <summary>
            Occurs after the dropdown is opened.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1DropDownControl.DropDownClosed">
      <summary>
            Occurs when the dropdown has been closed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1DropDownControl.UpDownButtonClick">
      <summary>
            Occurs when UpDown buttons are clicked.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1DropDownControl.ModalButtonClick">
      <summary>
            Occurs when the Modal button is clicked.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1DropDownControl.CustomButtonClick">
      <summary>
            Occurs when the Custom button is clicked.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1DropDownControl.DropDownFormAlignChanged">
      <summary>
            Occurs when the value of the DropDownFormAlign property has changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1DropDownControl.DropDownAlignChanged">
      <summary>
            Occurs when the value of the DropDownAlign property has changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1DropDownControl.ButtonCursorChanged">
      <summary>
            Event fired when the value of ButtonCursor property is changed.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DropDownControl.MinSizeInternal">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DropDownControl.ShowImage">
      <summary>
            Determines if image should be shown.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DropDownControl.DefaultThemeRootPath">
      <summary>
            Default path 
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1BasePicker.#ctor">
      <summary>
            Initializes a new instance of the C1BasePicker class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1BasePicker.OnParsing(C1.Win.C1Input.ParseEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1BasePicker.OnFormatting(C1.Win.C1Input.FormatEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1BasePicker.OnKeyDown(System.Windows.Forms.KeyEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1BasePicker.TextToValue(C1.Win.C1Input.C1BasePicker.TextToValueArgs@)">
      <summary>
            Should be overridden in deviced class and convert string to value.
            </summary>
      <param name="e">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.C1BasePicker.ValueToText(C1.Win.C1Input.C1BasePicker.ValueToTextArgs@)">
      <summary>
            Should be overridden in derived class and convert value to string.
            </summary>
      <param name="e">
      </param>
      <returns>
      </returns>
    </member>
    <member name="P:C1.Win.C1Input.C1BasePicker.DisplayFormat">
      <summary>
            Format used to display the value when the control is not in edit mode (does not have input focus or is read-only).
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1BasePicker.EditFormat">
      <summary>
            Format used to display the value when the control is in edit mode (has input focus and is not read-only).
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1BasePicker.ParseInfo">
      <summary>
            Settings affecting parsing, that is, converting the string entered by the user to the data type.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1BasePicker.InterceptArrowKeys">
      <summary>
            Settings affecting parsing, that is, converting the string entered by the user to the data type.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1BasePicker.AcceptsTab">
      <summary>
            Indicates if Tab and Shift+Tab keys are accepted by control and move the focus to the next (previous) group of input characters in edit mask and in DateTimeInput mode.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.C1Button">
      <summary>
            Represents a Windows button control supporting additional visual styles.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1Button.#ctor">
      <summary>
            Initializes a new instance of the C1Button class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1Button.Dispose(System.Boolean)">
      <summary>
            Releases the resources used by the C1Button.
            </summary>
      <param name="disposing">
        <b>true</b> to release both managed and unmanaged resources; <b>false</b> to release only unmanaged resources.</param>
    </member>
    <member name="M:C1.Win.C1Input.C1Button.GetPreferredSize(System.Drawing.Size)">
      <summary>
            Retrieves the size of a rectangular area into which a control can be fitted.
            </summary>
      <param name="proposedSize">The custom-sized area for a control.</param>
      <returns>An ordered pair of type <see cref="T:System.Drawing.Size" /> representing the width and height of a rectangle.</returns>
    </member>
    <member name="M:C1.Win.C1Input.C1Button.ResetVisualStyle">
      <summary>
            Resets <see cref="P:C1.Win.C1Input.C1Button.VisualStyle" /> to the default value.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1Button.ShouldSerializeVisualStyle">
      <summary>
            Tests whether <see cref="P:C1.Win.C1Input.C1Button.VisualStyle" /> should be serialized.
            </summary>
      <returns>True if <see cref="P:C1.Win.C1Input.C1Button.VisualStyle" /> should be serialized, false otherwise.</returns>
    </member>
    <member name="M:C1.Win.C1Input.C1Button.OnVisualStyleChanged(System.EventArgs)">
      <summary>
            Invokes the VisualStyleChanged event.
            </summary>
      <param name="e">The event arguments.</param>
    </member>
    <member name="M:C1.Win.C1Input.C1Button.ResetVisualStyleBaseStyle">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1Button.ShouldSerializeVisualStyleBaseStyle">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>True if VisualStyleBaseStyle should be serialized, false otherwise.</returns>
    </member>
    <member name="P:C1.Win.C1Input.C1Button.VisualStyle">
      <summary>
            Gets or sets the visual style of the control.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1Button.VisualStyleChanged">
      <summary>
            Occurs when the VisualStyle property has changed.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1Button.DefaultThemeRootPath">
      <summary>
            Default Theme root path.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.C1CheckBox">
      <summary>
            In addition to the standard CheckBox functionality, 
            C1CheckBox supports data binding to data source fields of Boolean, String, or Integer types. 
            C1CheckBox has BorderStyle and BorderColor properties.
            C1CheckBox supports visual styles.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1CheckBox.#ctor">
      <summary>
            Initializes a new instance of the C1CheckBox class. 
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1CheckBox.Dispose(System.Boolean)">
      <summary>
            Releases the resources used by the C1CheckBox.
            </summary>
      <param name="disposing">
        <b>true</b> to release both managed and unmanaged resources; <b>false</b> to release only unmanaged resources.</param>
    </member>
    <member name="M:C1.Win.C1Input.C1CheckBox.OnVisualStyleChanged(System.EventArgs)">
      <summary>
            Invokes the VisualStyleChanged event.
            </summary>
      <param name="e">The event arguments.</param>
    </member>
    <member name="M:C1.Win.C1Input.C1CheckBox.GetPreferredSize(System.Drawing.Size)">
      <summary>
            Retrieves the size of a rectangular area into which a control can be fitted.
            </summary>
      <param name="proposedSize">The custom-sized area for a control.</param>
      <returns>An ordered pair of type <see cref="T:System.Drawing.Size" /> representing the width and height of a rectangle.</returns>
    </member>
    <member name="P:C1.Win.C1Input.C1CheckBox.DataSource">
      <summary>
            The data source object to which the control is bound.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1CheckBox.DataField">
      <summary>
            The field of the data source object to which the control is bound.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1CheckBox.DataType">
      <summary>
            Type of the Value property.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1CheckBox.DisableOnNoData">
      <summary>
            Gets or sets whether the text box should be disabled when it is bound to an empty data source, i.e. CurrencyManager.Count = 0.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1CheckBox.Value">
      <summary>
            The main bindable property of a C1Input control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1CheckBox.TranslateValues">
      <summary>
            Translates between string values and check box states.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1CheckBox.VisualStyle">
      <summary>
            Gets or sets the visual style of the control.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1CheckBox.VisualStyleChanged">
      <summary>
            Occurs when the VisualStyle property has changed.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1CheckBox.BorderStyle">
      <summary>
            Gets or sets the border style of the C1CheckBox control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1CheckBox.BorderColor">
      <summary>
            Gets or sets the border color of the control.
            </summary>
      <remarks>
        <para>Use the <b>BorderColor</b> property to specify the border color of the control. 
            This property is set using a <see cref="T:System.Drawing.Color" /> object.</para>
      </remarks>
    </member>
    <member name="P:C1.Win.C1Input.C1CheckBox.DefaultCheckboxRootPath">
      <summary>
            Default C1CheckBox path in Theme xml file.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1CheckBox.DefaultButtonThemeRootPath">
      <summary>
            Default C1Button path in Theme xml file.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.TranslateValues">
      <summary>
            Translates between string values and check box states.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.TranslateValues.Checked">
      <summary>
            Value for <b>Checked</b> state.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.TranslateValues.Unchecked">
      <summary>
            Value for <b>Unchecked</b> state.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.TranslateValues.Indeterminate">
      <summary>
            Value for <b>Indeterminate</b> state.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.TranslateValues.IndeterminateIsDbNull">
      <summary>
            Determines if DbNull is used for indeterminate state of check box.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.C1ColorPicker">
      <summary>
            Represents a Windows color picker control supporting additional visual styles.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1ColorPicker.#ctor">
      <summary>
            Initializes a new instance of the C1ColorPicker class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1ColorPicker.TextToValue(C1.Win.C1Input.C1BasePicker.TextToValueArgs@)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1ColorPicker.ValueToText(C1.Win.C1Input.C1BasePicker.ValueToTextArgs@)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1ColorPicker.OnModalButtonClick(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1ColorPicker.OnKeyDown(System.Windows.Forms.KeyEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1ColorPicker.GetImageSize">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1ColorPicker.DrawImage(System.Drawing.Graphics,System.IntPtr,System.Drawing.Rectangle)">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1ColorPicker.AllowTransparent">
      <summary>
            Gets or sets a value indicating whether the transparent color can de selected.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1ColorPicker.AllowEmpty">
      <summary>
            Gets or sets a value indicating whether the empty color can de selected.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1ColorPicker.EmptyColorCaption">
      <summary>
            Gets or sets a caption for the empty color.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1ColorPicker.DefaultDropDownFormClassName">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1ColorPicker.ShowImage">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1ColorPicker.VisibleButtons">
      <summary>
            Gets or sets a value specifying which buttons are visible. The default is (UpDown Or DropDown).
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1ColorPicker.DataType">
      <summary>
            Type of the Value property.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1ColorPicker.DefaultThemeRootPath">
      <summary>
            Default path for the control in xml theme file.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.C1ColorPickerDropDownForm">
      <summary>
            Represents drop down color picker form.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.DropDownForm">
      <summary>
            Base class for custom dropdown forms.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DropDownForm.#ctor">
      <summary>
            Initializes a new instance of the DropDownForm class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DropDownForm.Dispose(System.Boolean)">
      <summary>
            Releases the resources used by the DropDownForm.
            </summary>
      <param name="disposing">
        <b>true</b> to release both managed and unmanaged resources; <b>false</b> to release only unmanaged resources.</param>
    </member>
    <member name="M:C1.Win.C1Input.DropDownForm.OnVisualStyleChanged(System.EventArgs)">
      <summary>
            Invokes the VisualStyleChanged event.
            </summary>
      <param name="e">The event arguments.</param>
    </member>
    <member name="M:C1.Win.C1Input.DropDownForm.PreFilterMessage(System.Windows.Forms.Message@)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="m">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.DropDownForm.OnPostChanges(System.EventArgs)">
      <summary>
            Fires the PostChanges event.
            </summary>
      <param name="e">Contains arguments describing the event.</param>
    </member>
    <member name="M:C1.Win.C1Input.DropDownForm.OnCancelChanges(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.DropDownForm.OnOpen(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.DropDownForm.OnOwnerControlTextChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.DropDownForm.OnOwnerControlValueChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.DropDownForm.OnActivated(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.DropDownForm.OnDeactivate(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.DropDownForm.CreateHandle">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DropDownForm.OnVisibleChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.DropDownForm.WndProc(System.Windows.Forms.Message@)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="m">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.DropDownForm.OnKeyDown(System.Windows.Forms.KeyEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.DropDownForm.OnKeyPress(System.Windows.Forms.KeyPressEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.DropDownForm.CloseDropDown">
      <summary>
            Closes the dropdown form.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DropDownForm.CloseDropDown(System.Boolean)">
      <summary>
            Closes the dropdown form.
            </summary>
      <param name="doPost">
      </param>
    </member>
    <member name="P:C1.Win.C1Input.DropDownForm.ShouldApplyTheme">
      <summary>
            Gets a value indicating whether theme should be applied to dropdown form
            when it applied to <see cref="T:C1.Win.C1Input.C1DropDownControl" />. This property returns <b>true</b>
            by default.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DropDownForm.BorderStyle">
      <summary>
            Determines the style of the control's border.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DropDownForm.ClientSize">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DropDownForm.Options">
      <summary>
            Determine various behavioral options of the dropdown form.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DropDownForm.FocusControl">
      <summary>
            Determines the control on the form that gets input focus when the form is shown.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DropDownForm.DropDownAlign">
      <summary>
            Alignment of the dropdown form relative to the owner control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DropDownForm.OwnerControl">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DropDownForm.CreateParams">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DropDownForm.KeyPreview">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.DropDownForm.PostChanges">
      <summary>
            Occurs when Value property of the OwnerControl is updated by the dropdown form.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.DropDownForm.CancelChanges">
      <summary>
            Occurs when changes made by dropdown form are cancelled.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.DropDownForm.Open">
      <summary>
            Occurs when dropdown form is opened.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.DropDownForm.OwnerControlTextChanged">
      <summary>
            Occurs when OwnerControl.Text property is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.DropDownForm.OwnerControlValueChanged">
      <summary>
            Occurs when OwnerControl.Value property is changed.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1ColorPickerDropDownForm.#ctor">
      <summary>
            Initializes a new instance of the C1ColorPickerDropDownForm class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1ColorPickerDropDownForm.OnPostChanges(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1ColorPickerDropDownForm.OnVisibleChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1ColorPickerDropDownForm.Dispose(System.Boolean)">
      <summary>
            Clean up any resources being used.
            </summary>
      <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
    </member>
    <member name="T:C1.Win.C1Input.C1ComboBox">
      <summary>
            Represents a composite control combining a textbox and a drop-down item list.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1ComboBox.#ctor">
      <summary>
            Initializes a new instance of the C1ComboBox class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1ComboBox.SetItemsDataSource(System.Object,System.String)">
      <summary>
            Sets items data source and data member for the C1ComboBox.
            </summary>
      <param name="dataSource">Data source.</param>
      <param name="dataMember">Data member.</param>
    </member>
    <member name="M:C1.Win.C1Input.C1ComboBox.SetItemsDataSource(System.Object,System.String,System.Int32)">
      <summary>
            Sets items data source, data member and initially selected item index for the C1ComboBox.
            </summary>
      <param name="dataSource">Data source.</param>
      <param name="dataMember">Data member.</param>
      <param name="selectedIndex">Initially selected item index.</param>
    </member>
    <member name="M:C1.Win.C1Input.C1ComboBox.CreateItemsCollection">
      <summary>
            Creates a new instance of the item collection.
            </summary>
      <returns>A <see cref="T:C1.Win.C1Input.ComboBoxItemList" /> that represents the new item collection.</returns>
    </member>
    <member name="M:C1.Win.C1Input.C1ComboBox.OnSelectedItemChanged">
      <summary>
            Raises the <see cref="E:C1.Win.C1Input.C1ComboBox.SelectedItemChanged" /> event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1ComboBox.OnSelectedIndexChanged">
      <summary>
            Raises the <see cref="E:C1.Win.C1Input.C1ComboBox.SelectedIndexChanged" /> event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1ComboBox.Dispose(System.Boolean)">
      <summary>
            Releases the resources used by the C1ComboBox.
            </summary>
      <param name="disposing">
        <b>true</b> to release both managed and unmanaged resources; <b>false</b> to release only unmanaged resources.</param>
    </member>
    <member name="P:C1.Win.C1Input.C1ComboBox.AllowSpinLoop">
      <summary>
            Determines whether the spinner moves to the first item when it reaches the last, or to the last when it reaches the first.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1ComboBox.Items">
      <summary>
            Gets an object representing the collection of the items contained in this ComboBox.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1ComboBox.ItemMode">
      <summary>
            Gets or sets the combobox item mode.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1ComboBox.HtmlPattern">
      <summary>
            Gets or sets the pattern to build HTML representation of combobox items.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1ComboBox.SelectedItem">
      <summary>
             Gets or sets currently selected item in the ComboBox.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1ComboBox.SelectedIndex">
      <summary>
             Gets or sets the index specifying the currently selected item.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1ComboBox.ItemsImageList">
      <summary>
            Gets or sets the ImageList to get the images to display in the combobox items.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1ComboBox.Style">
      <summary>
            Gets the style of the drop down form and combobox items.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1ComboBox.InitialSelectedIndex">
      <summary>
            Gets or sets the index of the initially selected item.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1ComboBox.ItemsDataSource">
      <summary>
            Gets or sets the data source for items of this C1ComboBox.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1ComboBox.ItemsDisplayMember">
      <summary>
            Gets or sets the property to display items for this C1ComboBox control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1ComboBox.ItemsValueMember">
      <summary>
            Gets or sets the path of the property to use as the actual value for the items in the C1ComboBox control.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1ComboBox.SelectedItemChanged">
      <summary>
            Occurs when the <see cref="P:C1.Win.C1Input.C1ComboBox.SelectedItem" /> changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1ComboBox.SelectedIndexChanged">
      <summary>
            Occurs when the <see cref="E:C1.Win.C1Input.C1ComboBox.SelectedIndexChanged" /> changes.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.ComboItemMode">
      <summary>
            Specifies how the visual representation of combo box items is building.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.ComboItemMode.Default">
      <summary>
            C1ComboBox displays text of combo box item in the drop drown list.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.ComboItemMode.Html">
      <summary>
            Each combo box item is fragment of HTML. <see cref="T:C1.Win.C1Input.C1ComboBox" /> parses the HTML fragment and displays it as the drop down items.
            </summary>
      <remakrs>
            C1ComboBox uses the same subset of HTML as C1SuperLabel does. 
            It does not support the full HTML features.
            </remakrs>
    </member>
    <member name="F:C1.Win.C1Input.ComboItemMode.HtmlPattern">
      <summary>
            Each combo box item is fragment of HTML defined by <see cref="P:C1.Win.C1Input.C1ComboBox.HtmlPattern" /> property. 
            <see cref="T:C1.Win.C1Input.C1ComboBox" /> parses the HTML pattern and replaces any of "{Text}" entires with the combo box item text.
            </summary>
      <remakrs>
            C1ComboBox uses the same subset of HTML as C1SuperLabel does. 
            It does not support the full HTML features.
            </remakrs>
    </member>
    <member name="T:C1.Win.C1Input.DropDownStyle">
      <summary>
             Specifies the <see cref="T:C1.Win.C1Input.C1DropDownControl" /> style.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.DropDownStyle.Default">
      <summary>
            Text portion of the drop down control is editable and the drop down is displayed by clicking 
            the down arrow.
            </summary>
      <remarks>
            This means that the user can enter a new value and is not limited to selecting an existing value.
            </remarks>
    </member>
    <member name="F:C1.Win.C1Input.DropDownStyle.DropDownList">
      <summary>
            The drop down of the <see cref="T:C1.Win.C1Input.C1DropDownControl" /> is displayed by clicking 
            on either the text portion of the control or drop down button. The text portion is not editable. 
            </summary>
      <remarks>
            This means that the user cannot enter a value not existing in the drop down. 
            </remarks>
    </member>
    <member name="T:C1.Win.C1Input.ComboBoxItemList">
      <summary>
            Represents collection of <see cref="T:C1.Win.C1Input.C1ComboBox" /> drop down items.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.ComboBoxStyle">
      <summary>
             Style for drop down form and combobox items.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.ComboBoxStyle.Reset">
      <summary>
            Resets the style to default value.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ComboBoxStyle.DropDownBackColor">
      <summary>
            Background color of the drop down form.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ComboBoxStyle.DropDownBorderColor">
      <summary>
            Color of border of the drop down form.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ComboBoxStyle.Padding">
      <summary>
            Gets or sets padding within the drop down form.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ComboBoxStyle.Font">
      <summary>
            Default font of the combo box items.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ComboBoxStyle.TextSpacing">
      <summary>
            Space around the textual parts of the combo box items.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ComboBoxStyle.DefaultItemForeColor">
      <summary>
            Default text color of the combo box items.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ComboBoxStyle.HotItemForeColor">
      <summary>
            Text color of the combo box items in hot state.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ComboBoxStyle.HotItemBorderColor">
      <summary>
            Border color of the combo box items in hot state.
             </summary>
    </member>
    <member name="P:C1.Win.C1Input.ComboBoxStyle.HotItemBackColor">
      <summary>
            Background color of the combo box items in hot state.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.DropDownControlButtonFlags">
      <summary>
            Used by C1DropDownControl.VisibleButtons properties.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.DropDownControlButtonFlags.None">
      <summary>
            Show without any button
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.DropDownControlButtonFlags.UpDown">
      <summary>
            Show up/down buttons.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.DropDownControlButtonFlags.DropDown">
      <summary>
            Show dropdown button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.DropDownControlButtonFlags.Modal">
      <summary>
            Show button to start a modal dialog.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.DropDownControlButtonFlags.Custom">
      <summary>
            Show a custom button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.DropDownControlButtonFlags.All">
      <summary>
            Show all buttons
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.UpDownButtonClickEventHandler">
      <summary>
            Represents the method that handles a UpDownButtonClick event.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">An UpDownButtonClickEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Input.UpDownButtonClickEventArgs">
      <summary>
            Provides data for a UpDownButtonClick event.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.UpDownButtonClickEventArgs.Delta">
      <summary>
            Set to 1 if Up button is pressed, to -1 if Down button is pressed.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.UpDownButtonClickEventArgs.Done">
      <summary>
            Set to true in the user's UpDownButtonClick event handler. No built-in processing is done. This allows the user to override the default behavior of the buttons.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.DropDownClosedEventArgs">
      <summary>
            Provides data for a DropDownClosed event.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.DropDownClosedEventArgs.Selected">
      <summary>
            Returns True if a value is selected in dropdown dialog.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DropDownClosedEventArgs.#ctor(System.Boolean)">
      <summary>
            Initializes a new instance of the DropDownClosedEventArgs class.
            </summary>
      <param name="selected">
      </param>
    </member>
    <member name="T:C1.Win.C1Input.DropDownClosedEventHandler">
      <summary>
            Represents the method that handles a DropDownClosed event.
            </summary>
      <param name="sender">The source of the event</param>
      <param name="e">A DropDownClosedEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Input.ButtonImages">
      <summary>
            Images for C1DropDownControl buttons
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.ButtonImages.Reset">
      <summary>
            Clears all custom images and returns standard images for C1DropDownControl buttons. 
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.ButtonImages.HasImage">
      <summary>
            Returns True if any custom image is assigned
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.ButtonImages.ShouldSerializeUpImage">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.ButtonImages.ResetUpImage">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.ButtonImages.ShouldSerializeDownImage">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.ButtonImages.ResetDownImage">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.ButtonImages.ShouldSerializeDropImage">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.ButtonImages.ResetDropImage">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.ButtonImages.ShouldSerializeModalImage">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.ButtonImages.ResetModalImage">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.ButtonImages.ShouldSerializeCustomImage">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.ButtonImages.ResetCustomImage">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ButtonImages.UpImage">
      <summary>
            Image for Up button
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ButtonImages.DownImage">
      <summary>
            Image for Down button
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ButtonImages.DropImage">
      <summary>
            Image for DropDown button
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ButtonImages.ModalImage">
      <summary>
            Image for Modal button
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ButtonImages.CustomImage">
      <summary>
            Image for Custom button
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.ButtonImages.ButtonImagesTypeConverter">
      <summary>
            TypeConverter class for ButtonImages
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.ButtonImages.ButtonImagesTypeConverter.GetPropertiesSupported(System.ComponentModel.ITypeDescriptorContext)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="context">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.ButtonImages.ButtonImagesTypeConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="context">
      </param>
      <param name="culture">
      </param>
      <param name="value">
      </param>
      <param name="type">
      </param>
      <returns>
      </returns>
    </member>
    <member name="P:C1.Win.C1Input.ButtonImages.ButtonImagesTypeConverter.Text">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.DropDownFormOptionsFlags">
      <summary>
            Used by DropDownForm.Options property.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.DropDownFormOptionsFlags.None">
      <summary>
            Indicates that none of the options are set.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.DropDownFormOptionsFlags.Focusable">
      <summary>
            Indicates that DropDownForm can contain focus.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.DropDownFormOptionsFlags.FixedWidth">
      <summary>
            The end users are not allowed to change the width of the dropdown form resizing it.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.DropDownFormOptionsFlags.FixedHeight">
      <summary>
            The end users are not allowed to change the height of the dropdown form resizing it.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.DropDownFormOptionsFlags.AlwaysPostChanges">
      <summary>
            If this flag is set, the changes to the control Value can only be cancelled explicitly, with Esc key 
            or using the CloseDropDown method, and implicit cancellation (when the form is closed because it loses focus) is disabled.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.DropDownFormOptionsFlags.NoCancelOnEscape">
      <summary>
            If this flag is set, pressing the Esc key does not close the form.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.DropDownFormOptionsFlags.NoPostOnEnter">
      <summary>
            If this flag is set, pressing the Enter key does not close the form.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.DropDownFormOptionsFlags.AutoResize">
      <summary>
            If set, the dropdown form width is automatically adjusted to the width of the owner control
            before the form is shown.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.DropDownFormOptionsFlags.FixedSize">
      <summary>
            = FixedWidth + FixedHeight
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.C1FontPicker">
      <summary>
            Represents a Windows font picker control supporting additional visual styles.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1FontPicker.#ctor">
      <summary>
            Initializes a new instance of the C1FontPicker class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1FontPicker.TextToValue(C1.Win.C1Input.C1BasePicker.TextToValueArgs@)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1FontPicker.ValueToText(C1.Win.C1Input.C1BasePicker.ValueToTextArgs@)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1FontPicker.OnKeyDown(System.Windows.Forms.KeyEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="P:C1.Win.C1Input.C1FontPicker.UsedFonts">
      <summary>
            Gets a collection of fonts installed on system.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1FontPicker.DropDownFormWidth">
      <summary>
            Gets or sets a width of drop down list of fonts.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1FontPicker.DefaultDropDownFormClassName">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1FontPicker.VisibleButtons">
      <summary>
            Gets or sets a value specifying which buttons are visible. The default is (UpDown Or DropDown).
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1FontPicker.DataType">
      <summary>
            Type of the Value property.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1FontPicker.DefaultThemeRootPath">
      <summary>
            Default path for the control in xml theme file.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.C1FontPickerDropDownForm">
      <summary>
            Represents drop down font picker form.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1FontPickerDropDownForm.#ctor">
      <summary>
            Initializes a new instance of the C1FontPickerDropDownForm class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1FontPickerDropDownForm.OnPostChanges(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1FontPickerDropDownForm.OnVisibleChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1FontPickerDropDownForm.Dispose(System.Boolean)">
      <summary>
            Clean up any resources being used.
            </summary>
      <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
    </member>
    <member name="T:C1.Win.C1Input.C1Label">
      <summary>
            Read-only data bound control displaying formatted data. C1Label supports all formatting features of the C1TextBox control.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1Label.#ctor">
      <summary>
            Initializes a new instance of the C1Label class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1Label.Dispose(System.Boolean)">
      <summary>
            Releases the resources used by the C1Label.
            </summary>
      <param name="disposing">
        <b>true</b> to release both managed and unmanaged resources; <b>false</b> to release only unmanaged resources.</param>
    </member>
    <member name="M:C1.Win.C1Input.C1Label.OnBorderColorChanged(System.EventArgs)">
      <summary>
            Raises the <see cref="E:C1.Win.C1Input.C1Label.BorderColorChanged" /> event.
            </summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
      <remarks>
        <para>Raising an event invokes the event handler through a delegate. </para>
        <para>The <b>OnBorderColorChanged</b> method also allows derived classes to handle the event without attaching a delegate. 
            This is the preferred technique for handling the event in a derived class.</para>
        <para>
          <b>Notes to Inheritors.</b> When overriding <b>OnBorderColorChanged</b> in a derived class, 
            be sure to call the base class's <b>OnBorderColorChanged</b> method so that registered delegates receive the event. 
            </para>
      </remarks>
    </member>
    <member name="M:C1.Win.C1Input.C1Label.OnFormatError(C1.Win.C1Input.FormatErrorEventArgs)">
      <summary>
            Invokes the <see cref="E:C1.Win.C1Input.C1Label.FormatError" /> event.
            </summary>
      <param name="e">An <see cref="T:C1.Win.C1Input.FormatErrorEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:C1.Win.C1Input.C1Label.OnVisualStyleChanged(System.EventArgs)">
      <summary>
            Invokes the VisualStyleChanged event.
            </summary>
      <param name="e">The event arguments.</param>
    </member>
    <member name="M:C1.Win.C1Input.C1Label.OnCultureInfoSetup(C1.Win.C1Input.CultureInfoSetupEventArgs)">
      <summary>
            Invokes the <see cref="E:C1.Win.C1Input.C1Label.CultureInfoSetup" /> event.
            </summary>
      <param name="e">An <see cref="T:C1.Win.C1Input.CultureInfoSetupEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:C1.Win.C1Input.C1Label.OnFormatting(C1.Win.C1Input.FormatEventArgs)">
      <summary>
            Invokes the <see cref="E:C1.Win.C1Input.C1Label.Formatting" /> event.
            </summary>
      <param name="e">An <see cref="T:C1.Win.C1Input.FormatEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:C1.Win.C1Input.C1Label.OnFormatted(C1.Win.C1Input.FormatEventArgs)">
      <summary>
            Invokes the <see cref="E:C1.Win.C1Input.C1Label.Formatted" /> event.
            </summary>
      <param name="e">An <see cref="T:C1.Win.C1Input.FormatEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:C1.Win.C1Input.C1Label.OnDataTypeChanged(System.EventArgs)">
      <summary>
            Invokes the <see cref="E:C1.Win.C1Input.C1Label.DataTypeChanged" /> event.
            </summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:C1.Win.C1Input.C1Label.OnValueChanged(System.EventArgs)">
      <summary>
            Invokes the <see cref="E:C1.Win.C1Input.C1Label.ValueChanged" /> event.
            </summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:C1.Win.C1Input.C1Label.OnBindingFormatting(System.Windows.Forms.ConvertEventArgs)">
      <summary>
            Invokes the <see cref="E:C1.Win.C1Input.C1Label.BindingFormatting" /> event.
            </summary>
      <param name="e">An <see cref="T:System.Windows.Forms.ConvertEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:C1.Win.C1Input.C1Label.GetPreferredSize(System.Drawing.Size)">
      <summary>
            Retrieves the size of a rectangular area into which a control can be fitted.
            </summary>
      <param name="proposedSize">The custom-sized area for a control.</param>
      <returns>An ordered pair of type <see cref="T:System.Drawing.Size" /> representing the width and height of a rectangle.</returns>
    </member>
    <member name="P:C1.Win.C1Input.C1Label.VisualStyle">
      <summary>
            Gets or sets the visual style of the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1Label.BorderColor">
      <summary>
            Gets or sets the border color of the control.
            </summary>
      <remarks>
        <para>Use the <b>BorderColor</b> property to specify the border color of the control. 
            This property is set using a <see cref="T:System.Drawing.Color" /> object.</para>
        <para>The <b>BorderColor</b> property has an effect only when the <see cref="P:C1.Win.C1Input.C1TextBox.BorderStyle" /> property is set to <b>FixedSingle</b>. </para>
      </remarks>
    </member>
    <member name="P:C1.Win.C1Input.C1Label.UseFieldStyle">
      <summary>
            Indicates whether to honor the ColumnStyle properties defined on the
            C1ViewColumn object that this control is bound to.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1Label.Culture">
      <summary>
            The culture ID.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1Label.CultureName">
      <summary>
            The name selected for the Culture property in the Properties grid.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1Label.UserCultureOverride">
      <summary>
            If True, current user regional settings override the selected culture.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1Label.CultureInfo">
      <summary>
            The current CultureInfo object (available at runtime).
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1Label.FormatInfo">
      <summary>
            Format used to display the value.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1Label.MaskInfo">
      <summary>
            Edit mask settings.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1Label.CaseSensitive">
      <summary>
            True if string comparisons are case-sensitive; otherwise, False.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1Label.FormatType">
      <summary>
            Enumeration value determining the formatting metod, including standard .NET format specifiers, custom and programmatic formatting.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1Label.CustomFormat">
      <summary>
            Custom format specifier used if FormatType = FormatTypeEnum.CustomFormat.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1Label.NullText">
      <summary>
            String representing a DbNull value.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1Label.EmptyAsNull">
      <summary>
            If True, empty strings are interpreted as null values (DbNull).
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1Label.TrimStart">
      <summary>
            If True, leading spaces are removed.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1Label.TrimEnd">
      <summary>
            If True, trailing spaces are removed.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1Label.CurrentTimeZone">
      <summary>
            If True, the control’s stored Value is in current time zone; otherwise, the Value is adjusted to the time zone defined by the GMTOffset property.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1Label.GMTOffset">
      <summary>
            The time zone of the Value property used if CurrentTimeZone = False.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1Label.DaylightTimeAdjustment">
      <summary>
            The value specified in the GMTOffset property can be advanced or set back due to daylight-saving time changes (used only if CurrentTimeZone = False). 
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1Label.Tag">
      <summary>
            Gets or sets the object that contains data about the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1Label.ErrorProvider">
      <summary>
            Gets or sets an ErrorProvider object used to indicate error state of the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1Label.Text">
      <summary>
            The current text in the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1Label.TextDetached">
      <summary>
            Specifies whether the Value and Text properties can be set independently.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1Label.DataSource">
      <summary>
            The data source object to which the control is bound.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1Label.DataField">
      <summary>
            The field of the data source object to which the control is bound.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1Label.DataType">
      <summary>
            Type of the Value property.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1Label.ValueIsDbNull">
      <summary>
            Gets or sets a value indicating whether the Value is DbNull.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1Label.Value">
      <summary>
            The main bindable property of a C1Input control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1Label.Padding">
      <summary>
            Gets or sets the padding within the control.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1Label.BorderColorChanged">
      <summary>
            Occurs when the BorderColor property has changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1Label.VisualStyleChanged">
      <summary>
            Occurs when the VisualStyle property has changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1Label.CultureInfoSetup">
      <summary>
            CultureInfo can be set up (on startup) or has been changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1Label.FormatError">
      <summary>
            Error occured while formatting the stored value.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1Label.Formatting">
      <summary>
            Formatting the value programmatically.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1Label.Formatted">
      <summary>
            Occurs after the value has been formatted.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1Label.DataTypeChanged">
      <summary>
            Occurs when the DataType property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1Label.ValueChanged">
      <summary>
            Occurs when the Value changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1Label.BindingFormatting">
      <summary>
            Occurs when Value is retrieved from the data source.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1Label.DefaultThemeRootPath">
      <summary>
            Default path for the control in xml theme file.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.Localization.Strings">
      <summary>
            The <see cref="T:C1.Win.C1Input.Localization.Strings" /> class contains static properties used for localization.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.Localization.Strings.ResourceManager">
      <summary>
            The <see cref="P:C1.Win.C1Input.Localization.Strings.ResourceManager" /> object used for lookup resources.
            </summary>
      <remarks>Set this property to the valid value if 
            you define resources in a Custom Control assembly.</remarks>
    </member>
    <member name="P:C1.Win.C1Input.Localization.Strings.UICulture">
      <summary>
            Returns default <see cref="T:System.Globalization.CultureInfo" /> object used as fallback culture.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.NumericEditCalculator">
      <summary>
            The calculator used as a dropdown in the C1NumericEdit control.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.NumericEditCalculator.#ctor">
      <summary>
            Initializes a new instance of the NumericEditCalculator class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.NumericEditCalculator.Dispose(System.Boolean)">
      <summary>
            Releases the resources used by the NumericEditCalculator.
            </summary>
      <param name="disposing">
        <b>true</b> to release both managed and unmanaged resources; <b>false</b> to release only unmanaged resources.</param>
    </member>
    <member name="M:C1.Win.C1Input.NumericEditCalculator.ResetVisualStyle">
      <summary>
            Resets <see cref="P:C1.Win.C1Input.NumericEditCalculator.VisualStyle" /> to the default value.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.NumericEditCalculator.ShouldSerializeVisualStyle">
      <summary>
            Tests whether <see cref="P:C1.Win.C1Input.NumericEditCalculator.VisualStyle" /> should be serialized.
            </summary>
      <returns>True if <see cref="P:C1.Win.C1Input.NumericEditCalculator.VisualStyle" /> should be serialized, false otherwise.</returns>
    </member>
    <member name="M:C1.Win.C1Input.NumericEditCalculator.OnVisualStyleChanged(System.EventArgs)">
      <summary>
            Invokes the VisualStyleChanged event.
            </summary>
      <param name="e">The event arguments.</param>
    </member>
    <member name="M:C1.Win.C1Input.NumericEditCalculator.ShouldSerializeUIStrings">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.NumericEditCalculator.ResetUIStrings">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.NumericEditCalculator.OnPaint(System.Windows.Forms.PaintEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.NumericEditCalculator.OnResize(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.NumericEditCalculator.OnForeColorChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.NumericEditCalculator.ThemeChanged(System.Object,C1.Win.C1Input.VisualStyle)">
      <summary>
            This method called when theme is changed.
            </summary>
      <param name="theme">New theme.</param>
      <param name="visualStyle">Base visual style.</param>
    </member>
    <member name="P:C1.Win.C1Input.NumericEditCalculator.VisualStyle">
      <summary>
            Gets or sets the visual style of the control.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.NumericEditCalculator.VisualStyleChanged">
      <summary>
            Occurs when the VisualStyle property has changed.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.NumericEditCalculator.ButtonFlatStyle">
      <summary>
            Determines the style of the dropdown calculator buttons.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.NumericEditCalculator.DisplayFormat">
      <summary>
            // patch: localization: added after StringTables.cs was created
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.NumericEditCalculator.StoredFormat">
      <summary>
            Custom format specifier used to format the stored value of the dropdown calculator.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.NumericEditCalculator.FormatOnClose">
      <summary>
            Gets or sets whether StoredFormat should be applied to the value entered in the dropdown calculator.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.NumericEditCalculator.RightToLeft">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.NumericEditCalculator.UIStrings">
      <summary>
            The collection of user interface strings.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.NumericEditCalculator.ContextMenu">
      <summary>
            Gets or sets the shortcut menu associated with the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.NumericEditCalculator.UseStaticMemory">
      <summary>
            Gets or sets whether Static Memory context menu should appear for the dropdown calculator.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.NumericEditCalculator.DefaultSize">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.NumericEditCalculator.ShowFocusCues">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.C1DateEdit">
      <summary>
            Displays and edits date and/or time values. Includes a dropdown calendar for easy input of datetime values.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1DateEdit.#ctor">
      <summary>
            Initializes a new instance of the C1DateEdit class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1DateEdit.Dispose(System.Boolean)">
      <summary>
            Releases the resources used by the C1DateEdit.
            </summary>
      <param name="disposing">
        <b>true</b> to release both managed and unmanaged resources; <b>false</b> to release only unmanaged resources.</param>
    </member>
    <member name="M:C1.Win.C1Input.C1DateEdit.ShouldSerializeCalendar">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.C1DateEdit.OnUpDownButtonClick(C1.Win.C1Input.UpDownButtonClickEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DateEdit.UpdateDataType(System.Type)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="propertyType">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DateEdit.SpinUp">
      <summary>
              Performs spin up.
            </summary>
      <remarks>
              This method has the same behavior with the Up key pressed when day field selected and increments value by one day.
            </remarks>
      <seealso cref="M:C1.Win.C1Input.C1DateEdit.SpinDown" />
    </member>
    <member name="M:C1.Win.C1Input.C1DateEdit.SpinUp(System.TimeSpan)">
      <summary>
              Performs spin up.
            </summary>
      <param name="increment">
              The <see cref="T:System.TimeSpan" /> value that indicates the increment when spin up.
            </param>
      <remarks>
              This method has the same behavior with the Up key pressed.
            </remarks>
      <seealso cref="M:C1.Win.C1Input.C1DateEdit.SpinDown(System.TimeSpan)" />
    </member>
    <member name="M:C1.Win.C1Input.C1DateEdit.SpinDown">
      <summary>
              Performs spin down.
            </summary>
      <remarks>
              This method has the same behavior with the Down key pressed when day field selected and decrements value by one day.
            </remarks>
      <seealso cref="M:C1.Win.C1Input.C1DateEdit.SpinUp" />
    </member>
    <member name="M:C1.Win.C1Input.C1DateEdit.SpinDown(System.TimeSpan)">
      <summary>
              Performs spin down.
            </summary>
      <param name="decrement">
              The <see cref="T:System.TimeSpan" /> value that indicates the decrement when spin down.
            </param>
      <remarks>
              This method has the same behavior with the Down key pressed.
            </remarks>
      <seealso cref="M:C1.Win.C1Input.C1DateEdit.SpinUp(System.TimeSpan)" />
    </member>
    <member name="P:C1.Win.C1Input.C1DateEdit.AllowSpinLoop">
      <summary>
            Determines whether the spinner moves to the <see cref="P:C1.Win.C1Input.DateEditMonthCalendar.MinDate" /> when it reaches the <see cref="P:C1.Win.C1Input.DateEditMonthCalendar.MaxDate" />, or to the <see cref="P:C1.Win.C1Input.DateEditMonthCalendar.MaxDate" /> when it reaches the <see cref="P:C1.Win.C1Input.DateEditMonthCalendar.MinDate" />.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DateEdit.RetainTimeOnDateChange">
      <summary>
            Gets or sets a value indicating whether to retain the current time value when the date is changed via the drop down calendar.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DateEdit.DefaultDropDownFormClassName">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DateEdit.TypeRestriction">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DateEdit.Calendar">
      <summary>
            The dropdown calendar object.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DateEdit.FormatType">
      <summary>
            Enumeration value determining the formatting metod, including standard .NET format specifiers, custom and programmatic formatting.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DateEdit.Text">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.C1DbNavigator">
      <summary>
            Data bound control providing buttons for convenient navigation over data source rows, moving to the first,
            last, previous and next row and performing common data actions such as updating the data source and refreshing data.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1DbNavigator.#ctor">
      <summary>
            Initializes a new instance of the C1DbNavigator class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1DbNavigator.Dispose(System.Boolean)">
      <summary>
            Releases the resources used by the C1DbNavigator.
            </summary>
      <param name="disposing">
        <b>true</b> to release both managed and unmanaged resources; <b>false</b> to release only unmanaged resources.</param>
    </member>
    <member name="M:C1.Win.C1Input.C1DbNavigator.BeginInit">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1DbNavigator.EndInit">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1DbNavigator.OnTextChanged(System.EventArgs)">
      <summary>
            Raises the <see cref="E:C1.Win.C1Input.C1DbNavigator.TextChanged">TextChanged</see> event. 
            </summary>
      <param name="e">An EventArgs that contains event data.</param>
    </member>
    <member name="M:C1.Win.C1Input.C1DbNavigator.ResetVisualStyle">
      <summary>
            Resets <see cref="P:C1.Win.C1Input.C1DbNavigator.VisualStyle" /> to the default value.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1DbNavigator.ShouldSerializeVisualStyle">
      <summary>
            Tests whether <see cref="P:C1.Win.C1Input.C1DbNavigator.VisualStyle" /> should be serialized.
            </summary>
      <returns>True if <see cref="P:C1.Win.C1Input.C1DbNavigator.VisualStyle" /> should be serialized, false otherwise.</returns>
    </member>
    <member name="M:C1.Win.C1Input.C1DbNavigator.ResetVisualStyleBaseStyle">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1DbNavigator.ShouldSerializeVisualStyleBaseStyle">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>True if VisualStyleBaseStyle should be serialized, false otherwise.</returns>
    </member>
    <member name="M:C1.Win.C1Input.C1DbNavigator.OnBackColorChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DbNavigator.OnForeColorChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DbNavigator.ShouldSerializeButtonSize">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.C1DbNavigator.ResetButtonSize">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1DbNavigator.ShouldSerializeButtonTexts">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.C1DbNavigator.ResetButtonTexts">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1DbNavigator.ShouldSerializeButtonToolTips">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.C1DbNavigator.ResetButtonToolTips">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1DbNavigator.ShouldSerializeUIStrings">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.C1DbNavigator.ResetUIStrings">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1DbNavigator.ShouldSerializeButtonCursor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.C1DbNavigator.ResetButtonCursor">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1DbNavigator.OnRightToLeftChanged(System.EventArgs)">
      <summary>
            Raises <see cref="E:C1.Win.C1Input.C1DbNavigator.RightToLeftChanged" /> event.
            </summary>
      <param name="e">The event data object.</param>
    </member>
    <member name="M:C1.Win.C1Input.C1DbNavigator.ClickButton(C1.Win.C1Input.NavigatorButtonEnum)">
      <summary>
            Performs the action associated with a navigator button.
            </summary>
      <param name="button">The button whose action is performed.</param>
    </member>
    <member name="M:C1.Win.C1Input.C1DbNavigator.SetDataBinding(System.Object,System.String)">
      <summary>
            Sets the DataSource and DataMember properties at run time.
            </summary>
      <param name="dataSource">The data source object to which the control is bound.</param>
      <param name="dataMember">For multi-table data sources, specifies a specific table to which the control is bound.</param>
    </member>
    <member name="M:C1.Win.C1Input.C1DbNavigator.WndProc(System.Windows.Forms.Message@)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="m">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DbNavigator.OnVisualStyleChanged(System.EventArgs)">
      <summary>
            Invokes the VisualStyleChanged event.
            </summary>
      <param name="e">The event arguments.</param>
    </member>
    <member name="M:C1.Win.C1Input.C1DbNavigator.GetPreferredSize(System.Drawing.Size)">
      <summary>
            Retrieves the size of a rectangular area into which a control can be fitted.
            </summary>
      <param name="proposedSize">The custom-sized area for a control.</param>
      <returns>An ordered pair of type <see cref="T:System.Drawing.Size" /> representing the width and height of a rectangle.</returns>
    </member>
    <member name="M:C1.Win.C1Input.C1DbNavigator.OnPositionChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DbNavigator.OnItemChanged(System.Windows.Forms.ItemChangedEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DbNavigator.OnBeforeAction(C1.Win.C1Input.NavigatorBeforeActionEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DbNavigator.OnAdding(C1.Win.C1Input.NavigatorAddingEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DbNavigator.OnDeleting(C1.Win.C1Input.NavigatorDeletingEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DbNavigator.OnEditing(C1.Win.C1Input.NavigatorEditingEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DbNavigator.OnUpdateData(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DbNavigator.OnRefreshData(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DbNavigator.OnButtonClick(C1.Win.C1Input.NavigatorButtonClickEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DbNavigator.OnError(C1.Win.C1Input.NavigatorErrorEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DbNavigator.OnButtonCursorChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1DbNavigator.OnEnabledChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="P:C1.Win.C1Input.C1DbNavigator.Text">
      <summary>
            Gets or sets value of the Position textbox.
            </summary>
      <remarks>
        <para>If the Position textbox is not visible, it returns empty string.</para>
        <para>If you set the Text property when the Position textbox is not visible, 
            the action has no effect.</para>
        <para>Changing the Text property causes the data source position change.</para>
      </remarks>
    </member>
    <member name="E:C1.Win.C1Input.C1DbNavigator.TextChanged">
      <summary>
            Occurs when the <see cref="P:C1.Win.C1Input.C1DbNavigator.Text">Text</see> property value changes.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DbNavigator.VisualStyle">
      <summary>
            Gets or sets the visual style of the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DbNavigator.AutoSize">
      <summary>
            Specifies if navigator buttons have color bitmaps.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DbNavigator.ColorButtons">
      <summary>
            Specifies if navigator buttons have color bitmaps.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DbNavigator.ColorWhenHover">
      <summary>
            If True, navigator buttons show color bitmaps when the mouse hovers over them.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DbNavigator.BorderStyle">
      <summary>
            Navigator border style.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DbNavigator.ButtonStyle">
      <summary>
            Navigator button style.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DbNavigator.VerticalAlign">
      <summary>
            Vertical alignment of navigator buttons.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DbNavigator.VerticalIndent">
      <summary>
            Vertical margin between the border and the buttons.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DbNavigator.HorizontalIndent">
      <summary>
            Horizontal margin between the border and the buttons.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DbNavigator.ImageList">
      <summary>
            Gets or sets the ImageList to use when displaying button images without highlighting.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DbNavigator.ImageListHighlight">
      <summary>
            Gets or sets the ImageList to use when displaying button images in highlighted state.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DbNavigator.ShowToolTips">
      <summary>
            Indicates whether tooltips are shown for the buttons.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DbNavigator.ForeColor">
      <summary>
            This member overrides Control.ForeColor.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DbNavigator.MoveDelayFirst">
      <summary>
            Time delay in milliseconds after pressing Next/Previous button before automatic scrolling begins.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DbNavigator.MoveDelayNext">
      <summary>
            Time delay in milliseconds between row moves in automatic scrolling.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DbNavigator.PageSize">
      <summary>
            Number of rows to skip when the user presses PageUp/Down.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DbNavigator.PositionVisible">
      <summary>
            Specifies whether the editable row position field and row captions are visible in the navigator.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DbNavigator.TabStop">
      <summary>
            This member overrides Control.TabStop.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DbNavigator.VisibleButtons">
      <summary>
            Flags enumeration specifying which buttons are visible.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DbNavigator.EnabledButtons">
      <summary>
            Flags enumeration specifying which buttons are enabled.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DbNavigator.ConfirmDelete">
      <summary>
            Whether a confirmation dialog is shown before deleting a record.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DbNavigator.CausesFormValidation">
      <summary>
            Whether the control in focus should be validated before a button click is handled by the navigator.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DbNavigator.DataSource">
      <summary>
            The data source object to which the control is bound.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DbNavigator.DataMember">
      <summary>
            "For multi-table data sources, specifies a specific table to which the control is bound.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DbNavigator.RowCount">
      <summary>
            Returns the number of rows in the table.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DbNavigator.Position">
      <summary>
            Gets or sets the current row position in the table (from 0 to RowCount - 1).
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DbNavigator.CurrencyManager">
      <summary>
            Gets or sets the CurrencyManager object navigated by the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DbNavigator.ButtonSize">
      <summary>
            The size of navigator buttons.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DbNavigator.ButtonTextAlign">
      <summary>
            Controls how the text is positioned relative to the image in navigator buttons.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DbNavigator.ButtonTexts">
      <summary>
            Gets or sets the texts displayed on the buttons.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DbNavigator.ButtonToolTips">
      <summary>
            The string collection defining navigator button tooltips.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DbNavigator.UIStrings">
      <summary>
            Gets the collection of user interface strings.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DbNavigator.ErrorProvider">
      <summary>
            Gets or sets an ErrorProvider object used to indicate error state of the current data row.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1DbNavigator.ButtonCursor">
      <summary>
            The cursor that is displayed when the mouse is over the navigator buttons.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1DbNavigator.VisualStyleChanged">
      <summary>
            Occurs when the VisualStyle property has changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1DbNavigator.PositionChanged">
      <summary>
            Occurs when the Position has changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1DbNavigator.ItemChanged">
      <summary>
            Occurs when the current row has been modified, some of its fields changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1DbNavigator.BeforeAction">
      <summary>
            Occurs when a button is clicked, before the action is executed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1DbNavigator.Adding">
      <summary>
            Occurs when Add button is pressed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1DbNavigator.Deleting">
      <summary>
            Occurs when Delete button is pressed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1DbNavigator.Editing">
      <summary>
            Occurs when Edit button is pressed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1DbNavigator.UpdateData">
      <summary>
            Occurs when Update button is pressed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1DbNavigator.RefreshData">
      <summary>
            Occurs when Refresh button is pressed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1DbNavigator.ButtonClick">
      <summary>
            Occurs when a navigator button has been pressed, after the button action is performed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1DbNavigator.Error">
      <summary>
            Occurs when an exceptions is thrown performing an action on button click.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1DbNavigator.ButtonCursorChanged">
      <summary>
            Event fired when the value of ButtonCursor property is changed.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.NavigatorButtonEnum">
      <summary>
            A list of all available navigator buttons
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NavigatorButtonEnum.First">
      <summary>
            First button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NavigatorButtonEnum.Previous">
      <summary>
            Previous button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NavigatorButtonEnum.Next">
      <summary>
            Next button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NavigatorButtonEnum.Last">
      <summary>
            Last button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NavigatorButtonEnum.Add">
      <summary>
            Add button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NavigatorButtonEnum.Delete">
      <summary>
            Delete button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NavigatorButtonEnum.Edit">
      <summary>
            Edit button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NavigatorButtonEnum.Apply">
      <summary>
            Apply button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NavigatorButtonEnum.Cancel">
      <summary>
            Cancel button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NavigatorButtonEnum.Update">
      <summary>
            Update button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NavigatorButtonEnum.Refresh">
      <summary>
            Refresh button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NavigatorButtonEnum.Position">
      <summary>
            Position text box
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.NavigatorBeforeActionEventHandler">
      <summary>
            Represents the method that handles a BeforeAction event.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A NavigatorBeforeActionEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Input.NavigatorBeforeActionEventArgs">
      <summary>
            Provides data for a BeforeAction event.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.NavigatorBeforeActionEventArgs.Button">
      <summary>
            The clicked button.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.NavigatorBeforeActionEventArgs.Index">
      <summary>
            Current row index in the data source (only for Delete and Edit buttons).
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.NavigatorBeforeActionEventArgs.Cancel">
      <summary>
            This argument is False by default. If you set it to True in your event code, the navigator control will skip the standard action associated with the button. 
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.NavigatorAddingEventHandler">
      <summary>
            Represents the method that handles an Adding event.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A NavigatorAddingEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Input.NavigatorAddingEventArgs">
      <summary>
            Provides data for a Adding event.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.NavigatorAddingEventArgs.Cancel">
      <summary>
            This argument is False by default. If you set it to True in your event code, the navigator control will abort adding a new row. 
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.NavigatorDeletingEventHandler">
      <summary>
            Represents the method that handles a Deleting event.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A NavigatorDeletingEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Input.NavigatorDeletingEventArgs">
      <summary>
            Provides data for a Deleting event.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.NavigatorDeletingEventArgs.Index">
      <summary>
            The index of the row being deleted.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.NavigatorDeletingEventArgs.Cancel">
      <summary>
            This argument is False by default. If you set it to True in your event code, the navigator control will abort deleting. 
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.NavigatorEditingEventHandler">
      <summary>
            Represents the method that handles a Editing event.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A NavigatorEditingEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Input.NavigatorEditingEventArgs">
      <summary>
            Provides data for a Editing event.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.NavigatorEditingEventArgs.Index">
      <summary>
            The index of the row being edited.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.NavigatorButtonClickEventHandler">
      <summary>
            Represents the method that handles a ButtonClick event.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A NavigatorButtonClickEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Input.NavigatorButtonClickEventArgs">
      <summary>
            Provides data for a ButtonClick event.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.NavigatorButtonClickEventArgs.Button">
      <summary>
            The clicked button.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.NavigatorErrorEventHandler">
      <summary>
            Represents the method that handles an Error event.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A NavigatorErrorEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Input.NavigatorErrorEventArgs">
      <summary>
            Provides data for an Error event.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.NavigatorErrorEventArgs.Button">
      <summary>
            The clicked C1DbNavigator button.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.NavigatorErrorEventArgs.Exception">
      <summary>
            Exception that occurred while performing the button action.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.NavigatorErrorEventArgs.ShowErrorMessage">
      <summary>
            This argument is set to true by default. If it is set to False by event code, the standard message box is not shown.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.NavigatorErrorEventArgs.ErrorMessage">
      <summary>
            Error message shown in the standard message box.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.NavigatorErrorEventArgs.ErrorMessageCaption">
      <summary>
            The caption of the standard message box.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.NavigatorButtonStyleEnum">
      <summary>
            Used by C1DbNavigator.ButtonStyle property.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NavigatorButtonStyleEnum.Flat">
      <summary>
            Flat buttons showing "mouse hover".
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NavigatorButtonStyleEnum.Standard">
      <summary>
            Standard 3D-buttons.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.NavigatorVerticalAlignEnum">
      <summary>
            Used by C1DbNavigator.VerticalAlign property.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NavigatorVerticalAlignEnum.Top">
      <summary>
            Buttons are aligned with the top of the control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NavigatorVerticalAlignEnum.Middle">
      <summary>
            Buttons are aligned with the center of the control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NavigatorVerticalAlignEnum.Bottom">
      <summary>
            Buttons are aligned with the bottom of the control.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.NavigatorButtonTextAlignEnum">
      <summary>
            Used by C1DbNavigator.ButtonTextAlign property.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NavigatorButtonTextAlignEnum.Right">
      <summary>
            Text appears to the right of the image.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NavigatorButtonTextAlignEnum.Underneath">
      <summary>
            Text appears underneath the image.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.NavigatorButtonFlags">
      <summary>
            Used by C1DbNavigator.VisibleButtons property.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NavigatorButtonFlags.None">
      <summary>
            No buttons.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NavigatorButtonFlags.First">
      <summary>
            Moves to the first row.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NavigatorButtonFlags.Previous">
      <summary>
            Moves to the previous row.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NavigatorButtonFlags.Next">
      <summary>
            Moves to the next row.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NavigatorButtonFlags.Last">
      <summary>
            Moves to the last row.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NavigatorButtonFlags.Add">
      <summary>
            Adds a new row to the table.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NavigatorButtonFlags.Delete">
      <summary>
            Deletes the current row.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NavigatorButtonFlags.Edit">
      <summary>
            Fires the Editing event.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NavigatorButtonFlags.Apply">
      <summary>
            Ends edit mode for the current row. 
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NavigatorButtonFlags.Cancel">
      <summary>
            Cancels (reverts) modifications in the current row.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NavigatorButtonFlags.Update">
      <summary>
            Fires the UpdateData event.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NavigatorButtonFlags.Refresh">
      <summary>
            Fires the RefreshData event. 
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NavigatorButtonFlags.Navigation">
      <summary>
            First + Previous + Next + Last buttons
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NavigatorButtonFlags.All">
      <summary>
            All buttons.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.DateEditMonthCalendar">
      <summary>
            Represents drop down calendar.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.#ctor">
      <summary>
            Creates the instance of the <see cref="T:C1.Win.C1Input.DateEditMonthCalendar" /> class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.Dispose(System.Boolean)">
      <summary> 
            Clean up any resources being used.
            </summary>
      <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ResetVisualStyle">
      <summary>
            Resets <see cref="P:C1.Win.C1Input.DateEditMonthCalendar.VisualStyle" /> to the default value.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ShouldSerializeVisualStyle">
      <summary>
            Tests whether <see cref="P:C1.Win.C1Input.DateEditMonthCalendar.VisualStyle" /> should be serialized.
            </summary>
      <returns>True if <see cref="P:C1.Win.C1Input.DateEditMonthCalendar.VisualStyle" /> should be serialized, false otherwise.</returns>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ShouldSerializeTodayDate">
      <summary>
            Indicates whether the <see cref="P:C1.Win.C1Input.DateEditMonthCalendar.TodayDate" /> property should be persisted.
            </summary>
      <returns>
        <see langword="true" /> if the <see cref="P:C1.Win.C1Input.DateEditMonthCalendar.TodayDate" /> property value has been changed from its default; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ResetTodayDate">
      <summary>
            Sets the <see cref="P:C1.Win.C1Input.DateEditMonthCalendar.TodayDate" /> property to its default value.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ShouldSerializeMinDate">
      <summary>
            Indicates whether the <see cref="P:C1.Win.C1Input.DateEditMonthCalendar.MinDate" /> property should be persisted.
            </summary>
      <returns>
        <see langword="true" /> if the <see cref="P:C1.Win.C1Input.DateEditMonthCalendar.MinDate" /> property value has been changed from its default; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ResetMinDate">
      <summary>
            Sets the <see cref="P:C1.Win.C1Input.DateEditMonthCalendar.MinDate" /> property to its default value.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ShouldSerializeMaxDate">
      <summary>
            Indicates whether the <see cref="P:C1.Win.C1Input.DateEditMonthCalendar.MaxDate" /> property should be persisted.
            </summary>
      <returns>
        <see langword="true" /> if the <see cref="P:C1.Win.C1Input.DateEditMonthCalendar.MaxDate" /> property value has been changed from its default; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ResetMaxDate">
      <summary>
            Sets the <see cref="P:C1.Win.C1Input.DateEditMonthCalendar.MaxDate" /> property to its default value.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ShouldSerializeBackColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ResetBackColor">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ShouldSerializeForeColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ResetForeColor">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ShouldSerializeSelectionForeColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ResetSelectionForeColor">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ShouldSerializeSelectionBackColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ResetSelectionBackColor">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ShouldSerializeLineColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ResetLineColor">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ShouldSerializeArrowColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ResetArrowColor">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ShouldSerializeTodayBorderColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ResetTodayBorderColor">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ShouldSerializeTrailingForeColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ResetTrailingForeColor">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ShouldSerializeDayNamesColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ResetDayNamesColor">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ShouldSerializeTitleBackColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ResetTitleBackColor">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ShouldSerializeTitleForeColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ResetTitleForeColor">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ShouldSerializeSelectedDate">
      <summary>
            Indicates whether the <see cref="P:C1.Win.C1Input.DateEditMonthCalendar.SelectedDate" /> property should be persisted.
            </summary>
      <returns>
        <see langword="true" /> if the <see cref="P:C1.Win.C1Input.DateEditMonthCalendar.SelectedDate" /> property value has been changed from its default; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ResetSelectedDate">
      <summary>
            Sets the <see cref="P:C1.Win.C1Input.DateEditMonthCalendar.SelectedDate" /> property to its default value.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ShouldSerializeCalendarDimensions">
      <summary>
            Indicates whether the <see cref="P:C1.Win.C1Input.DateEditMonthCalendar.CalendarDimensions" /> property should be persisted.
            </summary>
      <returns>
        <see langword="true" /> if the <see cref="P:C1.Win.C1Input.DateEditMonthCalendar.CalendarDimensions" /> property value has been changed from its default; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ResetCalendarDimensions">
      <summary>
            Sets the <see cref="P:C1.Win.C1Input.DateEditMonthCalendar.CalendarDimensions" /> property to its default value.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ShouldSerializeDayNameLength">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ResetDayNameLength">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ShouldSerializeTitleHeight">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ResetTitleHeight">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ShouldSerializeTitleFont">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ResetTitleFont">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ShouldSerializeDayNamesFont">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ResetDayNamesFont">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ShouldSerializeBoldedDates">
      <summary>
            Indicates whether the <see cref="P:C1.Win.C1Input.DateEditMonthCalendar.BoldedDates" /> property should be persisted.
            </summary>
      <returns>
        <see langword="true" /> if the <see cref="P:C1.Win.C1Input.DateEditMonthCalendar.BoldedDates" /> property value has been changed from its default; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ResetBoldedDates">
      <summary>
            Clears <see cref="P:C1.Win.C1Input.DateEditMonthCalendar.BoldedDates" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ShouldSerializeAnnuallyBoldedDates">
      <summary>
            Indicates whether the <see cref="P:C1.Win.C1Input.DateEditMonthCalendar.AnnuallyBoldedDates" /> property should be persisted.
            </summary>
      <returns>
        <see langword="true" /> if the <see cref="P:C1.Win.C1Input.DateEditMonthCalendar.AnnuallyBoldedDates" /> property value has been changed from its default; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ResetAnnuallyBoldedDates">
      <summary>
            Clears <see cref="P:C1.Win.C1Input.DateEditMonthCalendar.AnnuallyBoldedDates" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ShouldSerializeMonthlyBoldedDates">
      <summary>
            Indicates whether the <see cref="P:C1.Win.C1Input.DateEditMonthCalendar.MonthlyBoldedDates" /> property should be persisted.
            </summary>
      <returns>
        <see langword="true" /> if the <see cref="P:C1.Win.C1Input.DateEditMonthCalendar.MonthlyBoldedDates" /> property value has been changed from its default; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ResetMonthlyBoldedDates">
      <summary>
            Clears <see cref="P:C1.Win.C1Input.DateEditMonthCalendar.MonthlyBoldedDates" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ShouldSerializeDisabledDates">
      <summary>
            Indicates whether the <see cref="P:C1.Win.C1Input.DateEditMonthCalendar.DisabledDates" /> property should be persisted.
            </summary>
      <returns>
        <see langword="true" /> if the <see cref="P:C1.Win.C1Input.DateEditMonthCalendar.DisabledDates" /> property value has been changed from its default; otherwise, <see langword="false" />.</returns>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ResetDisabledDates">
      <summary>
            Clears <see cref="M:C1.Win.C1Input.DateEditMonthCalendar.ResetDisabledDates" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.OnVisualStyleChanged(System.EventArgs)">
      <summary>
            Invokes the VisualStyleChanged event.
            </summary>
      <param name="e">The event arguments.</param>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.OnTodayButtonClick(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.OnClearButtonClick(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.OnTodayButtonVisibilityChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.OnClearButtonVisibilityChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.OnDateValueChanged(C1.Win.C1Input.NullableDateTimeEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.OnDateValueSelected(C1.Win.C1Input.NullableDateTimeEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.OnRightToLeftLayoutChanged(System.EventArgs)">
      <summary>
            Raises the <see cref="E:C1.Win.C1Input.DateEditMonthCalendar.RightToLeftLayoutChanged" /> event.
            </summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.OnMonthChanged(System.EventArgs)">
      <summary>
            Raises the <see cref="E:C1.Win.C1Input.DateEditMonthCalendar.MonthChanged" /> event.
            </summary>
      <param name="e">An <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ContainsDate(System.DateTime)">
      <summary>
      </summary>
      <param name="date">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.SuspendAllLayout">
      <summary>
            Calls SuspendLayout on all DateEditMonthCalendar's child controls.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.ResumeAllLayout(System.Boolean)">
      <summary>
            Calls ResumeLayout on all DateEditMonthCalendar's child controls.
            </summary>
      <param name="performLayout">Indicates whether to invoke the layout logic now.</param>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.AddAnnuallyBoldedDate(System.DateTime)">
      <summary>
            Adds a day that is displayed in bold on an annual basis in the month calendar.
            </summary>
      <param name="date">The date to be displayed in bold.</param>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.AddBoldedDate(System.DateTime)">
      <summary>
            Adds a day to be displayed in bold in the month calendar.
            </summary>
      <param name="date">The date to be displayed in bold.</param>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.AddDisabledDate(System.DateTime)">
      <summary>
            Adds a day to be disabled in the month calendar.
            </summary>
      <param name="date">The date to be disabled.</param>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.AddMonthlyBoldedDate(System.DateTime)">
      <summary>
            Adds a day that is displayed in bold on a monthly basis in the month calendar.
            </summary>
      <param name="date">The date to be displayed in bold.</param>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.RemoveAllAnnuallyBoldedDates">
      <summary>
            Removes all the annually bold dates.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.RemoveAllBoldedDates">
      <summary>
            Removes all the nonrecurring bold dates.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.RemoveAllDisabledDates">
      <summary>
            Removes the all disabled dates.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.RemoveAllMonthlyBoldedDates">
      <summary>
            Removes all the monthly bold dates.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.RemoveAnnuallyBoldedDate(System.DateTime)">
      <summary>
            Removes the specified date from the list of annually bold dates.
            </summary>
      <param name="date">The date to remove from the date list.</param>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.RemoveBoldedDate(System.DateTime)">
      <summary>
            Removes the specified date from the list of nonrecurring bold dates.
            </summary>
      <param name="date">The date to remove from the date list.</param>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.RemoveDisabledDate(System.DateTime)">
      <summary>
            Removes the specified date from the list of disabled dates.
            </summary>
      <param name="date">The date to remove from the date list.</param>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.RemoveMonthlyBoldedDate(System.DateTime)">
      <summary>
            Removes the specified date from the list of monthly bolded dates.
            </summary>
      <param name="date">The date to remove from the date list.</param>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.SetCalendarDimensions(System.Int32,System.Int32)">
      <summary>
            Sets the number of columns and rows of months to display.
            </summary>
      <param name="x">The number of columns.</param>
      <param name="y">The number of rows.</param>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.SetDate(System.DateTime)">
      <summary>
            Sets a date as the currently selected date.
            </summary>
      <param name="date">The date to be selected.</param>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.GetDisplayRange(System.Boolean)">
      <summary>
            Retrieves date information that represents the low and high limits of the displayed dates of the control.
            </summary>
      <param name="visible">true to retrieve only the dates that are fully contained in displayed months; otherwise, false.</param>
      <returns>The begin and end dates of the displayed calendar.</returns>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.SetSelectionRange(System.DateTime,System.DateTime)">
      <summary>
            Sets the selected dates in a month calendar control to the specified date range.
            </summary>
      <param name="date1">The beginning date of the selection range.</param>
      <param name="date2">The end date of the selection range.</param>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.HitTest(System.Drawing.Point)">
      <summary>
            Returns an object with information on which portion of a month calendar control
                is at a location specified by a System.Drawing.Point.
            </summary>
      <param name="point">A System.Drawing.Point containing the System.Drawing.Point.X and System.Drawing.Point.Y
                coordinates of the point to be hit tested.</param>
      <returns>A System.Windows.Forms.MonthCalendar.HitTestInfo that contains information
                about the specified point on the month calendar.</returns>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.HitTest(System.Int32,System.Int32)">
      <summary>
            Returns a System.Windows.Forms.MonthCalendar.HitTestInfo with information
                on which portion of a month calendar control is at a specified x- and y-coordinate.
            </summary>
      <param name="x">The System.Drawing.Point.Y coordinate of the point to be hit tested.</param>
      <param name="y">The System.Drawing.Point.X coordinate of the point to be hit tested.</param>
      <returns>A System.Windows.Forms.MonthCalendar.HitTestInfo that contains information
                about the specified point on the month calendar.</returns>
    </member>
    <member name="M:C1.Win.C1Input.DateEditMonthCalendar.OnPaint(System.Windows.Forms.PaintEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="P:C1.Win.C1Input.DateEditMonthCalendar.CurrentMonthDisplayOffset">
      <summary>
            Gets or sets the current month display offset.
            </summary>
      <example>
            This example shows current month at the center of the multi-dimensional 
            drop down calendar.
            <code>
              c1DateEdit1.Calendar.CalendarDimensions = new Size(3, 1);
              // Display current month at the center of the calendar.
              // Other possible values:
              // 0 - (Default) - at the Right
              // 1 - Center
              // 2 - Left
              c1DateEdit1.Calendar.CurrentMonthDisplayOffset = 1;
            </code></example>
    </member>
    <member name="P:C1.Win.C1Input.DateEditMonthCalendar.VisualStyle">
      <summary>
            Gets or sets VisualStyle.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DateEditMonthCalendar.TodayDate">
      <summary>
            Gets or sets the value that is used by DateEditMonthCalendar as today's date.
            The default value is the current system date.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DateEditMonthCalendar.MinDate">
      <summary>
            Gets or sets the minimum allowable date. The default value is DateTime.MinValue (01/01/0001).
            </summary>
      <exception cref="T:System.ArgumentException">
            The value is greater than the <see cref="P:C1.Win.C1Input.DateEditMonthCalendar.MaxDate" />.
            </exception>
    </member>
    <member name="P:C1.Win.C1Input.DateEditMonthCalendar.MaxDate">
      <summary>
            Gets or sets the maximum allowable date.
            </summary>
      <exception cref="T:System.ArgumentException">
            The value is less than the <see cref="P:C1.Win.C1Input.DateEditMonthCalendar.MinDate" />.
            </exception>
    </member>
    <member name="P:C1.Win.C1Input.DateEditMonthCalendar.DateIsNull">
      <summary>
            Gets or sets the value indicating that no date is currently selected in the calendar.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DateEditMonthCalendar.BackColor">
      <summary>
            Gets or sets the background color of the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DateEditMonthCalendar.ForeColor">
      <summary>
            Gets or sets the foreground color of the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DateEditMonthCalendar.SelectionForeColor">
      <summary>
            Gets or sets the selection foreground color.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DateEditMonthCalendar.SelectionBackColor">
      <summary>
            Gets or sets the selection background color.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DateEditMonthCalendar.LineColor">
      <summary>
            Gets or sets the line color.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DateEditMonthCalendar.ArrowColor">
      <summary>
            Gets or sets the color of the arrows.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DateEditMonthCalendar.TodayBorderColor">
      <summary>
            Gets or sets the color of the border around the current date.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DateEditMonthCalendar.TrailingForeColor">
      <summary>
             Gets or sets a value indicating the color of days in months that are not
                fully displayed in the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DateEditMonthCalendar.DayNamesColor">
      <summary>
            Gets or sets the day names' text color.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DateEditMonthCalendar.TitleBackColor">
      <summary>
            Gets or sets a value indicating the background color of the title area of the calendar.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DateEditMonthCalendar.TitleForeColor">
      <summary>
            Gets or sets a value indicating the foreground color of the title area of the calendar.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DateEditMonthCalendar.FirstMonth">
      <summary>
            Gets the first day of the first fully shown month.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DateEditMonthCalendar.LastMonth">
      <summary>
            Gets the last day of the last fully shown month.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DateEditMonthCalendar.SelectedDate">
      <summary>
            Gets or sets the selected date.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DateEditMonthCalendar.CalendarDimensions">
      <summary>
            Gets or sets the number of columns and rows of months displayed.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DateEditMonthCalendar.FirstDayOfWeek">
      <summary>
            Gets or sets the first day of the week as displayed in the month calendar.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DateEditMonthCalendar.CaptionFormat">
      <summary>
            Gets or sets the format to use for the calendar caption.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DateEditMonthCalendar.DayNameLength">
      <summary>
            Gets or sets the maximum length of day names.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DateEditMonthCalendar.TitleHeight">
      <summary>
            Gets or sets the title height.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DateEditMonthCalendar.TitleFont">
      <summary>
            Gets or sets the title font.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DateEditMonthCalendar.DayNamesFont">
      <summary>
            Gets or sets the day names font.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DateEditMonthCalendar.BoldedDates">
      <summary>
            Gets or sets the array of System.DateTime objects that determines which nonrecurring dates are displayed in bold.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DateEditMonthCalendar.AnnuallyBoldedDates">
      <summary>
            Gets or sets the array of System.DateTime objects that determines which annual days are displayed in bold.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DateEditMonthCalendar.MonthlyBoldedDates">
      <summary>
            Gets or sets the array of System.DateTime objects that determine which monthly days to bold.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DateEditMonthCalendar.DisabledDates">
      <summary>
            Gets or sets the array of System.DateTime objects that determines which dates are disabled.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DateEditMonthCalendar.ShowToday">
      <summary>
            Gets or sets a value indicating whether the date represented by the TodayDate property is displayed at the bottom of the control. 
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DateEditMonthCalendar.ShowTodayCircle">
      <summary>
            Gets or sets a value indicating whether today's date is circled.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DateEditMonthCalendar.ShowTodayButton">
      <summary>
            Determines if the Today button of the dropdown calendar is visible.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DateEditMonthCalendar.ShowClearButton">
      <summary>
            Determines if the Clear button of the dropdown calendar is visible.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DateEditMonthCalendar.TodayText">
      <summary>
            Gets or sets the text of the "Today" button.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DateEditMonthCalendar.ClearText">
      <summary>
            Gets or sets the text of the "Clear" button.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DateEditMonthCalendar.CalendarWeekRule">
      <summary>
            Gets or sets a value that specifies which rule is used to determine the first calendar week of the year.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DateEditMonthCalendar.ShowWeekNumbers">
      <summary>
            Gets or sets a value indicating whether the month calendar control displays
               week numbers (1-52) to the left of each row of days.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DateEditMonthCalendar.RightToLeft">
      <summary>
            Gets or sets a value indicating whether control's elements are aligned to support locales using right-to-left fonts.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DateEditMonthCalendar.RightToLeftLayout">
      <summary>
            Gets or sets a value indicating whether the control is laid out from right to left.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.DateEditMonthCalendar.VisualStyleChanged">
      <summary>
            Occurs when the VisualStyle property has changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.DateEditMonthCalendar.TodayButtonClick">
      <summary>
            Occurs when Today button is clicked.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.DateEditMonthCalendar.ClearButtonClick">
      <summary>
            Occurs when Clear button is clicked.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.DateEditMonthCalendar.TodayButtonVisibilityChanged">
      <summary>
            Occurs when Today button visibility is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.DateEditMonthCalendar.ClearButtonVisibilityChanged">
      <summary>
            Occurs when Clear button visibility is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.DateEditMonthCalendar.DateValueChanged">
      <summary>
            Occurs when the value of the Date property is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.DateEditMonthCalendar.DateValueSelected">
      <summary>
            Occurs when the user selects a new value for the Date property.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.DateEditMonthCalendar.RightToLeftLayoutChanged">
      <summary>
            Occurs when the value of the RightToLeftLayout property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.DateEditMonthCalendar.MonthChanged">
      <summary>
            Handles the month change in the Dropdown calendar in C1DateEdit. 
            Occurs when the value of the <see cref="P:C1.Win.C1Input.DateEditMonthCalendar.FirstMonth" /> or <see cref="P:C1.Win.C1Input.DateEditMonthCalendar.LastMonth" /> property changes.
            </summary>
      <remarks>
            Can be used to set some dates in the month to bold as user scrolls through the months.
            </remarks>
      <example>
            This example sets a new bolded date in the selected month.
            <code>
            private void c1DateEdit1_Calendar_MonthChanged(object sender, EventArgs e)
            {
               DateTime dt = c1DateEdit1.Calendar.FirstMonth;
               c1DateEdit1.Calendar.AddBoldedDate(new DateTime(dt.Year, dt.Month, dt.Month));
            }
            </code></example>
    </member>
    <member name="P:C1.Win.C1Input.DateEditMonthCalendar.CurrentCulture">
      <summary>
            Get or sets current culture for the calendar.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.C1NumericEdit">
      <summary>
            Displays and edits numeric values. Includes a dropdown calculator for easy input of numeric values.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1NumericEdit.#ctor">
      <summary>
            Initializes a new instance of the C1NumericEdit class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1NumericEdit.ShouldSerializeIncrement">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.C1NumericEdit.ResetIncrement">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1NumericEdit.ShouldSerializeCalculator">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.C1NumericEdit.SpinUp">
      <summary>
              Performs spin up.
            </summary>
      <remarks>
        <see cref="P:C1.Win.C1Input.C1NumericEdit.Increment" /> property is used to define the increment.
              This method has the same behavior with the Up key pressed.
            </remarks>
      <seealso cref="M:C1.Win.C1Input.C1NumericEdit.SpinDown" />
    </member>
    <member name="M:C1.Win.C1Input.C1NumericEdit.SpinUp(System.Object)">
      <summary>
              Performs spin up.
            </summary>
      <param name="increment">
              The <see cref="T:System.Object" /> value that indicates the increment when spin up.
            </param>
      <remarks>
              This method has the same behavior with the Up key pressed.
            </remarks>
      <seealso cref="M:C1.Win.C1Input.C1NumericEdit.SpinDown(System.Object)" />
    </member>
    <member name="M:C1.Win.C1Input.C1NumericEdit.SpinDown">
      <summary>
              Performs spin down.
            </summary>
      <remarks>
        <see cref="P:C1.Win.C1Input.C1NumericEdit.Increment" /> property is used to define the decrement.
              This method has the same behavior with the Down key pressed.
            </remarks>
      <seealso cref="M:C1.Win.C1Input.C1NumericEdit.SpinUp" />
    </member>
    <member name="M:C1.Win.C1Input.C1NumericEdit.SpinDown(System.Object)">
      <summary>
              Performs spin down.
            </summary>
      <param name="decrement">
              The <see cref="T:System.Object" /> value that indicates the decrement when spin down.
            </param>
      <remarks>
              This method has the same behavior with the Down key pressed.
            </remarks>
      <seealso cref="M:C1.Win.C1Input.C1NumericEdit.SpinDown(System.Object)" />
    </member>
    <member name="M:C1.Win.C1Input.C1NumericEdit.OnUpDownButtonClick(C1.Win.C1Input.UpDownButtonClickEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1NumericEdit.OpenDropDown">
      <summary>
            Shows the dropdown.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1NumericEdit.UpdateDataType(System.Type)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="propertyType">
      </param>
    </member>
    <member name="P:C1.Win.C1Input.C1NumericEdit.DefaultDropDownFormClassName">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1NumericEdit.TypeRestriction">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1NumericEdit.DataType">
      <summary>
            Type of the Value property.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1NumericEdit.FormatType">
      <summary>
            Enumeration value determining the formatting metod, including standard .NET format specifiers, custom and programmatic formatting.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1NumericEdit.Increment">
      <summary>
            Indicates the amount to increment/decrement when the user clicks up/down buttons.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1NumericEdit.Calculator">
      <summary>
            The dropdown calculator object.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1NumericEdit.Text">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1NumericEdit.DefaultThemeRootPath">
      <summary>
            Default path for the control in xml theme file.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.C1PictureBox">
      <summary>
            In addition to the standard PictureBox functionality, C1PictureBox supports data binding to data source fields containing image data. 
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1PictureBox.#ctor">
      <summary>
            Initializes a new instance of the C1PictureBox class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1PictureBox.Dispose(System.Boolean)">
      <summary>
            Releases the resources used by the C1PictureBox.
            </summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
    </member>
    <member name="M:C1.Win.C1Input.C1PictureBox.ImageToByteArray(System.Drawing.Image,System.Drawing.Imaging.ImageFormat)">
      <summary>
            Returns the contents of an image.
            </summary>
      <param name="image">Image to return as a byte array.</param>
      <param name="format">Format in which to return the image contents.</param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.C1PictureBox.ImageFromByteArray(System.Byte[])">
      <summary>
            Converts a byte array to an image.
            </summary>
      <param name="buffer">The contents of an image. </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.C1PictureBox.CopyImage">
      <summary>
            Copies the image currently in the control to the clipboard.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1PictureBox.PasteImage">
      <summary>
            If the clipboard contains an image, this method replaces the contents of the Image property of the control
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1PictureBox.OnBindingFormatting(System.Windows.Forms.ConvertEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1PictureBox.OnBindingParsing(System.Windows.Forms.ConvertEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1PictureBox.OnImageChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1PictureBox.OnMouseDown(System.Windows.Forms.MouseEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1PictureBox.OnGotFocus(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1PictureBox.OnLostFocus(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1PictureBox.WndProc(System.Windows.Forms.Message@)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="m">
      </param>
    </member>
    <member name="P:C1.Win.C1Input.C1PictureBox.DataSource">
      <summary>
            The data source object to which the control is bound.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1PictureBox.DataField">
      <summary>
            The field of the data source object to which the control is bound.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1PictureBox.ImmediateUpdate">
      <summary>
            Gets or sets whether the image should be put into the data source right after the Image property has been changed or it shouldn't be put there until the following call of the CurrencyManager.EndCurrentEdit() method.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1PictureBox.Image">
      <summary>
            The image displayed in the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1PictureBox.FocusOnClick">
      <summary>
            Gets or sets the value indicating whether the picture box receives focus when clicked.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1PictureBox.BindingFormatting">
      <summary>
            Occurs when Image is retrieved from the data source.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1PictureBox.BindingParsing">
      <summary>
            Occurs when Image is stored in the data source.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1PictureBox.ImageChanged">
      <summary>
            Occurs when the Image changes.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.C1RangeSlider">
      <summary>
            Represents a Windows range slider control supporting additional visual styles.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.#ctor">
      <summary>
            Initializes a new instance of the C1RangeSlider class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.Dispose(System.Boolean)">
      <summary>
            Releases the resources used by the C1RangeSlider.
            </summary>
      <param name="disposing">
        <b>true</b> to release both managed and unmanaged resources; <b>false</b> to release only unmanaged resources.</param>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.ResetStyles">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.ShouldSerializeStyles">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>True if Styles should be serialized, false otherwise.</returns>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.ResetVisualStyle">
      <summary>
            Resets <see cref="P:C1.Win.C1Input.C1RangeSlider.VisualStyle" /> to the default value.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.ShouldSerializeVisualStyle">
      <summary>
            Tests whether <see cref="P:C1.Win.C1Input.C1RangeSlider.VisualStyle" /> should be serialized.
            </summary>
      <returns>True if <see cref="P:C1.Win.C1Input.C1RangeSlider.VisualStyle" /> should be serialized, false otherwise.</returns>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.OnVisualStyleChanged(System.EventArgs)">
      <summary>
            Invokes the VisualStyleChanged event.
            </summary>
      <param name="e">The event arguments.</param>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.ResetVisualStyleBaseStyle">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.ShouldSerializeVisualStyleBaseStyle">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>True if VisualStyleBaseStyle should be serialized, false otherwise.</returns>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.OnLowerValueChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.OnUpperValueChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.OnValueChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.OnOrientationChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.OnDirectionChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.OnPaint(System.Windows.Forms.PaintEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.OnMouseLeave(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.OnMouseDown(System.Windows.Forms.MouseEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.OnMouseMove(System.Windows.Forms.MouseEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.OnMouseUp(System.Windows.Forms.MouseEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.OnGotFocus(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.OnLostFocus(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="P:C1.Win.C1Input.C1RangeSlider.LowerValue">
      <summary>
            Gets or sets the current lower magnitude of the range control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1RangeSlider.UpperValue">
      <summary>
            Gets or sets the current upper magnitude of the range control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1RangeSlider.Minimum">
      <summary>
            Gets or sets the minimum possible value of the range element.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1RangeSlider.Maximum">
      <summary>
            Gets or sets the maximum possible value of the range element.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1RangeSlider.IsRangeBarVisible">
      <summary>
            Gets or sets a value that indicates whether C1RangeSlider bar should be displayed.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1RangeSlider.Orientation">
      <summary>
            Gets or sets a value indicating the horizontal or vertical orientation of the C1RangeSlider.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1RangeSlider.IsDirectionReversed">
      <summary>
            Gets or sets the direction of increasing value.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1RangeSlider.LowerThumbPath">
      <summary>
            Gets or sets custom thumb path for lower value.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1RangeSlider.UpperThumbPath">
      <summary>
            Gets or sets custom thumb path for upper value.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1RangeSlider.Enabled">
      <summary>
            Gets or sets a value indicating whether the control can respond to user interaction.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1RangeSlider.Styles">
      <summary>
            Gets or sets a collection of named Style objects.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1RangeSlider.VisualStyle">
      <summary>
            Gets or sets the visual style of the control.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1RangeSlider.VisualStyleChanged">
      <summary>
            Occurs when the VisualStyle property has changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1RangeSlider.LowerValueChanged">
      <summary>
            Fires when the <see cref="P:C1.Win.C1Input.C1RangeSlider.LowerValue" /> property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1RangeSlider.UpperValueChanged">
      <summary>
            Fires when the <see cref="P:C1.Win.C1Input.C1RangeSlider.UpperValue" /> property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1RangeSlider.ValueChanged">
      <summary>
            Fires when any of the properties: LowerValue or UpperValue changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1RangeSlider.OrientationChanged">
      <summary>
            Fires when the <see cref="P:C1.Win.C1Input.C1RangeSlider.Orientation" /> property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1RangeSlider.DirectionChanged">
      <summary>
            Fires when the <see cref="P:C1.Win.C1Input.C1RangeSlider.IsDirectionReversed" /> property changes.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1RangeSlider.DefaultThemeRootPath">
      <summary>
            Default Theme root path.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection">
      <summary>
            Represents a collection of named Style objects.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.ResetBarStyle">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.ShouldSerializeBarStyle">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>True if BarStyle should be serialized, false otherwise.</returns>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.ResetThumbStyle">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.ShouldSerializeThumbStyle">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>True if ThumbStyle should be serialized, false otherwise.</returns>
    </member>
    <member name="P:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.BarStyle">
      <summary>
            Gets or sets a collection of named Style objects for bar.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.ThumbStyle">
      <summary>
            Gets or sets a collection of named Style objects for thumb.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.BarStyleCollection">
      <summary>
            Represents a collection of named Style objects for bar.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.BarStyleCollection.ResetBackColor">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.BarStyleCollection.ShouldSerializeBackColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>True if BackSolor should be serialized, false otherwise.</returns>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.BarStyleCollection.ResetBorderColor">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.BarStyleCollection.ShouldSerializeBorderColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>True if BorderColor should be serialized, false otherwise.</returns>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.BarStyleCollection.ResetDisabledBackColor">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.BarStyleCollection.ShouldSerializeDisabledBackColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>True if DisabledBackColor should be serialized, false otherwise.</returns>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.BarStyleCollection.ResetDisabledBorderColor">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.BarStyleCollection.ShouldSerializeDisabledBorderColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>True if DisabledBorderColor should be serialized, false otherwise.</returns>
    </member>
    <member name="P:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.BarStyleCollection.BackColor">
      <summary>
            Gets or sets background color of bar.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.BarStyleCollection.BorderColor">
      <summary>
            Gets or sets border color of bar.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.BarStyleCollection.DisabledBackColor">
      <summary>
            Gets or sets background color of bar if control is disabled.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.BarStyleCollection.DisabledBorderColor">
      <summary>
            Gets or sets border color of bar if control is disabled.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.ThumbStyleCollection">
      <summary>
            Represents a collection of named Style objects for thumb.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.ThumbStyleCollection.ResetBackColor">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.ThumbStyleCollection.ShouldSerializeBackColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>True if BackColor should be serialized, false otherwise.</returns>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.ThumbStyleCollection.ResetBorderColor">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.ThumbStyleCollection.ShouldSerializeBorderColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>True if BorderColor should be serialized, false otherwise.</returns>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.ThumbStyleCollection.ResetHotBackColor">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.ThumbStyleCollection.ShouldSerializeHotBackColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>True if HotBackColor should be serialized, false otherwise.</returns>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.ThumbStyleCollection.ResetHotBorderColor">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.ThumbStyleCollection.ShouldSerializeHotBorderColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>True if HotBorderColor should be serialized, false otherwise.</returns>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.ThumbStyleCollection.ResetPressedBackColor">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.ThumbStyleCollection.ShouldSerializePressedBackColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>True if PressedBackColor should be serialized, false otherwise.</returns>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.ThumbStyleCollection.ResetPressedBorderColor">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.ThumbStyleCollection.ShouldSerializePressedBorderColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>True if PressedBorderColor should be serialized, false otherwise.</returns>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.ThumbStyleCollection.ResetDisabledBackColor">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.ThumbStyleCollection.ShouldSerializeDisabledBackColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>True if DisabledBackColor should be serialized, false otherwise.</returns>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.ThumbStyleCollection.ResetDisabledBorderColor">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.ThumbStyleCollection.ShouldSerializeDisabledBorderColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>True if DisabledBorderColor should be serialized, false otherwise.</returns>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.ThumbStyleCollection.ResetCornerRadius">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.ThumbStyleCollection.ShouldSerializeCornerRadius">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>True if CornerRadius should be serialized, false otherwise.</returns>
    </member>
    <member name="P:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.ThumbStyleCollection.BackColor">
      <summary>
            Gets or sets background color of thumb.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.ThumbStyleCollection.BorderColor">
      <summary>
            Gets or sets border color of thumb.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.ThumbStyleCollection.HotBackColor">
      <summary>
            Gets or sets background color of thumb if mouse is over it.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.ThumbStyleCollection.HotBorderColor">
      <summary>
            Gets or sets border color of thumb if mouse is over it.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.ThumbStyleCollection.PressedBackColor">
      <summary>
            Gets or sets background color of thumb when user performs mouse click on it.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.ThumbStyleCollection.PressedBorderColor">
      <summary>
            Gets or sets border color of thumb when user performs mouse click on it.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.ThumbStyleCollection.DisabledBackColor">
      <summary>
            Gets or sets background color of thumb if control is disabled.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.ThumbStyleCollection.DisabledBorderColor">
      <summary>
            Gets or sets border color of thumb if control is disabled.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1RangeSlider.RangeSliderStyleCollection.ThumbStyleCollection.CornerRadius">
      <summary>
            Gets or sets corner radius of thumb.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.CalendarType">
      <summary>
            Calendar used with C1DateEdit.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.CalendarType.Default">
      <summary>
            Represents default calendar.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.CalendarType.ChineseLunisolarCalendar">
      <summary>
            Represents Chinese Lunisolar Calendar
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.CalendarType.EastAsianLunisolarCalendar">
      <summary>
            Represents East Asian Lunisolar Calendar
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.CalendarType.GregorianCalendar">
      <summary>
            Represents Gregorian Calendar
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.CalendarType.HebrewCalendar">
      <summary>
            Represents Hebrew Calendar
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.CalendarType.HijriCalendar">
      <summary>
            Represents Hijri Calendar
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.CalendarType.JapaneseCalendar">
      <summary>
            Represents Japanese Calendar
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.CalendarType.JapaneseLunisolarCalendar">
      <summary>
            Represents Japanese Lunisolar Calendar
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.CalendarType.JulianCalendar">
      <summary>
            Represents Julian Calendar
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.CalendarType.KoreanCalendar">
      <summary>
            Represents Korean Calendar
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.CalendarType.KoreanLunisolarCalendar">
      <summary>
            Represents Korean Lunisolar Calendar
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.CalendarType.TaiwanCalendar">
      <summary>
            Represents Taiwan Calendar
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.CalendarType.TaiwanLunisolarCalendar">
      <summary>
            Represents Taiwan Lunisolar Calendar
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.CalendarType.ThaiBuddhistCalendar">
      <summary>
            Represents Thai Buddhist Calendar
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.CalendarType.UmAlQuraCalendar">
      <summary>
            Represents UmAlQuraCalendar
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.UpDownButtonAlignment">
      <summary>
            Specifies alignment of spin button of a <see cref="T:C1.Win.C1Input.C1DropDownControl" />.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.UpDownButtonAlignment.Default">
      <summary>
            Vertically stacked Up and Down buttons.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.UpDownButtonAlignment.UpLeftDownRight">
      <summary>
            Up button on the left and down button on the right.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.UpDownButtonAlignment.UpRightDownLeft">
      <summary>
            Up button on the right and down button on the left.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.DropDownItem">
      <summary>
            Represents drop down item for <see cref="T:C1.Win.C1Input.C1SplitButton" /> control.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DropDownItem.#ctor">
      <summary>
            Creates an instance of DropDownItem class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DropDownItem.Dispose(System.Boolean)">
      <summary>
            Releases the resources used by the DropDownItem.
            </summary>
      <param name="disposing">
        <b>true</b> to release both managed and unmanaged resources; <b>false</b> to release only unmanaged resources.</param>
    </member>
    <member name="M:C1.Win.C1Input.DropDownItem.OnDropDown(System.EventArgs)">
      <summary>
            Raises the <see cref="E:C1.Win.C1Input.DropDownItem.DropDown" /> event.
            </summary>
      <param name="e">A <see cref="T:System.EventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:C1.Win.C1Input.DropDownItem.OnDropDownClosed(System.EventArgs)">
      <summary>
            Raises the <see cref="E:C1.Win.C1Input.DropDownItem.DropDownClosed" /> event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DropDownItem.OnClick(System.EventArgs)">
      <summary>
            Raises the <see cref="E:C1.Win.C1Input.DropDownItem.Click" /> event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DropDownItem.OnKeyDown(System.Windows.Forms.KeyEventArgs)">
      <summary>
            Raises the <see cref="E:C1.Win.C1Input.DropDownItem.KeyDown" /> event.
            </summary>
      <param name="e">
        <see cref="T:System.Windows.Forms.KeyEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:C1.Win.C1Input.DropDownItem.OnKeyPress(System.Windows.Forms.KeyPressEventArgs)">
      <summary>
            Raises the <see cref="E:C1.Win.C1Input.DropDownItem.KeyPress" /> event.
            </summary>
      <param name="e">
        <see cref="T:System.Windows.Forms.KeyPressEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:C1.Win.C1Input.DropDownItem.OnKeyUp(System.Windows.Forms.KeyEventArgs)">
      <summary>
            Raises the <see cref="E:C1.Win.C1Input.DropDownItem.KeyUp" /> event.
            </summary>
      <param name="e">
        <see cref="T:System.Windows.Forms.KeyEventArgs" /> that contains the event data.</param>
    </member>
    <member name="M:C1.Win.C1Input.DropDownItem.OnGotFocus(System.EventArgs)">
      <summary>
            Raises the <see cref="E:C1.Win.C1Input.DropDownItem.GotFocus" /> event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DropDownItem.OnLostFocus(System.EventArgs)">
      <summary>
            Raises the <see cref="E:C1.Win.C1Input.DropDownItem.LostFocus" /> event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DropDownItem.OnDoubleClick(System.EventArgs)">
      <summary>
            Raises the <see cref="E:C1.Win.C1Input.DropDownItem.DoubleClick" /> event.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DropDownItem.SplitButton">
      <summary>
            Gets the <see cref="T:C1.Win.C1Input.C1SplitButton" /> that this <see cref="T:C1.Win.C1Input.DropDownItem" /> belongs to.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DropDownItem.Items">
      <summary>
            Gets the collection of child items in that is associated with this DropDownItem.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DropDownItem.CheckOnClick">
      <summary>
            Gets or sets a value that indicates whether the item should toggle its pressed state when it is clicked.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DropDownItem.Text">
      <summary>
            Gets or sets the text associated with the component.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DropDownItem.Checked">
      <summary>
            Gets or sets a value that indicates whether the item is checked.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DropDownItem.DialogResult">
      <summary>
            Gets or sets the dialog-box result produced in a modal form by clicking the button.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DropDownItem.Image">
      <summary>
            Gets or sets the image associated with the <see cref="T:C1.Win.C1Input.DropDownItem" />.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DropDownItem.ImageIndex">
      <summary>
            Gets or sets the index of the image in the ImageList.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DropDownItem.ImageKey">
      <summary>
            Gets or sets the image key in the ImageList.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DropDownItem.ImageSize">
      <summary>
            Gets or sets the size of the image that will be displayed on the button.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DropDownItem.ImageAlign">
      <summary>
            Gets or sets the alignment of the image within the bounds specified by the <see cref="P:C1.Win.C1Input.DropDownItem.ImageSize" /> property.
            </summary>
      <remarks>
            The value of this property is only used if <see cref="P:C1.Win.C1Input.DropDownItem.ImageScaling" /> = Clip and <see cref="P:C1.Win.C1Input.DropDownItem.ImageSize" />
            exceeds the original size of the image.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Input.DropDownItem.ImageScaling">
      <summary>
            Gets or sets how the image will be scaled to fit within the bounds specified by the <see cref="P:C1.Win.C1Input.DropDownItem.ImageSize" /> property.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DropDownItem.Enabled">
      <summary>
            Gets or sets a value indicating whether the item can respond to user interaction.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DropDownItem.Visible">
      <summary>
            Gets or sets a value indicating whether the item is visible.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DropDownItem.Tag">
      <summary>
            Gets or sets the object that contains data about the item.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DropDownItem.IsDisposed">
      <summary>
            Gets a value indicating whether the component has been disposed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.DropDownItem.DropDown">
      <summary>
            Fires when the drop-down portion of this element is shown.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.DropDownItem.DropDownClosed">
      <summary>
            Indicates that the drop-down portion of this element has closed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.DropDownItem.Click">
      <summary>
            Fires when the component is clicked.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.DropDownItem.KeyDown">
      <summary>
            Fires when a key is pressed while the component has focus. 
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.DropDownItem.KeyPress">
      <summary>
            Fires when a key is pressed while the component has focus. 
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.DropDownItem.KeyUp">
      <summary>
            Fires when a key is released while the component has focus. 
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.DropDownItem.GotFocus">
      <summary>
            Fires when the component receives the focus.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.DropDownItem.LostFocus">
      <summary>
            Fires when the component loses the focus.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.DropDownItem.DoubleClick">
      <summary>
            Fires when the component is double-clicked.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.C1SplitButton">
      <summary>
            Represents a composite button control supporting additional visual styles and drop down item list.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1SplitButton.#ctor">
      <summary>
            Initializes a new instance of the C1SplitButton class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.C1SplitButton.Dispose(System.Boolean)">
      <summary>
            Releases the resources used by the C1SplitButton.
            </summary>
      <param name="disposing">
        <b>true</b> to release both managed and unmanaged resources; <b>false</b> to release only unmanaged resources.</param>
    </member>
    <member name="M:C1.Win.C1Input.C1SplitButton.OnDropDownItemClicked(C1.Win.C1Input.DropDownItemClickedEventArgs)">
      <summary>
            Raises <see cref="E:C1.Win.C1Input.C1SplitButton.DropDownItemClicked" /> event.
            </summary>
      <param name="e">A <see cref="T:C1.Win.C1Input.DropDownItemClickedEventArgs" /> that contains the event data. </param>
    </member>
    <member name="M:C1.Win.C1Input.C1SplitButton.OnDropDownClosed(C1.Win.C1Input.DropDownClosedEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1SplitButton.OnDropDownOpened(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.C1SplitButton.GetPreferredSize(System.Drawing.Size)">
      <summary>
            Retrieves the size of a rectangular area into which a control can be fitted.
            </summary>
      <param name="proposedSize">The custom-sized area for a control.</param>
      <returns>An ordered pair of type <see cref="T:System.Drawing.Size" /> representing the width and height of a rectangle.</returns>
    </member>
    <member name="P:C1.Win.C1Input.C1SplitButton.Items">
      <summary>
            Gets the collection of <see cref="T:C1.Win.C1Input.DropDownItem" /> components on this button.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1SplitButton.DropDownImageList">
      <summary>
            The ImageList to get the images to display in the drop down items.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1SplitButton.DefaultItem">
      <summary>
            Gets or sets the default drop down item.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1SplitButton.DropDownItemClicked">
      <summary>
            Occurs when a <see cref="T:C1.Win.C1Input.DropDownItem" /> is clicked.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1SplitButton.DropDownClosed">
      <summary>
            Occurs when the dropdown has been closed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Input.C1SplitButton.DropDownOpened">
      <summary>
            Occurs after the dropdown is opened.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1SplitButton.DroppedDown">
      <summary>
            Run-time read-only property indicating if the dropdown is currently open.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.C1SplitButton.DefaultThemeRootPath">
      <summary>
            Default path for the control in xml theme file.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.DropDownItemCollection">
      <summary>
            Represents collection of drop down items.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DropDownItemCollection.Add(C1.Win.C1Input.DropDownItem)">
      <summary>
            Adds the specified item to the end of the collection. 
            </summary>
      <param name="value">The <see cref="T:C1.Win.C1Input.DropDownItem" /> to be added to the end of the collection. </param>
      <returns>The zero-based index value of the <see cref="T:C1.Win.C1Input.DropDownItem" /> added to the collection.</returns>
    </member>
    <member name="M:C1.Win.C1Input.DropDownItemCollection.IndexOf(C1.Win.C1Input.DropDownItem)">
      <summary>
            Returns the zero-based index of the first occurrence of a value in the list.
            </summary>
      <param name="value">The item to locate in the list.</param>
      <returns>The zero-based index of the first occurrence of value within the entire list, if found; otherwise, -1.</returns>
    </member>
    <member name="M:C1.Win.C1Input.DropDownItemCollection.Insert(System.Int32,C1.Win.C1Input.DropDownItem)">
      <summary>
            Inserts the specified item into the collection at the specified location.
            </summary>
      <param name="index">The indexed location within the collection to insert the item. </param>
      <param name="value">The item to insert.</param>
    </member>
    <member name="M:C1.Win.C1Input.DropDownItemCollection.Remove(C1.Win.C1Input.DropDownItem)">
      <summary>
            Removes the specified item from the collection.
            </summary>
      <param name="value">The item to remove from the collection. </param>
    </member>
    <member name="M:C1.Win.C1Input.DropDownItemCollection.Contains(C1.Win.C1Input.DropDownItem)">
      <summary>
            Indicates whether the collection contains a specific <see cref="T:C1.Win.C1Input.DropDownItem" />.
            </summary>
      <param name="value">The <see cref="T:C1.Win.C1Input.DropDownItem" /> object for which to search.</param>
      <returns>Whether the collection contains the specific <see cref="T:C1.Win.C1Input.DropDownItem" /> object.</returns>
    </member>
    <member name="M:C1.Win.C1Input.DropDownItemCollection.OnInsertComplete(System.Int32,System.Object)">
      <summary>
            Performs additional custom processes after inserting an <see cref="T:C1.Win.C1Input.DropDownItem" />
            into the <see cref="T:C1.Win.C1Input.DropDownItemCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DropDownItemCollection.OnRemoveComplete(System.Int32,System.Object)">
      <summary>
            Performs additional custom processes after removing an <see cref="T:C1.Win.C1Input.DropDownItem" />
            from the <see cref="T:C1.Win.C1Input.DropDownItemCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DropDownItemCollection.OnSetComplete(System.Int32,System.Object,System.Object)">
      <summary>
            Performs additional custom processes after setting an <see cref="T:C1.Win.C1Input.DropDownItem" />
            in the <see cref="T:C1.Win.C1Input.DropDownItemCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DropDownItemCollection.OnClear">
      <summary>
            Performs additional custom processes when clearing the contents of
            the <see cref="T:C1.Win.C1Input.DropDownItemCollection" /> instance.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DropDownItemCollection.OnClearComplete">
      <summary>
            Performs additional custom processes after clearing the contents of
            the <see cref="T:C1.Win.C1Input.DropDownItemCollection" /> instance.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DropDownItemCollection.Item(System.Int32)">
      <summary>
            Gets or sets the item at the specified indexed location in the collection.
            </summary>
      <param name="index">The indexed location of the item in the collection. </param>
      <returns>An <see cref="T:C1.Win.C1Input.DropDownItem" /> that represents the item at the specified indexed location.</returns>
    </member>
    <member name="T:C1.Win.C1Input.Enums.BreakType">
      <summary>
            Specifies whether the layout should break the flow after the component.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.BreakType.None">
      <summary>
            The layout should not break the flow after the component.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.BreakType.Row">
      <summary>
            Causes next component to be placed in a new row within the flow.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.BreakType.Column">
      <summary>
            Creates a new column after this component.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.BreakType.Group">
      <summary>
            Creates a new row starting below the current component, at the left edge of the current group.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.Enums.ContentAlignment">
      <summary>
            Defines values for the content alignment within the <see cref="T:C1.Win.C1Input.DropDownItem" />.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.ContentAlignment.NotSet">
      <summary>
            Default value.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.ContentAlignment.Near">
      <summary>
            Near.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.ContentAlignment.Far">
      <summary>
            Far.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.ContentAlignment.Center">
      <summary>
            Center.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.ContentAlignment.Spread">
      <summary>
            Spread.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.Enums.InputComboBoxStyle">
      <summary>
            Determines whether the user can edit a value in the text portion of the <see cref="T:C1.Win.C1Input.C1ComboBox" />.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputComboBoxStyle.DropDown">
      <summary>
            The text portion is editable.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputComboBoxStyle.DropDownList">
      <summary>
            The user cannot directly edit the text portion.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.Enums.InputContentAlignment">
      <summary>
            Defines values for the content alignment within the <see cref="T:C1.Win.C1Input.DropDownItem" />.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputContentAlignment.NotSet">
      <summary>
            Default value.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputContentAlignment.Near">
      <summary>
            Near.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputContentAlignment.Far">
      <summary>
            Far.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputContentAlignment.Center">
      <summary>
            Center.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputContentAlignment.Spread">
      <summary>
            Spread.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.Enums.InputImageAlignment">
      <summary>
            Specifies the image alignment on the input components.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputImageAlignment.TopLeft">
      <summary>
            Image is vertically aligned at the top, 
            and horizontally aligned on the left. 
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputImageAlignment.TopCenter">
      <summary>
            Image is vertically aligned at the top, 
            and horizontally aligned at the center.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputImageAlignment.TopRight">
      <summary>
            Image is vertically aligned at the top, 
            and horizontally aligned on the right. 
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputImageAlignment.MiddleLeft">
      <summary>
            Image is vertically aligned in the middle, 
            and horizontally aligned on the left. 
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputImageAlignment.MiddleCenter">
      <summary>
            Image is vertically aligned in the middle, 
            and horizontally aligned at the center. 
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputImageAlignment.MiddleRight">
      <summary>
            Image is vertically aligned in the middle, 
            and horizontally aligned on the right. 
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputImageAlignment.BottomLeft">
      <summary>
            Image is vertically aligned at the bottom, 
            and horizontally aligned on the left. 		
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputImageAlignment.BottomCenter">
      <summary>
            Image is vertically aligned at the bottom, 
            and horizontally aligned at the center.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputImageAlignment.BottomRight">
      <summary>
            Image is vertically aligned at the bottom, 
            and horizontally aligned on the right. 
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.Enums.InputImageLayout">
      <summary>
            Specifies the layout logic used to display the background image in a rich tooltip.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputImageLayout.None">
      <summary>
            The image is left-aligned at the top across the control's client rectangle.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputImageLayout.Tile">
      <summary>
            The image is tiled across the control's client rectangle.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputImageLayout.Center">
      <summary>
            The image is centered within the control's client rectangle.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputImageLayout.Stretch">
      <summary>
            The image is stretched across the control's client rectangle. 
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputImageLayout.Zoom">
      <summary>
            The image is enlarged within the control's client rectangle.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputImageLayout.TileStretch">
      <summary>
            The image is split into nine pieces and tiled within the control's client rectangle.
            Center tiles are stretched, corner tiles are rendered with the original size.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.Enums.InputImageScaling">
      <summary>
            Specifies the image scaling on the input components.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputImageScaling.Clip">
      <summary>
            Use the image's original size, clipping it to the display area if necessary.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputImageScaling.Stretch">
      <summary>
            Stretch the image to fill the display area. 
            This mode will usually change the image's aspect ratio.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputImageScaling.Scale">
      <summary>
            Scale the image to fit the display area. 
            This mode may increase or reduce the size of the image while maintaining its aspect ratio.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputImageScaling.Tile">
      <summary>
            Tile the image to fill the display area.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputImageScaling.TileStretch">
      <summary>
            Tile the image into the 3x3 matrix and stretch it to fill the display area.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputImageScaling.TileStretchHorizontal">
      <summary>
            Tile the image into the row of 3 images and stretch it to fill the display area.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputImageScaling.TileStretchVertical">
      <summary>
            Tile the image into the column of 3 images and stretch it to fill the display area.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.Enums.InputNavigatorButton">
      <summary>
            A list of all available navigator buttons.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputNavigatorButton.MoveFirst">
      <summary>
            The 'Move First' button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputNavigatorButton.MovePrevious">
      <summary>
            The 'Move Previous' button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputNavigatorButton.MoveNext">
      <summary>
            The 'Move Next' button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputNavigatorButton.MoveLast">
      <summary>
            The 'Move Last' button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputNavigatorButton.AddNew">
      <summary>
            The 'Add New' button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputNavigatorButton.Delete">
      <summary>
            The 'Delete' button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputNavigatorButton.Edit">
      <summary>
            The 'Edit' button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputNavigatorButton.Apply">
      <summary>
            The 'Apply' button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputNavigatorButton.Cancel">
      <summary>
            The 'Cancel' button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputNavigatorButton.Save">
      <summary>
            The 'Save Data' button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputNavigatorButton.Reload">
      <summary>
            The 'Reload Data' button
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.Enums.InputNavigatorItems">
      <summary>
            A set of navigator items.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputNavigatorItems.None">
      <summary>
            No items are in the set.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputNavigatorItems.MoveFirstButton">
      <summary>
            The 'Move First' button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputNavigatorItems.MovePreviousButton">
      <summary>
            The 'Move Previous' button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputNavigatorItems.PositionInputBox">
      <summary>
            The input box that changes the current position.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputNavigatorItems.CountLabel">
      <summary>
            The text label that displays the total number of items.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputNavigatorItems.MoveNextButton">
      <summary>
            The 'Move Next' button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputNavigatorItems.MoveLastButton">
      <summary>
            The 'Move Last' button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputNavigatorItems.AddNewButton">
      <summary>
            The 'Add New' button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputNavigatorItems.DeleteButton">
      <summary>
            The 'Delete' button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputNavigatorItems.ApplyButton">
      <summary>
            The 'Apply' button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputNavigatorItems.CancelButton">
      <summary>
            The 'Cancel' button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.InputNavigatorItems.All">
      <summary>
            Includes all available items.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.Enums.Visibility">
      <summary>
            Specifies the display state of a component.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.Visibility.Visible">
      <summary>
            Display the component.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.Visibility.Hidden">
      <summary>
            Do not display the element, but reserve space for the element in layout.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.Enums.Visibility.Collapsed">
      <summary>
            Do not display the element, and do not reserve space for it in layout.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.DropDownItemClickedEventHandler">
      <summary>
            Represents the method that will handle the <see cref="E:C1.Win.C1Input.C1SplitButton.DropDownItemClicked" /> event of the <see cref="T:C1.Win.C1Input.C1SplitButton" /> class.
            </summary>
      <param name="sender">
      </param>
      <param name="e">
      </param>
    </member>
    <member name="T:C1.Win.C1Input.DropDownItemClickedEventArgs">
      <summary>
            Provides data for <see cref="E:C1.Win.C1Input.C1SplitButton.DropDownItemClicked" /> event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DropDownItemClickedEventArgs.#ctor(C1.Win.C1Input.DropDownItem)">
      <summary>
            Initializes a new instance of the DropDownItemClickedEventArgs class for the specified control.
            </summary>
      <param name="clickedItem">The <see cref="T:C1.Win.C1Input.DropDownItem" /> to store in this event.</param>
    </member>
    <member name="P:C1.Win.C1Input.DropDownItemClickedEventArgs.ClickedItem">
      <summary>
            Returns the clicked item.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.CharType">
      <summary>
              Defines the category of a Unicode character.  
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.CharType.OtherChar">
      <summary>
              Indicates that the character is not of a particular category.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.CharType.Control">
      <summary>
              Indicates that the character is a control code.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.CharType.Numeric">
      <summary>
              Indicates that the character is a numeric digit.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.CharType.MathSymbol">
      <summary>
              Indicates that the character is a mathematical symbol.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.CharType.Symbol">
      <summary>
              Indicates that the character is a symbol.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.CharType.Punctuation">
      <summary>
              Indicates that the character is a punctuation. ( Open &amp; Close )
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.CharType.Space">
      <summary>
              Indicates that the character is a space character.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.CharType.UpperCase">
      <summary>
              Indicates that the character is an upper case letter.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.CharType.LowerCase">
      <summary>
              Indicates that the character is a lower case letter.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.CharType.Katakana">
      <summary>
              Indicates that the character is a Japanese Katakana character.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.CharType.Hiragana">
      <summary>
              Indicates that the character is a Japanese Hiragana character.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.CharType.FarEastPunctation">
      <summary>
              Indicates that the character is a CJK punctuation.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.CharType.Hangul">
      <summary>
              Indicates that the character is a Hangal character.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.CharType.FullWidth">
      <summary>
              Indicates that the character is of full width.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.DropDownCalendar">
      <summary>
            Represents drop down calendar form.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DropDownCalendar.#ctor">
      <summary>
            Creates a new instance of <see cref="T:C1.Win.C1Input.DropDownCalendar" /> class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DropDownCalendar.OnPostChanges(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.DropDownCalendar.Dispose(System.Boolean)">
      <summary>
            Clean up any resources being used.
            </summary>
      <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
    </member>
    <member name="P:C1.Win.C1Input.DropDownCalendar.Calendar">
      <summary>
            Calendar.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.DropDownCalculator">
      <summary>
            Represents the class of dropdown calculator form.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DropDownCalculator.#ctor">
      <summary>
            Initializes a new instance of the DropDownCalculator class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.DropDownCalculator.Dispose(System.Boolean)">
      <summary>
            Releases the resources used by the DropDownCalculator.
            </summary>
      <param name="disposing">
        <b>true</b> to release both managed and unmanaged resources; <b>false</b> to release only unmanaged resources.</param>
    </member>
    <member name="M:C1.Win.C1Input.DropDownCalculator.OnPostChanges(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.DropDownCalculator.OnCancelChanges(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.DropDownCalculator.OnKeyDown(System.Windows.Forms.KeyEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.DropDownCalculator.OnKeyUp(System.Windows.Forms.KeyEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.DropDownCalculator.OnKeyPress(System.Windows.Forms.KeyPressEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.DropDownCalculator.OnHandleCreated(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.DropDownCalculator.OnVisibleChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.DropDownCalculator.OnOpen(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.DropDownCalculator.WndProc(System.Windows.Forms.Message@)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="m">
      </param>
    </member>
    <member name="P:C1.Win.C1Input.DropDownCalculator.Calculator">
      <summary>
            The calculator used in a DropDownCalculator form.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.ErrorActionEnum">
      <summary>
            Used by ErrorInfo.ErrorAction property.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.ErrorActionEnum.None">
      <summary>
            Control value remains as the user typed it.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.ErrorActionEnum.SetValueOnError">
      <summary>
            Control value is set to ValueOnError.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.ErrorActionEnum.ResetValue">
      <summary>
            Control value is reset to the value control had before entering edit mode.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.ErrorActionEnum.ThrowException">
      <summary>
            Control value is reset to the value control had before entering edit mode, and an exception is thrown.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.ErrorInfo">
      <summary>
            Settings affecting error handling.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.ErrorInfo.ShouldSerializeValueOnError">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.ErrorInfo.Reset">
      <summary>
            Resets whole ErrorInfo property in base control
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ErrorInfo.BeepOnError">
      <summary>
            If True, the control beeps signaling an error. Default: False.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ErrorInfo.ShowErrorMessage">
      <summary>
            If True (default), the standard error message is shown.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ErrorInfo.ErrorMessage">
      <summary>
            Error message shown in the standard message box and/or in the exception.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ErrorInfo.ErrorMessageCaption">
      <summary>
            The text to display in the title bar of the error message box.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ErrorInfo.ErrorAction">
      <summary>
            Enumerated value that determines what action is performed on the control value when an error occurs.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ErrorInfo.CanLoseFocus">
      <summary>
            Determines whether or not the control is allowed to lose focus after the error.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ErrorInfo.ValueOnError">
      <summary>
            Value used to reset the control if ErrorAction = SetValueOnError.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ErrorInfo.ValueOnErrorIsDbNull">
      <summary>
            Boolean property used to set ValueOnError to DbNull (only necessary at design time).
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ErrorInfo.ErrorProvider">
      <summary>
            Gets or sets an ErrorProvider object used to indicate error state of the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ErrorInfo.C1SuperErrorProvider">
      <summary>
            Gets or sets an C1SuperErrorProvider object used to indicate error state of the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ErrorInfo.InnerException">
      <summary>
            Run-time-only read-only property returning the original exception object that caused the error.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ErrorInfo.DataType">
      <summary>
            Type of the Value property.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.FormatErrorEventHandler">
      <summary>
            Represents the method that handles a FormatError event.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A FormatErrorEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Input.FormatErrorEventArgs">
      <summary>
            Provides data for a FormatError event.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.FormatErrorEventArgs.Value">
      <summary>
            Source value for formatting.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.FormatErrorEventArgs.Target">
      <summary>
            Formatting target (ForEdit or ForDisplay).
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.FormatErrorEventArgs.Text">
      <summary>
            The text to show in the control. Your code in FormatError event can set this argument to a text
            you want to show in the control in case of a formatting error.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.FormatErrorEventArgs.FormatInfo">
      <summary>
            FormatInfo object that fired the event.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.FormatErrorEventArgs.ErrorMessage">
      <summary>
            Error description shown as the tooltip in the ErrorProvider icon, if ErrorInfo.ErrorProvider property is set.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.ErrorReasonEnum">
      <summary>
            Used by ValidationErrorEventArgs.Reason and ValidationException.Reason properties.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.ErrorReasonEnum.NoErrors">
      <summary>
            All validation checks were successful.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.ErrorReasonEnum.EditMaskError">
      <summary>
            Value does not match the edit mask.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.ErrorReasonEnum.PreValidationError">
      <summary>
            Value was rejected before parsing (in PreValidation).
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.ErrorReasonEnum.ParsingError">
      <summary>
            Parsing failed.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.ErrorReasonEnum.PostValidationError">
      <summary>
            Value rejected after parsing (in PostValidation).
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.ErrorReasonEnum.DataSourceError">
      <summary>
            Value rejected by the data source, setting the data source field to that value failed.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.ValidationErrorEventHandler">
      <summary>
            Represents the method that handles a ValidationError event.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A ValidationErrorEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Input.ValidationErrorEventArgs">
      <summary>
            Provides data for a ValidationError event.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ValidationErrorEventArgs.Reason">
      <summary>
            Validation phase on which the error has been detected.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ValidationErrorEventArgs.Text">
      <summary>
            The input text that caused the error.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ValidationErrorEventArgs.Value">
      <summary>
            If Reason = ErrorReasonEnum.PostValidationError, this is the value that caused the error. Otherwise, this property is null or DBNull.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ValidationErrorEventArgs.ErrorInfo">
      <summary>
            Error information.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.ValidationException">
      <summary>
            An exception that follows the ValidationError event if ValidationErrorEventArgs.ErrorInfo is set to ErrorActionEnum.ThrowException.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ValidationException.Sender">
      <summary>
            Control that fired the exception.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ValidationException.Reason">
      <summary>
            Validation phase on which the error has been detected.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ValidationException.Text">
      <summary>
            The input text that caused the error.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ValidationException.Value">
      <summary>
            The typed value that caused the error.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.DataSourceException">
      <summary>
            An exception thrown by C1Input when a control's Value property is set, but the new value is rejected by the data source, 
            setting the data field to that value is cancelled by the data source throwing an exception.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DataSourceException.Sender">
      <summary>
            Control that caused the exception.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.DataSourceException.Value">
      <summary>
            Current value in the control that caused the exception.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.FormatInfoInheritFlags">
      <summary>
            Used by FormatInfo.Inherit property.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatInfoInheritFlags.None">
      <summary>
            Indicates that none of the properties are inherited from the control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatInfoInheritFlags.FormatType">
      <summary>
            Indicates that the FormatType property is inherited from the control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatInfoInheritFlags.CustomFormat">
      <summary>
            Indicates that the CustomFormat property is inherited from the control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatInfoInheritFlags.NullText">
      <summary>
            Indicates that the NullText property is inherited from the control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatInfoInheritFlags.EmptyAsNull">
      <summary>
            Indicates that the EmptyAsNull property is inherited from the control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatInfoInheritFlags.TrimStart">
      <summary>
            Indicates that the TrimStart property is inherited from the control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatInfoInheritFlags.TrimEnd">
      <summary>
            Indicates that the TrimEnd property is inherited from the control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatInfoInheritFlags.CalendarType">
      <summary>
            Indicates that the CalendarType property is inherited from the control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatInfoInheritFlags.All">
      <summary>
            Indicates that values of FormatType, CustomFormat, NullText, EmptyAsNull, TrimStart, TrimEnd, CalendarType properties are inherited from the control.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.FormatTargetEnum">
      <summary>
            Used by FormatEventArgs.Target property.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatTargetEnum.ForDisplay">
      <summary>
            Formatting (or Formatted) event was called while the control is not in edit mode.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatTargetEnum.ForEdit">
      <summary>
            Formatting (or Formatted) event was called while the control is in edit mode.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.FormatEventHandler">
      <summary>
            Represents the method that handles Formatting or Formatted events.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A FormatEventArgs object that contains the event data</param>
    </member>
    <member name="T:C1.Win.C1Input.FormatEventArgs">
      <summary>
            Provides data for Formatting or Formatted events.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.FormatEventArgs.Value">
      <summary>
            Source value for formatting.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.FormatEventArgs.Target">
      <summary>
            Formatting target (ForEdit or ForDisplay).
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.FormatEventArgs.Text">
      <summary>
            The out parameter for the resulting string (in Formatting), or the in parameter for the formatted string (in Formatted).
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.FormatEventArgs.FormatInfo">
      <summary>
            FormatInfo object that fired the event.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.FormatEventArgs.ErrorMessage">
      <summary>
            The out parameter for specifying error description if Succeeded is set to False.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.FormatEventArgs.Succeeded">
      <summary>
            Formatting result (assigned in the event code in Formatting). Default: True
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.FormatInfo">
      <summary>
            Settings for data formatting, converting data to string.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.FormatInfo.ShouldSerializeFormatType">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.FormatInfo.ResetFormatType">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.FormatInfo.ShouldSerializeCustomFormat">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.FormatInfo.ResetCustomFormat">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.FormatInfo.ShouldSerializeNullText">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.FormatInfo.ResetNullText">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.FormatInfo.ShouldSerializeEmptyAsNull">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.FormatInfo.ResetEmptyAsNull">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.FormatInfo.ShouldSerializeTrimStart">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.FormatInfo.ResetTrimStart">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.FormatInfo.ShouldSerializeTrimEnd">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.FormatInfo.ResetTrimEnd">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.FormatInfo.Reset">
      <summary>
            Resets whole FormatInfo property of base control
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.FormatInfo.Format(System.Object)">
      <summary>
            Formats a value, converts it to a string.
            </summary>
      <param name="value">Typed value to convert to a string</param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.FormatInfo.Format(System.Object,C1.Win.C1Input.FormatTypeEnum,System.Globalization.CultureInfo)">
      <summary>
            Formats a value, converts it to a string.
            </summary>
      <param name="value">Typed value to convert to a string.</param>
      <param name="formatType">Format method or specifier used for conversion.</param>
      <param name="culture">Culture used for conversion.</param>
      <returns>
      </returns>
    </member>
    <member name="P:C1.Win.C1Input.FormatInfo.Inherit">
      <summary>
            Manages inheritance of the FormatInfo properties from the host control properties.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.FormatInfo.FormatType">
      <summary>
            Enumeration value determining the formatting method, including standard .NET format specifiers, custom and programmatic formatting.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.FormatInfo.CalendarType">
      <summary>
            Calendar, used to format date.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.FormatInfo.CustomFormat">
      <summary>
            Custom format specifier used if FormatType = FormatTypeEnum.CustomFormat.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.FormatInfo.NullText">
      <summary>
            String representing a DbNull value.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.FormatInfo.EmptyAsNull">
      <summary>
            If True, empty strings are interpreted as null values (DbNull).
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.FormatInfo.TrimStart">
      <summary>
            If True, leading spaces are removed.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.FormatInfo.TrimEnd">
      <summary>
            If True, trailing spaces are removed.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.MaskInfoInheritFlags">
      <summary>
            Used by MaskInfo.Inherit property.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.MaskInfoInheritFlags.None">
      <summary>
            Indicates that none of the properties are inherited from the control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.MaskInfoInheritFlags.CaseSensitive">
      <summary>
            Indicates that the CaseSensitive property is inherited from the control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.MaskInfoInheritFlags.EmptyAsNull">
      <summary>
            Indicates that the EmptyAsNull property is inherited from the control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.MaskInfoInheritFlags.ErrorMessage">
      <summary>
            Indicates that the ErrorMessage property is inherited from the control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.MaskInfoInheritFlags.All">
      <summary>
            Indicates that all properties are inherited from the control.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.ShowLiteralsEnum">
      <summary>
            Used by MaskInfo.ShowLiterals property.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.ShowLiteralsEnum.ShowAlways">
      <summary>
            The whole mask is shown when editing begins (empty spaces filled with PromptChar).
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.ShowLiteralsEnum.FreeFormatEntry">
      <summary>
            The mask is validated on exit (no literals or prompt chars displayed automatically).
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.ShowLiteralsEnum.WhenNextStarted">
      <summary>
            The literals will be inserted after the user enters the first character of the field that follows the literals.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.ShowLiteralsEnum.WhenPrevFilled">
      <summary>
            Literals that follows the input mask will be inserted after the user enters the last character in the current field.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.MaskInfo">
      <summary>
            Contains edit mask settings.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.MaskInfo.ShouldSerializeEditMask">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.MaskInfo.ShouldSerializeCaseSensitive">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.MaskInfo.ResetCaseSensitive">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.MaskInfo.ShouldSerializeEmptyAsNull">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.MaskInfo.ResetEmptyAsNull">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.MaskInfo.ShouldSerializeErrorMessage">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.MaskInfo.ResetErrorMessage">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.MaskInfo.ShouldSerializeCustomPlaceholders">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.MaskInfo.ResetCustomPlaceholders">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.MaskInfo.IsMaskedMode">
      <summary>
            Returns True if EditMask is non-empty and the control is in edit mode.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.MaskInfo.Reset">
      <summary>
            Resets whole MaskInfo property of base control
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.MaskInfo.ResetContent">
      <summary>
            Resets the text to the string representing a DBNull value.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.MaskInfo.GetDisplayText(System.String,System.Boolean)">
      <summary>
            Returns a string formatted for display using edit mask.
            </summary>
      <param name="content">String containing characters entered by the user, including those on optional positions that were left blank (skipped).</param>
      <param name="skipBlanks">If set to True, the result will omit blank positions. If set to False, the resulting text will contain PromptChar on blank positions</param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.MaskInfo.GetStoredText(System.String)">
      <summary>
            Returns the string that will be stored in the database, in accordance with current values of SaveLiterals, SaveBlanks and StoredEmptyChar properties.
            </summary>
      <param name="content">String containing characters entered by the user, including those on optional positions that were left blank (skipped).</param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.MaskInfo.GetContent(System.String,System.Boolean,System.Boolean,System.Char)">
      <summary>
            Returns the string containing characters entered by the user, including those on optional positions that were left blank (skipped).
            </summary>
      <param name="text">Text to parse.</param>
      <param name="textContainsLiterals">Same as MaskInfo.SaveLiterals property.</param>
      <param name="textContainsBlanks">Same as MaskInfo.SaveBlanks property.</param>
      <param name="blankCharacter">Same as MaskInfo.StoredEmptyChar property</param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.MaskInfo.IsValid">
      <summary>
            Returns True if the input string is valid with respect to the edit mask.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="P:C1.Win.C1Input.MaskInfo.EditMask">
      <summary>
            The edit mask string restricting user input.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.MaskInfo.Inherit">
      <summary>
            Manages inheritance of the MaskInfo properties from the host control properties.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.MaskInfo.CaseSensitive">
      <summary>
            True if comparison with mask literals is case-sensitive; otherwise, False.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.MaskInfo.CopyWithLiterals">
      <summary>
            If True, text copied to the clipboard includes literals.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.MaskInfo.EmptyAsNull">
      <summary>
            If True, empty strings are interpreted as null values (DbNull).
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.MaskInfo.ErrorMessage">
      <summary>
            Error message shown in the standard message box and/or if an exception occurs.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.MaskInfo.AutoTabWhenFilled">
      <summary>
            If True, focus automatically moves to the next control when the mask is filled.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.MaskInfo.PromptChar">
      <summary>
            Character displayed on empty mask positions in edit mode.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.MaskInfo.SkipOptional">
      <summary>
            If True (default), optional mask positions are automatically skipped until the first position allowing the typed character.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.MaskInfo.SaveLiterals">
      <summary>
            If True (default), the stored text includes literals.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.MaskInfo.SaveBlanks">
      <summary>
            If True, the stored text includes blank positions as StoredEmptyChar.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.MaskInfo.StoredEmptyChar">
      <summary>
            Character stored in empty mask positions.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.MaskInfo.ShowLiterals">
      <summary>
            Literal display method.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.MaskInfo.CustomPlaceholders">
      <summary>
            The collection of user-defined mask characters.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.MaskInfo.EditablePositionCount">
      <summary>
            Returns the number of editable positions in the edit mask.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.MaskInfo.Content">
      <summary>
            Text entered by the user without literals (mask literals stripped).
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.MaskInfo.EffectiveText">
      <summary>
            Text in a readable format, with literals and without blanks.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.MaskInfo.StoredContent">
      <summary>
            The stored string obtained from the user input string.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.CharCategory">
      <summary>
            Defines rules to filter keyboard input.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.CharCategory.SingleByte">
      <summary>
            Only single byte characters are allowed.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.CharCategory.DoubleByte">
      <summary>
            Only double byte characters are allowed.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.CharCategory.Letter">
      <summary>
            Letters allowed.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.CharCategory.Number">
      <summary>
            Numbers are allowed.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.CharCategory.Symbol">
      <summary>
            Symbols are allowed.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.CharCategory.UpperCase">
      <summary>
            Only upper case letters are allowed.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.CharCategory.LowerCase">
      <summary>
            Only lower case letters are allowed.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.CharCategory.Katakana">
      <summary>
            Only katakana is allowed.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.CharCategory.Hiragana">
      <summary>
            Only hiragana is allowed.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.CharCategory.BigKana">
      <summary>
            Only big kana letters are allowed.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.CharCategory.AutoConvert">
      <summary>
            Auto conversion if possible.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.NumericInputKeyFlags">
      <summary>
            Used by C1TextBox.NumericInputKeys property.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NumericInputKeyFlags.None">
      <summary>
            Indicates that none of the Input Keys are used.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NumericInputKeyFlags.F2">
      <summary>
            Indicates that F2 enters negative infinity (-Infinity).
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NumericInputKeyFlags.F3">
      <summary>
            Indicates that F3 enters positive infinity (+Infinity).
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NumericInputKeyFlags.F4">
      <summary>
            Indicates that F4 enters the "not a number" value (NaN).
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NumericInputKeyFlags.F9">
      <summary>
            Indicates that F9 toggles the sign of the displayed number.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NumericInputKeyFlags.Minus">
      <summary>
            Indicates that '-' makes the displayed number negative.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NumericInputKeyFlags.Plus">
      <summary>
            Indicates that '+' makes the displayed number positive.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NumericInputKeyFlags.Decimal">
      <summary>
            Indicates that Decimal key enters a decimal separator regardless of culture settings.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NumericInputKeyFlags.X">
      <summary>
            Indicates that the X key starts entry of the exponent part in scientific-notation numbers.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NumericInputKeyFlags.Standard">
      <summary>
            This value is a combination of the F9, Minus, Plus, Decimal, and X values.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NumericInputKeyFlags.All">
      <summary>
            This value is a combination of all enum values
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.ParseInfoInheritFlags">
      <summary>
            Used by ParseInfo.Inherit property.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.ParseInfoInheritFlags.None">
      <summary>
            Indicates that none of the properties are inherited from the control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.ParseInfoInheritFlags.CaseSensitive">
      <summary>
            Indicates that the value of the CaseSensitive property is inherited from the control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.ParseInfoInheritFlags.FormatType">
      <summary>
            Indicates that the value of the FormatType property is inherited from the control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.ParseInfoInheritFlags.CustomFormat">
      <summary>
            Indicates that the value of the CustomFormat property is inherited from the control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.ParseInfoInheritFlags.NullText">
      <summary>
            Indicates that the value of the NullText property is inherited from the control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.ParseInfoInheritFlags.EmptyAsNull">
      <summary>
            Indicates that the value of the EmptyAsNull property is inherited from the control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.ParseInfoInheritFlags.ErrorMessage">
      <summary>
            Indicates that the value of the ErrorMessage property is inherited from the control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.ParseInfoInheritFlags.TrimStart">
      <summary>
            Indicates that the value of the TrimStart property is inherited from the control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.ParseInfoInheritFlags.TrimEnd">
      <summary>
            Indicates that the value of the TrimEnd property is inherited from the control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.ParseInfoInheritFlags.All">
      <summary>
            Indicates that all properties are inherited from the control.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.NumberStyleFlags">
      <summary>
            Used by ParseInfo.NumberStyle property.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NumberStyleFlags.None">
      <summary>
            Indicates that none of the bit styles are allowed.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NumberStyleFlags.AllowCurrencySymbol">
      <summary>
            Indicates that the numeric string is parsed as currency if it contains a currency symbol; otherwise, it is parsed as a number.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NumberStyleFlags.AllowDecimalPoint">
      <summary>
            Indicates that the numeric string can have a decimal point.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NumberStyleFlags.AllowExponent">
      <summary>
            Indicates that the numeric string can be in exponential notation.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NumberStyleFlags.AllowHexSpecifier">
      <summary>
            Indicates that the numeric string can have notation that signifies that the number is hexadecimal.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NumberStyleFlags.AllowLeadingSign">
      <summary>
            Indicates that the numeric string can have a leading sign.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NumberStyleFlags.AllowLeadingWhite">
      <summary>
            Indicates that a leading white-space character is ignored during parsing.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NumberStyleFlags.AllowParentheses">
      <summary>
            Indicates that the numeric string can have one pair of parentheses enclosing the number.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NumberStyleFlags.AllowThousands">
      <summary>
            Indicates that the numeric string can have group separators.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NumberStyleFlags.AllowTrailingSign">
      <summary>
            Indicates that the numeric string can have a trailing sign.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NumberStyleFlags.AllowTrailingWhite">
      <summary>
            Indicates that trailing white-space character must be ignored during parsing.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NumberStyleFlags.Any">
      <summary>
            Indicates that all the AllowXXX bit styles are used.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NumberStyleFlags.Currency">
      <summary>
            Indicates all styles except AllowExponent and AllowHexSpecifier.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NumberStyleFlags.Float">
      <summary>
            Indicates that the AllowLeadingWhite, AllowTrailingWhite, AllowLeadingSign,
            AllowDecimalPoint, and AllowExponent styles are used.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NumberStyleFlags.HexNumber">
      <summary>
            Indicates that the AllowLeadingWhite, AllowTrailingWhite, and AllowHexSpecifier styles are used.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NumberStyleFlags.Integer">
      <summary>
            Indicates that the AllowLeadingWhite, AllowTrailingWhite, and AllowLeadingSign styles are used.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.NumberStyleFlags.Number">
      <summary>
            Indicates that the AllowLeadingWhite, AllowTrailingWhite, AllowLeadingSign,
            AllowTrailingSign, AllowDecimalPoint, and AllowThousands styles are used.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.DateTimeStyleFlags">
      <summary>
            Used by ParseInfo.DateTimeStyle property.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.DateTimeStyleFlags.None">
      <summary>
            Indicates that the default formatting options must be used.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.DateTimeStyleFlags.AllowInnerWhite">
      <summary>
            Indicates that extra white space characters in the middle of the string must be ignored during parsing.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.DateTimeStyleFlags.AllowLeadingWhite">
      <summary>
            Indicates that leading white space characters must be ignored during parsing.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.DateTimeStyleFlags.AllowTrailingWhite">
      <summary>
            Indicates that trailing white space characters must be ignored during parsing.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.DateTimeStyleFlags.AllowWhiteSpaces">
      <summary>
            Indicates that extra white space characters anywhere in the string must be ignored during parsing.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.DateTimeStyleFlags.NoCurrentDateDefault">
      <summary>
            Indicates that, if the parsed string contains only the time and not the date, the parsing methods
            assume the Gregorian date with year = 1, month = 1, and day = 1. If this value is not used, the current date is assumed.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.ParseEventHandler">
      <summary>
            Represents the method that handles Parsing or Parsed events.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A ParseEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Input.ParseEventArgs">
      <summary>
            Provides data for Parsing or Parsed events.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ParseEventArgs.Text">
      <summary>
            Source string for parsing.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ParseEventArgs.DestinationType">
      <summary>
            Data type to convert the string to.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ParseEventArgs.Succeeded">
      <summary>
            Parse result (assigned in the event code in Parsing). Default: True
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ParseEventArgs.Value">
      <summary>
            Property to save the result to (in Parsing) or to get the resulting value from (in Parsed).
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ParseEventArgs.ErrorInfo">
      <summary>
            Error information in case of parsing failure. Can be set in event code in Parsing.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ParseEventArgs.ParseInfo">
      <summary>
            ParseInfo object that fired the event.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.ParseInfo">
      <summary>
            Contains settings affecting parsing, that is, converting a string to the required data type.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.ParseInfo.ShouldSerializeCaseSensitive">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.ParseInfo.ResetCaseSensitive">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.ParseInfo.ShouldSerializeFormatType">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.ParseInfo.ResetFormatType">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.ParseInfo.ShouldSerializeCustomFormat">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.ParseInfo.ResetCustomFormat">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.ParseInfo.ShouldSerializeNullText">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.ParseInfo.ResetNullText">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.ParseInfo.ShouldSerializeEmptyAsNull">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.ParseInfo.ResetEmptyAsNull">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.ParseInfo.ShouldSerializeErrorMessage">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.ParseInfo.ResetErrorMessage">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.ParseInfo.ShouldSerializeTrimStart">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.ParseInfo.ResetTrimStart">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.ParseInfo.ShouldSerializeTrimEnd">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.ParseInfo.ResetTrimEnd">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.ParseInfo.Reset">
      <summary>
            Resets whole ParseInfo property of base control
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.ParseInfo.IsTextDbNull(System.String,System.Globalization.CultureInfo)">
      <summary>
            Returns True if the argument represents a null value (DBNull).
            </summary>
      <param name="text">The string to parse.</param>
      <param name="culture">Culture used in parsing.</param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.ParseInfo.ParseFixed(System.String,C1.Win.C1Input.FormatTypeEnum,System.Globalization.CultureInfo)">
      <summary>
            Converts the text to a Decimal value using a fixed numeric format.
            </summary>
      <param name="text">The string to parse.</param>
      <param name="formatType">Format type used in parsing.</param>
      <param name="culture">Culture used in parsing.</param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.ParseInfo.ParseFloat(System.String,C1.Win.C1Input.FormatTypeEnum,System.Globalization.CultureInfo)">
      <summary>
            Converts the text to a Double value using a float numeric format.
            </summary>
      <param name="text">The string to parse.</param>
      <param name="formatType">Format type used in parsing.</param>
      <param name="culture">Culture used in parsing.</param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.ParseInfo.ParseInteger(System.String,C1.Win.C1Input.FormatTypeEnum,System.Globalization.CultureInfo)">
      <summary>
            Converts the text to Int64 value using an integer numeric format.
            </summary>
      <param name="text">
      </param>
      <param name="formatType">
      </param>
      <param name="culture">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.ParseInfo.ParseBoolean(System.String,C1.Win.C1Input.FormatTypeEnum,System.Globalization.CultureInfo)">
      <summary>
            Converts the text to a Boolean value.
            </summary>
      <param name="text">The string to parse.</param>
      <param name="formatType">Format type used in parsing.</param>
      <param name="culture">Culture used in parsing.</param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.ParseInfo.ParseDateTime(System.String,C1.Win.C1Input.FormatTypeEnum,System.Globalization.CultureInfo)">
      <summary>
            Converts text to a value using one of DateTime formats.
            </summary>
      <param name="text">The string to parse.</param>
      <param name="formatType">Format type used in parsing.</param>
      <param name="culture">Culture used in parsing.</param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.ParseInfo.Parse(System.String,System.Type,System.Object@,C1.Win.C1Input.ErrorInfo)">
      <summary>
            Converts text to the specified data type.
            </summary>
      <param name="text">The string to parse.</param>
      <param name="destinationType">Type to which the string is converted.</param>
      <param name="result">Conversion result.</param>
      <param name="errorInfo">Object detailing error information, if an error occurred.</param>
      <returns>
      </returns>
    </member>
    <member name="P:C1.Win.C1Input.ParseInfo.Inherit">
      <summary>
            Manages inheritance of the ParseInfo properties from the host control properties.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ParseInfo.CaseSensitive">
      <summary>
            True if string comparisons are case-sensitive; otherwise, False.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ParseInfo.FormatType">
      <summary>
            The format used for parsing.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ParseInfo.CustomFormat">
      <summary>
            Custom format specifier (in parsing used for DateTime and Boolean types only).
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ParseInfo.NullText">
      <summary>
            The string representing DbNull value.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ParseInfo.EmptyAsNull">
      <summary>
            If True, empty strings are interpreted as null values (DbNull).
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ParseInfo.ErrorMessage">
      <summary>
            Error message shown in the standard message box and/or in the exception.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ParseInfo.TrimStart">
      <summary>
            If True, leading spaces are removed before parsing.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ParseInfo.TrimEnd">
      <summary>
            If True, trailing spaces are removed before parsing.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ParseInfo.NumberStyle">
      <summary>
            Determines the styles (flags) permitted in input strings representing numbers.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ParseInfo.DateTimeStyle">
      <summary>
            Determines the styles (flags) permitted in input strings representing date/time.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.PostValidationInheritFlags">
      <summary>
            Used by PostValidation.Inherit property.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.PostValidationInheritFlags.None">
      <summary>
            Indicates that none of the properties are inherited from the control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.PostValidationInheritFlags.CaseSensitive">
      <summary>
            Indicates that the value of the CaseSensitive property is inherited from the control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.PostValidationInheritFlags.ErrorMessage">
      <summary>
            Indicates that the value of the ErrorMessage property is inherited from the control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.PostValidationInheritFlags.All">
      <summary>
            Indicates that all properties are inherited from the control.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.PostValidationTypeEnum">
      <summary>
            Used by PostValidation.Validation property.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.PostValidationTypeEnum.ValuesAndIntervals">
      <summary>
            Validation using Values and Intervals.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.PostValidationTypeEnum.PostValidatingEvent">
      <summary>
            Using the PostValidating event.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.PostValidationEventHandler">
      <summary>
            Represents the method that handles PostValidating or PostValidated events.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A PostValidationEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Input.PostValidationEventArgs">
      <summary>
            Provides data for PostValidating or PostValidated events.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.PostValidationEventArgs.Value">
      <summary>
            The value to validate.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.PostValidationEventArgs.PostValidation">
      <summary>
            PostValidation object that fired the event.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.PostValidationEventArgs.ErrorInfo">
      <summary>
            Error information in case of validation failure. Can be set in event code in PostValidating.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.PostValidationEventArgs.Succeeded">
      <summary>
            Validation result (assigned in the event code in PostValidating). Default: True
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.ValueInterval">
      <summary>
            An interval in a collection of intervals of possible values used in post-validation.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.ValueInterval.#ctor">
      <summary>
            Initializes a new instance of the ValueInterval class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.ValueInterval.#ctor(System.Object,System.Object,System.Boolean,System.Boolean)">
      <summary>
            ValueInterval Constructor.
            </summary>
      <param name="minValue">MinValue property value.</param>
      <param name="maxValue">MaxValue property value.</param>
      <param name="includeMin">IncludeMin property value.</param>
      <param name="includeMax">IncludeMax property value.</param>
    </member>
    <member name="M:C1.Win.C1Input.ValueInterval.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>
            Initializes a new instance of the ValueInterval class.
            </summary>
      <param name="info">
      </param>
      <param name="context">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.ValueInterval.ShouldSerializeMinValue">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.ValueInterval.ShouldSerializeMaxValue">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="P:C1.Win.C1Input.ValueInterval.DataType">
      <summary>
            The type of lower and upper bounds (taken from the control’s DataType).
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ValueInterval.MinValue">
      <summary>
            Lower bound.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ValueInterval.MaxValue">
      <summary>
            Upper bound.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ValueInterval.UseMinValue">
      <summary>
            If False (default), the lower bound is negative infinity.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ValueInterval.UseMaxValue">
      <summary>
            If False (default), the upper bound is positive infinity.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ValueInterval.IncludeMin">
      <summary>
            If True (default), the lower bound is included.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.ValueInterval.IncludeMax">
      <summary>
            If True (default), the upper bound is included.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.ValueIntervalCollection">
      <summary>
            A collection of ValueInterval objects used by a PostValidation object. 
            The collection is used if the Validation = PostValidationTypeEnum.ValuesAndIntervals
            or if the user calls the ValidateValuesAndIntervals method.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.ValueIntervalCollection.AddRange(C1.Win.C1Input.ValueInterval[])">
      <summary>
            Adds the elements of an array to the end of the collection.
            </summary>
      <param name="c">The array whose elements should be added to the end of the collection.</param>
    </member>
    <member name="M:C1.Win.C1Input.ValueIntervalCollection.Add(C1.Win.C1Input.ValueInterval)">
      <summary>
            Adds a new value interval to the collection.
            </summary>
      <param name="vi">The ValueInterval to add.</param>
    </member>
    <member name="M:C1.Win.C1Input.ValueIntervalCollection.Insert(System.Int32,C1.Win.C1Input.ValueInterval)">
      <summary>
            Inserts a new value interval to the collection at the specified position.
            </summary>
      <param name="index">The zero-based index at which ValueInterval should be inserted.</param>
      <param name="vi">The ValueInterval to insert.</param>
    </member>
    <member name="M:C1.Win.C1Input.ValueIntervalCollection.OnInsert(System.Int32,System.Object)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="index">
      </param>
      <param name="value">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.ValueIntervalCollection.OnSet(System.Int32,System.Object,System.Object)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="index">
      </param>
      <param name="oldValue">
      </param>
      <param name="newValue">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.ValueIntervalCollection.Contains(C1.Win.C1Input.ValueInterval)">
      <summary>
            Returns True if the collection contains the specific value interval, False otherwise.
            </summary>
      <param name="vi">The ValueInterval to locate in the collection.</param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.ValueIntervalCollection.IndexOf(C1.Win.C1Input.ValueInterval)">
      <summary>
            Determines the index of a specific value interval in the collection, return -1 if this item not found.
            </summary>
      <param name="vi">The ValueInterval to locate in the collection.</param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.ValueIntervalCollection.Remove(C1.Win.C1Input.ValueInterval)">
      <summary>
            Removes a specific value interval from the collection.
            </summary>
      <param name="vi">The ValueInterval to remove from the collection.</param>
    </member>
    <member name="M:C1.Win.C1Input.ValueIntervalCollection.CopyTo(C1.Win.C1Input.ValueInterval[],System.Int32)">
      <summary>
            Copies elements of the collection to an array starting at a particular array index.
            </summary>
      <param name="array">The one-array that is the destination of the elements copied from the collection. </param>
      <param name="index">The zero-based index in array at which copying begins.</param>
    </member>
    <member name="P:C1.Win.C1Input.ValueIntervalCollection.Item(System.Int32)">
      <summary>
            Gets the collection element at the specified index.
            </summary>
      <param name="index">
      </param>
      <returns>
      </returns>
    </member>
    <member name="T:C1.Win.C1Input.PostValidation">
      <summary>
            Validating the typed value after parsing, after the input string has been converted to the DataType.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.PostValidation.ShouldSerializeCaseSensitive">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.PostValidation.ResetCaseSensitive">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.PostValidation.ShouldSerializeErrorMessage">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.PostValidation.ResetErrorMessage">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.PostValidation.ShouldSerializeValues">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.PostValidation.ShouldSerializeValuesExcluded">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.PostValidation.ShouldSerializeIntervals">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.PostValidation.Reset">
      <summary>
            Resets whole PostValidation property of base control
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.PostValidation.ValidateValuesAndIntervals(System.Object,C1.Win.C1Input.ErrorInfo)">
      <summary>
            Performs validation of the value against the collections of Values, ValuesExcluded and Intervals.
            </summary>
      <param name="value">The value to validate.</param>
      <param name="errorInfo">Error information filled in case of failure.</param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.PostValidation.Validate(System.Object,C1.Win.C1Input.ErrorInfo)">
      <summary>
            Performs validation using the current settings of the PostValidation object. Returns true if vaidation was successful.
            </summary>
      <param name="value">The value to validate.</param>
      <param name="errorInfo">Error information filled in case of failure.</param>
      <returns>
      </returns>
    </member>
    <member name="P:C1.Win.C1Input.PostValidation.Validation">
      <summary>
            Validation method.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.PostValidation.AllowDbNull">
      <summary>
            Determines whether it is possible to enter DbNull value in the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.PostValidation.Inherit">
      <summary>
            Manages inheritance of the PostValidation properties from the host control properties.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.PostValidation.CaseSensitive">
      <summary>
            True if string comparisons are case-sensitive; otherwise, False.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.PostValidation.ErrorMessage">
      <summary>
            Error message shown in the standard message box and/or in the exception.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.PostValidation.Values">
      <summary>
            Predefined values used for matching the input value.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.PostValidation.ValuesExcluded">
      <summary>
            List of values that are not permitted as input value.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.PostValidation.Intervals">
      <summary>
            Collection of intervals. The input value must belong to one of these intervals.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.PreValidationInheritFlags">
      <summary>
            Used by PreValidation.Inherit property.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.PreValidationInheritFlags.None">
      <summary>
            Indicates that none of the properties are inherited from the control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.PreValidationInheritFlags.CaseSensitive">
      <summary>
            Indicates that the value of the CaseSensitive property is inherited from the control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.PreValidationInheritFlags.ErrorMessage">
      <summary>
            Indicates that the value of the ErrorMessage property is inherited from the control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.PreValidationInheritFlags.TrimStart">
      <summary>
            Indicates that the value of the TrimStart property is inherited from the control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.PreValidationInheritFlags.TrimEnd">
      <summary>
            Indicates that the value of the TrimEnd property is inherited from the control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.PreValidationInheritFlags.All">
      <summary>
            Indicates that all properties are inherited from the control.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.PreValidationTypeEnum">
      <summary>
            Used by PreValidation.Validation property.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.PreValidationTypeEnum.ExactList">
      <summary>
            The PatternString property contains a list of possible values separated by the ItemSeparator.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.PreValidationTypeEnum.PreValidatingEvent">
      <summary>
            Using the PreValidating event.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.PreValidationTypeEnum.Wildcards">
      <summary>
            The PatternString property contains a list of wildcard patterns separated by the ItemSeparator. 
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.PreValidationTypeEnum.RegexPattern">
      <summary>
            The PatternString property contains a regular expression.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.RegexOptionFlags">
      <summary>
            Used by PreValidation.RegexOptions property.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.RegexOptionFlags.None">
      <summary>
            Specifies that no options are set.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.RegexOptionFlags.ECMAScript">
      <summary>
            Enables ECMAScript-compliant behavior for the expression. This flag can be used only
            in conjunction with the IgnoreCase, Multiline, and Compiled flags.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.RegexOptionFlags.ExplicitCapture">
      <summary>
            Specifies that the only valid captures are explicitly named or numbered groups of the form.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.RegexOptionFlags.IgnorePatternWhitespace">
      <summary>
            Eliminates unescaped white space from the pattern and enables comments marked with #.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.RegexOptionFlags.Multiline">
      <summary>
            Multiline mode. Changes the meaning of ^ and $ so they match at the beginning and end, 
            respectively, of any line, and not just the beginning and end of the entire string.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.RegexOptionFlags.RightToLeft">
      <summary>
            Specifies that the search will be from right to left instead of from left to right.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.RegexOptionFlags.Singleline">
      <summary>
            Specifies single-line mode. Changes the meaning of the dot (.) so it matches every character (instead of every character except \n).
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.PreValidationEventHandler">
      <summary>
            Represents the method that handles PreValidating or PreValidated events.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A PreValidationEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Input.PreValidationEventArgs">
      <summary>
            Provides data for PreValidating or PreValidated events.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.PreValidationEventArgs.Text">
      <summary>
            The text to validate.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.PreValidationEventArgs.PreValidation">
      <summary>
            PreValidation object that fired the event.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.PreValidationEventArgs.ErrorInfo">
      <summary>
            Error information in case of validation failure. Can be set in event code in PreValidating.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.PreValidationEventArgs.Succeeded">
      <summary>
            Validation result (assigned in the event code in PreValidating). Default: True.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.PreValidation">
      <summary>
            Validating the input string entered by the user, before parsing.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.PreValidation.ShouldSerializeCaseSensitive">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.PreValidation.ResetCaseSensitive">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.PreValidation.ShouldSerializeErrorMessage">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.PreValidation.ResetErrorMessage">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.PreValidation.ShouldSerializeTrimStart">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.PreValidation.ResetTrimStart">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.PreValidation.ShouldSerializeTrimEnd">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.PreValidation.ResetTrimEnd">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.PreValidation.ShouldSerializeCustomPlaceholders">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.PreValidation.ResetCustomPlaceholders">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.PreValidation.Reset">
      <summary>
            Resets whole PreValidation property of base control
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.PreValidation.MatchExactList(System.String,C1.Win.C1Input.ErrorInfo)">
      <summary>
            Returns True if the input text matches one of the exact list items specified in PatternString.
            </summary>
      <param name="text">The input string to match.</param>
      <param name="errorInfo">Error information filled in case of failure.</param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.PreValidation.MatchWildcardPattern(System.String,System.String)">
      <summary>
            Returns True if the input text matches the specified wildcard pattern passed as argument.
            </summary>
      <param name="text">The input string to match.</param>
      <param name="pattern">The wildcard pattern.</param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.PreValidation.MatchWildcardPatterns(System.String,C1.Win.C1Input.ErrorInfo)">
      <summary>
            Returns True if the input text matches at least one of the wildcard patterns specified in PatternString.
            </summary>
      <param name="text">The input string to match.</param>
      <param name="errorInfo">Error information filled in case of failure.</param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.PreValidation.MatchRegex(System.String,C1.Win.C1Input.ErrorInfo)">
      <summary>
            Returns True if the input text matches the regular expression pattern specified in PatternString.
            </summary>
      <param name="text">The input string to match.</param>
      <param name="errorInfo">Error information filled in case of failure.</param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.PreValidation.Validate(System.String,C1.Win.C1Input.ErrorInfo)">
      <summary>
            Returns True if the text successfully passes validation using the current settings of the PreValidation object.
            </summary>
      <param name="text">The input string to validate.</param>
      <param name="errorInfo">Error information filled in case of failure.</param>
      <returns>
      </returns>
    </member>
    <member name="P:C1.Win.C1Input.PreValidation.Validation">
      <summary>
            Validation method.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.PreValidation.Inherit">
      <summary>
            Manages inheritance of the PreValidation properties from the host control properties.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.PreValidation.CaseSensitive">
      <summary>
            True if string comparisons are case-sensitive; otherwise, False.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.PreValidation.ErrorMessage">
      <summary>
            Error message shown in the standard message box and/or in the exception.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.PreValidation.TrimStart">
      <summary>
            If True, leading spaces are removed before validation.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.PreValidation.TrimEnd">
      <summary>
            If True, trailing spaces are removed before validation.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.PreValidation.PatternString">
      <summary>
            String containing the validation pattern.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.PreValidation.ItemSeparator">
      <summary>
            String separating list items in PatternString.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.PreValidation.CustomPlaceholders">
      <summary>
            The collection of user-defined characters to use in a wildcard pattern.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.PreValidation.RegexOptions">
      <summary>
            Settings affecting regular expression matching.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.NullableDateTimeEventHandler">
      <summary>
            Represents the method that handles DateValueChanged and DateValueSelected events.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A NullableDateTimeEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Input.NullableDateTimeEventArgs">
      <summary>
            Provides data for DateValueChanged and DateValueSelected events.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.NullableDateTimeEventArgs.Date">
      <summary>
            The date value.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.NullableDateTimeEventArgs.DateIsNull">
      <summary>
            Set to True if the date value is null.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.CreationTime">
      <summary>
            Determines when drop down form will be created.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.CreationTime.Default">
      <summary>
            At the host control creation time.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.CreationTime.OnFirstCall">
      <summary>
            On first call of the drop down form.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.LengthUnit">
      <summary>
               Defines the <b>LengthUnit</b> enumeration.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.LengthUnit.Char">
      <summary>
              Indicates the calculational unit is a .Net character when calculating the length of the string.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.LengthUnit.Byte">
      <summary>
              Indicates the calculational unit is a byte when calculating the length of the string.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.VisualStyle">
      <summary>
            Determines the visual style of a control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.VisualStyle.Custom">
      <summary>
            Custom style.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.VisualStyle.System">
      <summary>
            Standard system style.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.VisualStyle.Office2007Blue">
      <summary>
            MS Office 2007 Blue color scheme.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.VisualStyle.Office2007Black">
      <summary>
            MS Office 2007 Black color scheme.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.VisualStyle.Office2007Silver">
      <summary>
            MS Office 2007 Silver color scheme.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.VisualStyle.Office2010Blue">
      <summary>
            MS Office 2010 Blue color scheme.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.VisualStyle.Office2010Black">
      <summary>
            MS Office 2010 Black color scheme.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.VisualStyle.Office2010Silver">
      <summary>
            MS Office 2010 Silver color scheme.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.FormatTypeEnum">
      <summary>
            Used by FormatType property.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatTypeEnum.DefaultFormat">
      <summary>
            Conversion using TypeConverter.ConvertToString().
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatTypeEnum.UseEvent">
      <summary>
            Conversion performed by user code in the Formatting (or Parsing) event.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatTypeEnum.CustomFormat">
      <summary>
            Formatting uses the string assigned to the CustomFormat property.
            Parsing uses NumberStyle, DateTimeStyle, and CustomFormat properties.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatTypeEnum.GeneralNumber">
      <summary>
            The number is converted to the most compact decimal form, using fixed point or scientific notation.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatTypeEnum.Currency">
      <summary>
            The number is converted to a string that represents a currency amount.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatTypeEnum.FixedPoint">
      <summary>
            The number is converted to a string of the form “-ddd.ddd…” where each 'd' indicates a digit (0-9). 
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatTypeEnum.StandardNumber">
      <summary>
            The number is converted to a string of the form "-d,ddd,ddd.ddd…", where each 'd' indicates a digit (0-9).
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatTypeEnum.Percent">
      <summary>
            The number is converted to a string that represents a percent as defined by the NumberFormatInfo.PercentNegativePattern property
            or the NumberFormatInfo.PercentPositivePattern property. 
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatTypeEnum.Scientific">
      <summary>
            The number is converted to a string of the form "-d.ddd…E+ddd" or "-d.ddd…e+ddd", where each 'd' indicates a digit (0-9). 
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatTypeEnum.RoundTrip">
      <summary>
            The round-trip specifier guarantees that a numeric value converted to a string will be parsed back
            into the same numeric value. This format is supported by floating-point types only.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatTypeEnum.Integer">
      <summary>
            Displays number as a string that contains the value of the number in Decimal (base 10) format. This format is supported for integral types only.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatTypeEnum.Hexadecimal">
      <summary>
            The number is converted to a string of hexadecimal digits. This format is supported for integral types only.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatTypeEnum.YesNo">
      <summary>
            Converts to Boolean and shows No for false, Yes for true.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatTypeEnum.TrueFalse">
      <summary>
            Converts to Boolean and shows True or False.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatTypeEnum.OnOff">
      <summary>
            Converts to Boolean and shows Off for false, On for true.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatTypeEnum.GeneralDate">
      <summary>
            General date/time pattern (short time).
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatTypeEnum.LongDate">
      <summary>
            Displays a date according to specified CultureInfo's long date format.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatTypeEnum.MediumDate">
      <summary>
            Displays a date using the medium date format ("dd-MMM-yy").
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatTypeEnum.ShortDate">
      <summary>
            Displays a date using specified CultureInfo's short date format.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatTypeEnum.LongTime">
      <summary>
            Displays a time using your locale's long time format; includes hours, minutes, seconds.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatTypeEnum.MediumTime">
      <summary>
            Displays time in 12-hour format using hours and minutes and the AM/PM designator ("hh:mm tt").
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatTypeEnum.ShortTime">
      <summary>
            Displays a time using the 24-hour format, for example, 17:45.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatTypeEnum.LongDateShortTime">
      <summary>
            Displays the long date and short time according to specified CultureInfo's format.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatTypeEnum.LongDateLongTime">
      <summary>
            Displays the long date and long time according to specified CultureInfo's format.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatTypeEnum.ShortDateShortTime">
      <summary>
            Displays the short date and short time according to specified CultureInfo's format.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatTypeEnum.ShortDateLongTime">
      <summary>
            Displays the short date and long time according to specified CultureInfo's format.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatTypeEnum.MonthAndDay">
      <summary>
            Displays the month and the day of a date.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatTypeEnum.DateAndTimeGMT">
      <summary>
            Formats the date and time as Greenwich Mean Time (GMT).
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatTypeEnum.DateTimeSortable">
      <summary>
            Formats the date and time as a sortable index.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatTypeEnum.GMTSortable">
      <summary>
            Formats the date and time as a GMT sortable index.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatTypeEnum.LongDateTimeGMT">
      <summary>
            Formats the date and time with the long date and long time as GMT.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.FormatTypeEnum.YearAndMonth">
      <summary>
            Formats the date as the year and month.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.TypeRestrictionEnum">
      <summary>
            This enum is for internal use only
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.TypeRestrictionEnum.AnyType">
      <summary>
            Any type of data.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.TypeRestrictionEnum.Numbers">
      <summary>
            Only number data type.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.TypeRestrictionEnum.DateTime">
      <summary>
            Only DateTime data type.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.DropDownFormAlignmentEnum">
      <summary>
            Used by DropDownForm.DropDownAlign and C1DropDownControl.DropDownFormAlign properties.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.DropDownFormAlignmentEnum.Default">
      <summary>
            In a C1DropDownControl, the alignment of the dropdown form is determined by the 
            DropDownAlign property of the drop down form.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.DropDownFormAlignmentEnum.Left">
      <summary>
            Left alignment.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.DropDownFormAlignmentEnum.Center">
      <summary>
            Center alignment.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.DropDownFormAlignmentEnum.Right">
      <summary>
            Right alignment.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.VerticalAlignEnum">
      <summary>
            Used by C1TextBox.VerticalAlign property.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.VerticalAlignEnum.Top">
      <summary>
            Text is aligned with the top of the enclosing control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.VerticalAlignEnum.Middle">
      <summary>
            Text is aligned with the center of the enclosing control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.VerticalAlignEnum.Bottom">
      <summary>
            Text is aligned with the bottom of the enclosing control.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.DaylightTimeAdjustmentEnum">
      <summary>
            Used by C1Label.DaylightTimeAdjustment and C1TextBox.DaylightTimeAdjustment properties.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.DaylightTimeAdjustmentEnum.NoAdjustments">
      <summary>
            No daylight-saving time adjustments.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.DaylightTimeAdjustmentEnum.AdvanceGMTOffsetForTheDaylightSavingTime">
      <summary>
            Use this option if the GMTOffset property corresponds to the standard time offset.
            For the daylight-saving time GMTOffset should be advanced by an hour.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.DaylightTimeAdjustmentEnum.SetGMTOffsetBackForTheStandardTime">
      <summary>
            Use this option if the GMTOffset property is set to the daylight time offset. 
            So, for the standard time it should be set back by an hour.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.InitialSelectionEnum">
      <summary>
            Used by C1TextBox.InitialSelection property.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.InitialSelectionEnum.SelectAll">
      <summary>
            The whole text is selected.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.InitialSelectionEnum.CaretAtStart">
      <summary>
            Nothing selected, the caret is moved at the beginning of the text.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.InitialSelectionEnum.CaretAtEnd">
      <summary>
            Nothing selected, the caret is moved at the end of the text.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.ValueChangedBehaviorEnum">
      <summary>
            Used by C1TextBox.ValueChangedBehavior.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.ValueChangedBehaviorEnum.Default">
      <summary>
            The ValueChanged event is fired only when the Value property changes.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.ValueChangedBehaviorEnum.FireOnAnyChange">
      <summary>
            The ValueChanged event is fired on any text change.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.ExitOnLeftRightKey">
      <summary>
              Specifies the action for the left or right arrow key.
            </summary>
      <remarks>
              The left or right key causes focus to be lost when the caret is at the left-most or right-most position.
            </remarks>
    </member>
    <member name="F:C1.Win.C1Input.ExitOnLeftRightKey.None">
      <summary>
              Specifies no action.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.ExitOnLeftRightKey.Left">
      <summary>
              Specifies to move to the previous control when pressing left or Ctrl+left keys on the first character of the control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.ExitOnLeftRightKey.Right">
      <summary>
              Specifies to move to the next control when pressing right or Ctrl+right keys on the last character of the control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Input.ExitOnLeftRightKey.Both">
      <summary>
              Specifies to move to the previous or next control when pressing the left or right or Ctrl+left or Ctrl+right keys on the first or last character of the control.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.CultureInfoSetupEventHandler">
      <summary>
            Represents the method that handles a CultureInfoSetup event.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A CultureInfoSetupEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Input.CultureInfoSetupEventArgs">
      <summary>
            Provides data for a CultureInfoSetup event.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.CultureInfoSetupEventArgs.CultureInfo">
      <summary>
            Regional settings.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.Margins">
      <summary>
            Represents the four margins around a control’s content.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.Margins.#ctor">
      <summary>
            Initializes a new instance of the Margins class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.Margins.#ctor(System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>
            Initializes a new instance of the Margins class.
            </summary>
      <param name="left">
      </param>
      <param name="right">
      </param>
      <param name="top">
      </param>
      <param name="bottom">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.Margins.Clone">
      <summary>
            Creates an identical copy of the current Margins.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.Margins.Equals(System.Object)">
      <summary>
            Overloaded. Determines whether two Object instances are equal.
            </summary>
      <param name="obj">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.Margins.GetHashCode">
      <summary>
            Serves as a hash function for a particular type, suitable for use in hashing algorithms and data structures like a hash table.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.Margins.ToString">
      <summary>
            Converts this Margins to a human readable string.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.Margins.Set(System.Int32,System.Int32,System.Int32,System.Int32)">
      <summary>
            Overloaded. Sets the margin values.
            </summary>
      <param name="left">
      </param>
      <param name="right">
      </param>
      <param name="top">
      </param>
      <param name="bottom">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.Margins.Set(System.Drawing.Printing.Margins)">
      <summary>
            Overloaded. Sets the margin values.
            </summary>
      <param name="printMargins">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.Margins.Reset">
      <summary>
            Sets all margins to zero.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.Margins.ShouldSerialize">
      <summary>
            Returns true if at least one margin differs from the default zero value. Otherwise, returns false.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="P:C1.Win.C1Input.Margins.Left">
      <summary>
            Gets or sets the left margin, in pixels.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.Margins.Right">
      <summary>
            Gets or sets the right margin, in pixels.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.Margins.Top">
      <summary>
            Gets or sets the top margin, in pixels.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.Margins.Bottom">
      <summary>
            Gets or sets the bottom margin, in pixels.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.CustomPlaceholder">
      <summary>
            Custom user-defined characters (placeholders) used in edit mask and in wildcard patterns in pre-validation 
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.CustomPlaceholder.#ctor">
      <summary>
            Initializes a new instance of the CustomPlaceholder class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.CustomPlaceholder.#ctor(System.Char,System.Boolean,System.String,System.Boolean,System.Boolean)">
      <summary>
            Initializes a new instance of the CustomPlaceholder class.
            </summary>
      <param name="placeholder">The value of the Placeholder property.</param>
      <param name="optionalPlace">The value of the Optional property.</param>
      <param name="lookupChars">The value of the LookupChars property.</param>
      <param name="caseSensitive">The value of the CaseSensitive property.</param>
      <param name="excluding">The value of the Excluding property.</param>
    </member>
    <member name="M:C1.Win.C1Input.CustomPlaceholder.#ctor(System.Int32,System.Boolean,System.String,System.Boolean,System.Boolean)">
      <summary>
            Initializes a new instance of the CustomPlaceholder class.
            </summary>
      <param name="placeholder">The value of the Placeholder property.</param>
      <param name="optionalPlace">The value of the Optional property.</param>
      <param name="lookupChars">The value of the LookupChars property.</param>
      <param name="caseSensitive">The value of the CaseSensitive property.</param>
      <param name="excluding">The value of the Excluding property.</param>
    </member>
    <member name="M:C1.Win.C1Input.CustomPlaceholder.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
      <summary>
            Initializes a new instance of the CustomPlaceholder class.
            </summary>
      <param name="info">
      </param>
      <param name="context">
      </param>
    </member>
    <member name="P:C1.Win.C1Input.CustomPlaceholder.Placeholder">
      <summary>
            The special character used as a user-defined placeholder.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.CustomPlaceholder.Optional">
      <summary>
            True if character is optional and can be omitted.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.CustomPlaceholder.LookupChars">
      <summary>
            The list (string) of characters matching the placeholder (see also Excluding).
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.CustomPlaceholder.CaseSensitive">
      <summary>
            True if string comparison is case-sensitive; otherwise, False.
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.CustomPlaceholder.Excluding">
      <summary>
            Specifies whether the LookupChars are interpreted as allowed or disallowed characters.
            </summary>
    </member>
    <member name="T:C1.Win.C1Input.PlaceholderCollection">
      <summary>
            A collection of CustomPlaceholder objects.
            </summary>
    </member>
    <member name="M:C1.Win.C1Input.PlaceholderCollection.AddRange(C1.Win.C1Input.CustomPlaceholder[])">
      <summary>
            Adds the special characters of an ICollection to the end of the PlaceholderCollection.
            </summary>
      <param name="c">The ICollection whose elements should be added.</param>
    </member>
    <member name="M:C1.Win.C1Input.PlaceholderCollection.CheckItemBeforeAdding(C1.Win.C1Input.CustomPlaceholder)">
      <summary>
            Checks validity of the placeholder
            </summary>
      <param name="cph">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.PlaceholderCollection.Add(C1.Win.C1Input.CustomPlaceholder)">
      <summary>
            Adds a new special character to the collection.
            </summary>
      <param name="cph">The CustomPlaceholder to add.</param>
    </member>
    <member name="M:C1.Win.C1Input.PlaceholderCollection.Insert(System.Int32,C1.Win.C1Input.CustomPlaceholder)">
      <summary>
            Inserts a new special character to the collection at the specified position.
            </summary>
      <param name="index">The zero-based index at which CustomPlaceholder should be inserted.</param>
      <param name="cph">The CustomPlaceholder to insert.</param>
    </member>
    <member name="M:C1.Win.C1Input.PlaceholderCollection.OnInsert(System.Int32,System.Object)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="index">
      </param>
      <param name="value">
      </param>
    </member>
    <member name="M:C1.Win.C1Input.PlaceholderCollection.Contains(System.Char)">
      <summary>
            Determines whether a character is in the placeholder collection.
            </summary>
      <param name="placeholder">The character argument.</param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.PlaceholderCollection.IndexOf(System.Char)">
      <summary>
            Returns the index of a character in the placeholder collection, or –1 if it does not belong to the collection.
            </summary>
      <param name="placeholder">The character argument.</param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.PlaceholderCollection.Remove(System.Char)">
      <summary>
            Removes a specific character from the collection.
            </summary>
      <param name="placeholder">The character to remove from the collection.</param>
    </member>
    <member name="M:C1.Win.C1Input.PlaceholderCollection.CopyTo(C1.Win.C1Input.CustomPlaceholder[],System.Int32)">
      <summary>
            Copies elements of the collection to an array starting at a particular array index.
            </summary>
      <param name="array">The one-array that is the destination of the elements copied from the collection. </param>
      <param name="index">The zero-based index in array at which copying begins.</param>
    </member>
    <member name="M:C1.Win.C1Input.PlaceholderCollection.GetItem(System.Char)">
      <summary>
            Returns the CustomPlaceholder object corresponding to the character argument, or null if it does not belong to the collection.
            </summary>
      <param name="placeholder">The character argument.</param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Input.PlaceholderCollection.OnChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="E:C1.Win.C1Input.PlaceholderCollection.Changed">
      <summary>
            Occurs when PlaceholderCollection is changed 
            </summary>
    </member>
    <member name="P:C1.Win.C1Input.PlaceholderCollection.Item(System.Int32)">
      <summary>
            Gets or sets the the special character at the specified index.
            </summary>
      <param name="index">The zero-based index of the element to get or set.</param>
      <returns>
      </returns>
    </member>
  </members>
</doc>