﻿Imports System.Data
Imports System.Data.SqlClient
'Imports System.IO
Public Class Conn_Cls
    Public SqlConn, SqlConnPOS As New SqlConnection
    Public ServerName, ServerPass, DBName, UID, POS, AdminPass As String
    Public MSGTitle As String = "IFC SCM POS"
    Dim Encrypt As New Encrypt_Cls
    Public IsUp, <PERSON><PERSON><PERSON> As Boolean


    Sub New()
        Try
            If IsUps Then Return
            ConnPOS()
            ConnSCM()
            'AdminPass = "M@trixNeo@424342"
            'AdminPass = "P@ssw0rd"
            AdminPass = "123456"
            '  End If
        Catch ex As Exception
            MessageBox.Show(ex.ToString)
        End Try


    End Sub

    'Public Function FillDictionary() As Dictionary(Of Integer, String)
    '    Dim Dic As New Dictionary(Of Integer, String)
    '    Dic.Clear()

    '    Dic.Add(1, "Matrix POS")
    '    Dic.Add(2, "Smart POS")
    '    Return Dic

    'End Function

    Public Sub ConnPOS()
        Dim P As String = System.IO.Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().CodeBase)
        P = New Uri(P).LocalPath
        Dim PathFile As String = P & "\PathPOS.txt"
        Dim SourcePath As String = System.IO.Path.GetFileName(PathFile)

        '   If System.IO.File.Exists("jjh") = False Then Return

        If System.IO.File.Exists(PathFile) = False Then
            IsUps = True
            Dim F As New LoginServer_Frm(PathFile, True)
            IsUps = False
            F.ShowDialog()

            If F.IsSelect = False Then End
        End If
        Dim objectR As New System.IO.StreamReader(P & "\PathPOS.txt")

        ServerName = objectR.ReadLine()
        ServerPass = objectR.ReadLine()
        DBName = objectR.ReadLine()
        UID = objectR.ReadLine()
        POS = objectR.ReadLine()
        objectR.Close()
        If ServerName = "manually" Then
            Ismanuel = True
        End If
        If Ismanuel = False Then
            ServerName = Encrypt.DEncrypt(ServerName, Encrypt.EncryptKey)
            ServerPass = Encrypt.DEncrypt(ServerPass, Encrypt.EncryptKey)
            DBName = Encrypt.DEncrypt(DBName, Encrypt.EncryptKey)
            UID = Encrypt.DEncrypt(UID, Encrypt.EncryptKey)
            POS = Encrypt.DEncrypt(POS, Encrypt.EncryptKey)
            SqlConnPOS = New SqlConnection("Data Source='" & ServerName & "' ;Initial Catalog='" & DBName & "';Persist Security Info=True;MultipleActiveResultSets=True;User ID='" & UID & "';Password='" & ServerPass & "'")
            '    SqlConn = New SqlConnection("Data Source='" & ServerName & "' ;Network Library=DBMSSOCN;Initial Catalog='" & DBName & "';Integrated Security=SSPI; Persist Security Info=True;MultipleActiveResultSets=True;User ID=sa;Password='" & ServerPass & "'")
        End If
    End Sub


    Public Sub ConnSCM()
        Dim P As String = System.IO.Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().CodeBase)
        P = New Uri(P).LocalPath
        Dim PathFile As String = P & "\Path.txt"
        Dim SourcePath As String = System.IO.Path.GetFileName(PathFile)


        If System.IO.File.Exists(PathFile) = False Then
            MessageBox.Show("The Server SCM Not Found Check The True Pass Of SCM Sysytem", "IFC SCM POS", MessageBoxButtons.OK, MessageBoxIcon.Information)
            Return
        End If
        Dim objectR As New System.IO.StreamReader(P & "\Path.txt")

        ServerName = objectR.ReadLine()
        ServerPass = objectR.ReadLine()
        DBName = objectR.ReadLine()
        UID = objectR.ReadLine()
        objectR.Close()
        If UID = "" Then UID = "sa"
        'ServerName = Encrypt.DEncrypt(ServerName, Encrypt.EncryptKey)
        'ServerPass = Encrypt.DEncrypt(ServerPass, Encrypt.EncryptKey)
        'DBName = Encrypt.DEncrypt(DBName, Encrypt.EncryptKey)

        SqlConn = New SqlConnection("Data Source='" & ServerName & "' ;Initial Catalog='" & DBName & "';Persist Security Info=True;MultipleActiveResultSets=True;User ID='" & UID & "';Password='" & ServerPass & "'")
        '    SqlConn = New SqlConnection("Data Source='" & ServerName & "' ;Network Library=DBMSSOCN;Initial Catalog='" & DBName & "';Integrated Security=SSPI; Persist Security Info=True;MultipleActiveResultSets=True;User ID=sa;Password='" & ServerPass & "'")

    End Sub
    Public Sub CloseConnection()
        If SqlConn.State = ConnectionState.Open Then
            SqlConn.Close()
        End If
    End Sub
    Public Sub OpenConnection()
        If SqlConn.State = ConnectionState.Closed Then
            SqlConn.Open()
        End If
    End Sub

    Public Sub CloseConnectionPOS()
        If SqlConnPOS.State = ConnectionState.Open Then
            SqlConnPOS.Close()
        End If
    End Sub
    Public Sub OpenConnectionPOS()
        If SqlConnPOS.State = ConnectionState.Closed Then
            SqlConnPOS.Open()
        End If
    End Sub

    Public Function TestConnectionPOS(SeName As String, DBNme As String, UPID As String, SePass As String) As Boolean
        On Error GoTo F
        SqlConnPOS = New SqlConnection("Data Source='" & SeName & "' ;Initial Catalog='" & DBNme & "';Persist Security Info=True;MultipleActiveResultSets=True;User ID='" & UPID & "';Password='" & SePass & "'")
        If SqlConnPOS.State = ConnectionState.Closed Then
            SqlConnPOS.Open()
            SqlConnPOS.Close()
            Return True
        End If
F:
        Return False
    End Function
    Public Sub EXECUT_Txt(Txt_ As String)
        Dim CMD As New SqlCommand
        CMD.CommandType = CommandType.Text
        CMD.Connection = SqlConn
        OpenConnection()

        '    MessageBox.Show("Open")
        CMD = New SqlCommand(Txt_, SqlConn)

        CMD.ExecuteNonQuery()
        '    MessageBox.Show("Done")

        CloseConnection()
        '   MessageBox.Show("Close")
    End Sub

    Public Sub EXECUT_TxtPOS(Txt_ As String)
        Dim CMD As New SqlCommand
        CMD.CommandType = CommandType.Text
        CMD.Connection = SqlConnPOS
        OpenConnectionPOS()

        '    MessageBox.Show("Open")
        CMD = New SqlCommand(Txt_, SqlConnPOS)

        CMD.ExecuteNonQuery()
        '    MessageBox.Show("Done")

        CloseConnectionPOS()
        '   MessageBox.Show("Close")
    End Sub
    Public Function SELECT_TXT(Txt_ As String) As DataTable
        Dim DT As New DataTable
        DT.Clear()
        Dim ADP As New SqlDataAdapter(Txt_, SqlConn)
        ADP.Fill(DT)
        Return DT
    End Function

    Public Function SELECT_TXTPOS(Txt_ As String) As DataTable
        Dim DT As New DataTable
        DT.Clear()
        Dim ADP As New SqlDataAdapter(Txt_, SqlConnPOS)
        ADP.Fill(DT)
        Return DT
    End Function

    Public Sub CallLoginPOS(Optional Ended As Boolean = True)
        Dim P As String = System.IO.Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().CodeBase)
        P = New Uri(P).LocalPath
        Dim PathFile As String = P & "\PathPOS.txt"
        Dim SourcePath As String = System.IO.Path.GetFileName(PathFile)

        '   If System.IO.File.Exists("jjh") = False Then Return

        '  If System.IO.File.Exists(PathFile) = False Then
        Dim F As New LoginServer_Frm(PathFile, Ended)
        F.ShowDialog()

        If F.IsSelect = False Then Return
        '   End If
        Dim objectR As New System.IO.StreamReader(P & "\PathPOS.txt")

        ServerName = objectR.ReadLine()
        ServerPass = objectR.ReadLine()
        DBName = objectR.ReadLine()
        UID = objectR.ReadLine()
        POS = objectR.ReadLine()
        objectR.Close()

        ServerName = Encrypt.DEncrypt(ServerName, Encrypt.EncryptKey)
        ServerPass = Encrypt.DEncrypt(ServerPass, Encrypt.EncryptKey)
        DBName = Encrypt.DEncrypt(DBName, Encrypt.EncryptKey)
        UID = Encrypt.DEncrypt(UID, Encrypt.EncryptKey)
        POS = Encrypt.DEncrypt(POS, Encrypt.EncryptKey)
        SqlConnPOS = New SqlConnection("Data Source='" & ServerName & "' ;Initial Catalog='" & DBName & "';Persist Security Info=True;MultipleActiveResultSets=True;User ID='" & UID & "';Password='" & ServerPass & "'")
        '    SqlConn = New SqlConnection("Data Source='" & ServerName & "' ;Network Library=DBMSSOCN;Initial Catalog='" & DBName & "';Integrated Security=SSPI; Persist Security Info=True;MultipleActiveResultSets=True;User ID=sa;Password='" & ServerPass & "'")

    End Sub
End Class
