﻿Imports System.Security.Cryptography
Imports System.Text
Public Class Encrypt_Cls
    ' Dim Conn As New CLS_Conn
    Public DES As New TripleDESCryptoServiceProvider
    Public MD5 As New MD5CryptoServiceProvider
    Public EncryptKey As String = "Bb23081984"

    Function MD5Hash(value As String) As Byte()
        Return MD5.ComputeHash(ASCIIEncoding.ASCII.GetBytes(value))
    End Function

    Function Encrypt(StringInput As String, Key As String) As String
        DES.Key = MD5Hash(Key)
        DES.Mode = CipherMode.ECB

        Dim buffer As Byte() = ASCIIEncoding.ASCII.GetBytes(StringInput)

        Return Convert.ToBase64String(DES.CreateEncryptor().TransformFinalBlock(buffer, 0, buffer.Length))
    End Function

    Function DEncrypt(StringEncrypt As String, Key As String) As String
        On Error Resume Next
        DES.Key = MD5Hash(Key)
        DES.Mode = CipherMode.ECB

        Dim buffer As Byte() = Convert.FromBase64String(StringEncrypt)
        'ASCIIEncoding.ASCII.GetString (DES.CreateEncryptor().TransformFinalBlock(buffer, 0, buffer.Length))
        Return ASCIIEncoding.ASCII.GetString(DES.CreateDecryptor().TransformFinalBlock(buffer, 0, buffer.Length))
    End Function

    Public Function GetIPv4Address() As String
        GetIPv4Address = String.Empty
        Dim strHostName As String = System.Net.Dns.GetHostName()
        Dim iphe As System.Net.IPHostEntry = System.Net.Dns.GetHostEntry(strHostName)

        For Each ipheal As System.Net.IPAddress In iphe.AddressList
            If ipheal.AddressFamily = System.Net.Sockets.AddressFamily.InterNetwork Then
                GetIPv4Address = ipheal.ToString()
            End If
        Next

    End Function
End Class
