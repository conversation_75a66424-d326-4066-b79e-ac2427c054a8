<?xml version="1.0"?>
<doc>
  <assembly>
    <name>C1.Win.C1Command.2</name>
  </assembly>
  <members>
    <member name="T:C1.Util.FlagsEnumEditor">
      <summary>
            Represents the editor of enum properties with FlagsAttribute attribute.
            </summary>
    </member>
    <member name="M:C1.Util.FlagsEnumEditor.GetEditStyle(System.ComponentModel.ITypeDescriptorContext)">
      <summary>
            Gets the editor style used by the <see cref="M:C1.Util.FlagsEnumEditor.EditValue(System.ComponentModel.ITypeDescriptorContext,System.IServiceProvider,System.Object)" /> method.
            The override for <see cref="T:C1.Util.FlagsEnumEditor" /> returns <see cref="F:System.Drawing.Design.UITypeEditorEditStyle.DropDown" />.
            </summary>
    </member>
    <member name="M:C1.Util.FlagsEnumEditor.EditValue(System.ComponentModel.ITypeDescriptorContext,System.IServiceProvider,System.Object)">
      <summary>
            Edits the specified object's value using the editor style 
            indicated by the <see cref="M:C1.Util.FlagsEnumEditor.GetEditStyle(System.ComponentModel.ITypeDescriptorContext)" /> method. 
            </summary>
      <param name="context">
      </param>
      <param name="provider">
      </param>
      <param name="value">
      </param>
      <returns>
      </returns>
    </member>
    <member name="P:C1.Util.FlagsEnumEditor.IsDropDownResizable">
      <summary>
            Gets a value indicating whether drop-down editors should be resizable by the user. 
            The override for <see cref="T:C1.Util.FlagsEnumEditor" /> returns true;
            </summary>
    </member>
    <member name="T:C1.Util.Design.Floaties.FloatiesStrings">
      <summary>
            Contains localizable design-time strings.
            </summary>
    </member>
    <member name="T:C1.Design.UITypeEditorStrings">
      <summary>
            Static class containing UI strings used by the designer.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.UIStringsItemEventHandler">
      <summary>
            Represents a handler for an <see cref="T:C1.Win.C1Command.UIStrings" /> item related event.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.UIStringsItemEventArgs">
      <summary>
            Provides data for an <see cref="T:C1.Win.C1Command.UIStrings" /> item related event.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.UIStringsItemEventArgs.Key">
      <summary>
            Gets key of the item being added or changed.
            </summary>
      <value>The key.</value>
    </member>
    <member name="P:C1.Win.C1Command.UIStringsItemEventArgs.Value">
      <summary>
            Gets the string value.
            </summary>
      <value>The value.</value>
    </member>
    <member name="P:C1.Win.C1Command.UIStringsItemEventArgs.IsDefault">
      <summary>
            Gets a value indicating whether this instance is default.
            </summary>
      <value>
        <c>true</c> if this instance is default; otherwise, <c>false</c>.
            </value>
    </member>
    <member name="P:C1.Win.C1Command.UIStringsItemEventArgs.Description">
      <summary>
            Gets the description.
            </summary>
      <value>The description.</value>
    </member>
    <member name="T:C1.Win.C1Command.UIStrings">
      <summary>
            Represents a collection of end user visible UI strings.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.UIStrings.Add(System.Object,System.Int32,System.String,System.String)">
      <summary>
            Adds a string to the collection, specifying the ordinal.
            </summary>
      <param name="key">The key of the string.</param>
      <param name="ordinal">The ordinal of the string.</param>
      <param name="value">The string.</param>
      <param name="description">The description of the string.</param>
    </member>
    <member name="M:C1.Win.C1Command.UIStrings.Add(System.Object,System.String,System.String)">
      <summary>
            Adds a string to the collection in alphabetical order.
            </summary>
      <param name="key">The key of the string.</param>
      <param name="value">The string.</param>
      <param name="description">The description of the string.</param>
    </member>
    <member name="M:C1.Win.C1Command.UIStrings.AddInOrder(System.Object,System.String,System.String)">
      <summary>
            Adds a string to the collection, preserving the order.
            </summary>
      <param name="key">The key of the string.</param>
      <param name="value">The string.</param>
      <param name="description">The description of the string.</param>
    </member>
    <member name="M:C1.Win.C1Command.UIStrings.Reset">
      <summary>
            Sets all strings in collection to their default values.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.UIStrings.ShouldSerialize">
      <summary>
            Indicates whether any of the strings in the current collection
            have non-default values.
            </summary>
      <returns>
        <c>true</c> if any of the strings have non-default values, <c>false</c> otherwise.</returns>
    </member>
    <member name="M:C1.Win.C1Command.UIStrings.IsDefault(System.Object)">
      <summary>
            Tests whether a string in the collection has default value.
            </summary>
      <param name="key">The key of the string to test.</param>
      <returns>
        <c>true</c> if the string has default value, <c>false</c> otherwise.</returns>
    </member>
    <member name="M:C1.Win.C1Command.UIStrings.GetDescription(System.Object)">
      <summary>
            Returns the description of a string.
            </summary>
      <param name="key">The key of the string to get the description of.</param>
      <returns>The string's description</returns>
    </member>
    <member name="M:C1.Win.C1Command.UIStrings.Reset(System.Object)">
      <summary>
            Resets a string to its default value.
            </summary>
      <param name="key">The key of the string to reset.</param>
    </member>
    <member name="M:C1.Win.C1Command.UIStrings.GetKeyAt(System.Int32)">
      <summary>
            Returns the key of an item with the specified index.
            </summary>
      <param name="index">The item index.</param>
      <returns>The item's key.</returns>
    </member>
    <member name="M:C1.Win.C1Command.UIStrings.GetValueAt(System.Int32)">
      <summary>
            Gets the string by its index.
            </summary>
      <param name="index">The string index.</param>
      <returns>The string.</returns>
    </member>
    <member name="M:C1.Win.C1Command.UIStrings.SetValueAt(System.Int32,System.String)">
      <summary>
            Sets the value of a string with the specified index.
            </summary>
      <param name="index">The string index.</param>
      <param name="value">The new string value.</param>
    </member>
    <member name="M:C1.Win.C1Command.UIStrings.OnItemAdded(C1.Win.C1Command.UIStringsItemEventArgs)">
      <summary>
            Fires the <see cref="E:C1.Win.C1Command.UIStrings.ItemAdded" /> event.
            </summary>
      <param name="e">The event data.</param>
    </member>
    <member name="M:C1.Win.C1Command.UIStrings.OnItemChanged(C1.Win.C1Command.UIStringsItemEventArgs)">
      <summary>
            Fires the <see cref="E:C1.Win.C1Command.UIStrings.ItemChanged" /> event.
            </summary>
      <param name="e">The event data.</param>
    </member>
    <member name="M:C1.Win.C1Command.UIStrings.OnCollectionChanged(System.EventArgs)">
      <summary>
            Fires the <see cref="E:C1.Win.C1Command.UIStrings.CollectionChanged" /> event.
            </summary>
      <param name="e">The event data.</param>
    </member>
    <member name="P:C1.Win.C1Command.UIStrings.Count">
      <summary>
            Gets the number of elements contained in the collection.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.UIStrings.ItemAdded">
      <summary>
            Occurs when a new item is added to the collection.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.UIStrings.ItemChanged">
      <summary>
            Occurs when an item in the collection is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.UIStrings.CollectionChanged">
      <summary>
            Occurs when the collection has been changed.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.UIStrings.TypeConverter">
      <summary>
            Provides type conversion for the <see cref="T:C1.Win.C1Command.UIStrings" /> type.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.UIStrings.TypeConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
      <summary>
            For internal use.
            </summary>
      <param name="context">
      </param>
      <param name="culture">
      </param>
      <param name="value">
      </param>
      <param name="type">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.UIStrings.TypeConverter.GetProperties(System.ComponentModel.ITypeDescriptorContext,System.Object,System.Attribute[])">
      <summary>
            For internal use.
            </summary>
      <param name="context">
      </param>
      <param name="value">
      </param>
      <param name="attrFilter">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.UIStrings.TypeConverter.GetPropertiesSupported(System.ComponentModel.ITypeDescriptorContext)">
      <summary>
            For internal use.
            </summary>
      <param name="context">
      </param>
      <returns>
      </returns>
    </member>
    <member name="T:C1.Win.C1Command.BorderStyleEnum">
      <summary>
            Use members of this enumeration to set the value of the Style property of the C1Border class.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.BorderStyleEnum.None">
      <summary>
            No border.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.BorderStyleEnum.Flat">
      <summary>
            Flat border at dark color.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.BorderStyleEnum.Groove">
      <summary>
            Grooved border.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.BorderStyleEnum.Ridge">
      <summary>
            Ridged border.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.BorderStyleEnum.Inset">
      <summary>
            Inset border.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.BorderStyleEnum.Outset">
      <summary>
            Outset border.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.C1Border">
      <summary>
            This class allows you to add borders to a C1ToolBar object.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1Border.#ctor">
      <summary>
            Initializes a new instance of the C1Border class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1Border.#ctor(System.Windows.Forms.Control)">
      <summary>
            Initializes a new instance of the C1Border class.
            </summary>
      <param name="c">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1Border.FrameIt(System.Boolean)">
      <summary>
            Sets full border around toolbar or removes border.
            </summary>
      <param name="framed">If True, sets all borders, otherwise removes them</param>
    </member>
    <member name="M:C1.Win.C1Command.C1Border.IsFrameOn">
      <summary>
            Checks if border is full and visible.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1Border.IsFrameOff">
      <summary>
            Checks if border is not visible.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1Border.Draw(System.Drawing.Rectangle,System.Drawing.Graphics)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="r">
      </param>
      <param name="g">
      </param>
    </member>
    <member name="P:C1.Win.C1Command.C1Border.Style">
      <summary>
            Gets or sets the border style.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1Border.Width">
      <summary>
            Gets or sets the border width in pixels.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1Border.LeftEdge">
      <summary>
            Determines whether the border has a left edge.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1Border.TopEdge">
      <summary>
            Determines whether the border has a top edge.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1Border.RightEdge">
      <summary>
            Determines whether the border has a right edge.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1Border.BottomEdge">
      <summary>
            Determines whether the border has a bottom edge.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1Border.LightColor">
      <summary>
            Gets or sets the color of the border.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1Border.DarkColor">
      <summary>
            Gets or sets the color of the border.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.C1Border.BorderTypeConverter">
      <summary>
            Provides a type converter to convert C1Border objects to and from various other representations.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1Border.BorderTypeConverter.GetPropertiesSupported(System.ComponentModel.ITypeDescriptorContext)">
      <summary>
            Returns whether this object supports properties.
            </summary>
      <param name="context">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1Border.BorderTypeConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
      <summary>
            Converts the given value object to the specified type.
            </summary>
      <param name="context">
      </param>
      <param name="culture">
      </param>
      <param name="value">
      </param>
      <param name="type">
      </param>
      <returns>
      </returns>
    </member>
    <member name="P:C1.Win.C1Command.C1Border.BorderTypeConverter.Text">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.C1Command">
      <summary>
            The base for specialized commands such as button (a plain button, the only action is invoke),
            submenu (points to a menu or a toolbar), textbox (text entry), list, combo etc. etc.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1Command.Empty">
      <summary>
            Empty command
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1Command.#ctor">
      <summary>
            Initializes a new instance of the C1Command class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1Command.Dispose(System.Boolean)">
      <summary>
            Releases the resources used by the C1Command.
            </summary>
      <param name="disposing">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1Command.AddCommandLink(C1.Win.C1Command.C1CommandLink)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="cl">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1Command.RemoveCommandLink(C1.Win.C1Command.C1CommandLink)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="cl">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1Command.ShouldSerializeIcon">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1Command.ResetIcon">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1Command.ShouldSerializeImage">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1Command.ResetImage">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1Command.Invoke(C1.Win.C1Command.ClickEventArgs)">
      <summary>
            Invokes the command.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1Command.PerformClick">
      <summary>
            Generates a click event for the C1Command.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1Command.PerformSelect">
      <summary>
            Raises the Select event for the C1Command.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1Command.StateQuery">
      <summary>
            Fires the CommandStateQuery event, updates any changed properties.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1Command.OnClick(C1.Win.C1Command.ClickEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1Command.OnSelect(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1Command.OnTextChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1Command.OnImageChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1Command.OnEnabledChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1Command.OnVisibleChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1Command.OnCategoryChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1Command.OnToolTipChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1Command.OnShowShortcutChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1Command.OnShowTextAsToolTipChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1Command.OnCheckedChanged(C1.Win.C1Command.CheckedChangedEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1Command.OnPressedChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1Command.OnCommandStateQuery(C1.Win.C1Command.CommandStateQueryEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1Command.OnCommandLinkAdded(C1.Win.C1Command.CommandLinkEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1Command.OnCommandLinkRemoved(C1.Win.C1Command.CommandLinkEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1Command.OnDropDown(C1.Win.C1Command.DropDownEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1Command.OnBarPopDown">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1Command.LinkReceiveFocus(C1.Win.C1Command.C1CommandLink)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="cl">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1Command.RefreshBars(System.Boolean)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="measure">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1Command.InvalidateBars">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1Command.CheckNonEmpty">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1Command.AssignFrom(C1.Win.C1Command.C1Command)">
      <summary>
            Copy another command properties to this command
            </summary>
      <param name="cmd">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1Command.SetBoolean(System.Boolean@,System.Boolean)">
      <summary>
            Returns true if the change has actually been made
            and the control is not initializing (i.e. on change should be fired)
            </summary>
      <param name="oldValue">
      </param>
      <param name="newValue">
      </param>
      <returns>
      </returns>
    </member>
    <member name="P:C1.Win.C1Command.C1Command.Owner">
      <summary>
            C1CommandHolder containing this command.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1Command.Text">
      <summary>
            Gets or sets the text of the command.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1Command.Name">
      <summary>
            Gets or sets the name of the command.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1Command.ToolTipText">
      <summary>
            Gets or sets the tool tip text.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1Command.Category">
      <summary>
            Gets or sets the command category.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1Command.Visible">
      <summary>
            Gets or sets the command visibility.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1Command.Enabled">
      <summary>
            Gets or sets whether the command is enabled.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1Command.Checked">
      <summary>
            Gets or sets whether the command is checked.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1Command.Pressed">
      <summary>
            Gets or sets whether the command is pressed.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1Command.DropDown">
      <summary>
            Gets or sets whether the command has a drop down arrow when it is in a toolbar.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1Command.CheckAutoToggle">
      <summary>
            If true, the Checked property value is toggled automatically when this command is invoked.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1Command.Shortcut">
      <summary>
            Gets or sets the keyboard shortcut associated with the command.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1Command.ShortcutText">
      <summary>
            Gets or sets the shortcut text associated with the command.
            </summary>
      <remarks>
        <para>&gt;Use this property to localize shortcuts.</para>
        <para>If <see cref="P:C1.Win.C1Command.C1Command.Shortcut" /> property is empty or <see cref="P:C1.Win.C1Command.C1Command.ShowShortcut" /> is False, then the <see cref="P:C1.Win.C1Command.C1Command.ShortcutText" /> has no any effect.</para>
        <para>If <see cref="P:C1.Win.C1Command.C1Command.ShortcutText" /> property is empty, then default value is used for the shortcut.</para>
      </remarks>
    </member>
    <member name="P:C1.Win.C1Command.C1Command.ShowShortcut">
      <summary>
            Indicates whether the shortcut for this command will be shown.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1Command.ShowTextAsToolTip">
      <summary>
            Indicates whether this command's text will be shown as tooltip.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1Command.Icon">
      <summary>
            Gets or sets the command icon.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1Command.ImageIndex">
      <summary>
            Gets or sets the index of the command image (in C1CommandHolder.ImageList).
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1Command.Image">
      <summary>
            Gets or sets the command image.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1Command.UserData">
      <summary>
            Arbitrary data that can be associated with the command.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1Command.IsParent">
      <summary>
            Determines whether this command is a non-empty submenu. 
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1Command.Mnemonic">
      <summary>
            Returns the value of the mnemonic character, or the character following the ampersand, to be used as an access key.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1Command.C1ContextMenu">
      <summary>
            Gets or sets the C1ContextMenu component associated with this command.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1Command.Click">
      <summary>
            Event fired when the command is invoked by the user.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1Command.Select">
      <summary>
            Event fired when the command is selected by the user.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1Command.CheckedChanged">
      <summary>
            Event fired when the Checked property has changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1Command.PressedChanged">
      <summary>
            Event fired when the Pressed property has changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1Command.CommandStateQuery">
      <summary>
            Event fired when the command state has to be verified.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1Command.CommandLinkAdded">
      <summary>
            Event fired after a command link was linked to this command.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1Command.CommandLinkRemoved">
      <summary>
            Event fired after a command link was unlinked from this command.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1Command.TextChanged">
      <summary>
            Event fired when the Text property of command is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1Command.ImageChanged">
      <summary>
            Event fired when the Image or ImageIndex or Icon property of command is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1Command.EnabledChanged">
      <summary>
            Event fired when the Enabled property of command is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1Command.VisibleChanged">
      <summary>
            Event fired when the Visible property of command is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1Command.CategoryChanged">
      <summary>
            Event fired when the Category property of command is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1Command.ToolTipChanged">
      <summary>
            Event fired when the ToolTip property of command is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1Command.ShowShortcutChanged">
      <summary>
            Event fired when the ShowShortcut property of command is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1Command.ShowTextAsToolTipChanged">
      <summary>
            Event fired when the ShowTextAsToolTip property of command is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1Command.DropDownClicked">
      <summary>
            Event fired when the drop down arrow is clicked on the toolbar button.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1Command.HasInputFocus">
      <summary>
            Returns true if the command has focus and "firmly" keeps it
            (e.g. if it is C1CommandControl and user clicked inside the control).
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.C1Command.TypeConverter">
      <summary>
            Provides a type converter to convert C1Command to and from various other representations.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1Command.TypeConverter.#ctor(System.Type)">
      <summary>
            Initializes a new instance of the TypeConverter class.
            </summary>
      <param name="type">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1Command.TypeConverter.GetPropertiesSupported(System.ComponentModel.ITypeDescriptorContext)">
      <summary>
            Returns whether this object supports properties.
            </summary>
      <param name="context">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1Command.TypeConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
      <summary>
            Converts the given value object to the specified type.
            </summary>
      <param name="context">An ITypeDescriptorContext that provides a format context. </param>
      <param name="cinfo">A CultureInfo object.</param>
      <param name="o">The Object to convert.</param>
      <param name="type">The Type to convert the value parameter to. </param>
      <returns>
      </returns>
    </member>
    <member name="T:C1.Win.C1Command.C1Commands">
      <summary>
            Represents a collection of C1Command
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1Commands.#ctor(C1.Win.C1Command.C1CommandHolder)">
      <summary>
            Initializes a new instance of the C1Commands class.
            </summary>
      <param name="owner">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1Commands.IndexOfKey(System.String)">
      <summary>
            Returns the index of the first occurrence of the C1Command with the specified key. 
            </summary>
      <param name="key">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1Commands.Clear">
      <summary>
            Removes all elements
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1Commands.Add(System.Object)">
      <summary>
            Adds an object to the end.
            </summary>
      <param name="o">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1Commands.AddRange(System.Collections.ICollection)">
      <summary>
            Adds the elements of an ICollection to the end.
            </summary>
      <param name="c">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1Commands.Insert(System.Int32,System.Object)">
      <summary>
            Inserts an element into the C1Commands at the specified index.
            </summary>
      <param name="index">
      </param>
      <param name="o">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1Commands.InsertRange(System.Int32,System.Collections.ICollection)">
      <summary>
            Inserts the elements of a collection into the C1Commands at the specified index.
            </summary>
      <param name="index">
      </param>
      <param name="c">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1Commands.RemoveAt(System.Int32)">
      <summary>
            Removes the element at the specified index.
            </summary>
      <param name="index">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1Commands.RemoveRange(System.Int32,System.Int32)">
      <summary>
            Removes a range of elements
            </summary>
      <param name="index">
      </param>
      <param name="count">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1Commands.SetRange(System.Int32,System.Collections.ICollection)">
      <summary>
            Copies the elements of a collection over a range of elements.
            </summary>
      <param name="index">
      </param>
      <param name="c">
      </param>
    </member>
    <member name="P:C1.Win.C1Command.C1Commands.Item(System.Int32)">
      <summary>
            Gets or sets the element at the specified index. 
            </summary>
      <param name="idx">
      </param>
      <returns>
      </returns>
    </member>
    <member name="P:C1.Win.C1Command.C1Commands.Item(System.String)">
      <summary>
            Gets a command with the specified key from the collection. 
            </summary>
      <param name="key">
      </param>
      <returns>
      </returns>
    </member>
    <member name="T:C1.Win.C1Command.C1CommandControl">
      <summary>
            Arbitrary controls can be contained in toolbars. This functionality is provided by the class C1CommandControl, derived from C1Command.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandControl.#ctor">
      <summary>
            Initializes a new instance of the C1CommandControl class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandControl.Invoke(C1.Win.C1Command.ClickEventArgs)">
      <summary>
            Invokes the command.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandControl.OnBarPopDown">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandControl.LinkReceiveFocus(C1.Win.C1Command.C1CommandLink)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="cl">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandControl.AddCommandLink(C1.Win.C1Command.C1CommandLink)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="cl">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandControl.RemoveCommandLink(C1.Win.C1Command.C1CommandLink)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="cl">
      </param>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandControl.ControlChanged">
      <summary>
            Event fired when the Control property of command is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandControl.CommandActiveChanged">
      <summary>
            Event fired when the CommandActive property of command is changed.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandControl.Control">
      <summary>
            Gets or sets the arbitrary control attached to the command.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandControl.Enabled">
      <summary>
            Gets or sets whether the command is enabled.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandControl.CommandActive">
      <summary>
            Gets or sets the value indicating whether the command link can be clicked.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandControl.AcceptsReturn">
      <summary>
            Gets or sets the value indicating whether the embedded control receives the Return key.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandControl.HotFrame">
      <summary>
            Gets or sets the value indicating how the hot frame around the link is drawn.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandControl.HasInputFocus">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.C1CommandDock">
      <summary>
            The C1CommandDock control provides docking and floating capabilities to C1Command toolbars (class C1ToolBar) and tab controls (class C1DockingTab).
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandDock.#ctor">
      <summary>
            Initializes a new instance of the C1CommandDock class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandDock.Dispose(System.Boolean)">
      <summary>
            Releases the resources used by the C1CommandDock.
            </summary>
      <param name="disposing">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandDock.DockOrFloatChild(System.Windows.Forms.Control,System.Drawing.Point)">
      <summary>
            Docs or floats the specified control.
            </summary>
      <param name="child">The child control.</param>
      <param name="screenPt">The x- and y-coordinates of the point where the child control will be moved.</param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandDock.OnPaint(System.Windows.Forms.PaintEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="pe">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandDock.InitLayout">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandDock.OnLayout(System.Windows.Forms.LayoutEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandDock.OnDockChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandDock.OnBackColorChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandDock.OnBackgroundImageChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandDock.OnFontChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandDock.OnForeColorChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandDock.OnControlAdded(System.Windows.Forms.ControlEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandDock.OnControlRemoved(System.Windows.Forms.ControlEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandDock.OnStyleChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandDock.OnEnabledChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandDock.OnResize(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandDock.OnParentChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandDock.Dock">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandDock.AutoDockLeft">
      <summary>
            Indicates whether to automatically create a dock area on the left.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandDock.AutoDockTop">
      <summary>
            Indicates whether to automatically create a dock area at the top.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandDock.AutoDockRight">
      <summary>
            Indicates whether to automatically create a dock area on the right.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandDock.AutoDockBottom">
      <summary>
            Indicates whether to automatically create a dock area at the bottom.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandDock.AllowFloat">
      <summary>
            Indicates whether to allow floating C1ToolBar and C1DockingTab controls.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandDock.AutoSize">
      <summary>
            Sets or returns whether the dock automatically adjusts its size.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandDock.Horizontal">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandDock.AutoCreated">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandDock.DockingStyle">
      <summary>
            Specifies docking behavior of C1DockingTab controls.
            </summary>
      <remarks>
        <para>
            If the DockingStyle property is set to Default, 
            docking works without the selectors: when you drag a floating panel over another panel, 
            a gray frame appears to show you the position that the instance of C1DockingTab will 
            have once you release it.
            </para>
        <para>
            The VS2005 docking style feedback is similar to Visual Studio 2005, using docking zone 
            selectors to specify where the dragged view will be docked if you release it.
            </para>
      </remarks>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandDock.FloatHide">
      <summary>
            Defines behavior of floating windows when the application loses focus.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.C1CommandException">
      <summary>
            This class is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandException.#ctor">
      <summary>
            Initializes a new instance of the C1CommandException class
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandException.#ctor(System.String)">
      <summary>
            Initializes a new instance of the C1CommandException class
            </summary>
      <param name="description">
      </param>
    </member>
    <member name="T:C1.Win.C1Command.C1CommandHolder">
      <summary>
            Holds C1Command objects defined on the form.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.#ctor">
      <summary>
            Initializes a new instance of the C1CommandHolder class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.Dispose(System.Boolean)">
      <summary>
            Releases the resources used by the C1CommandHolder.
            </summary>
      <param name="disposing">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.CreateCommandHolder(System.Windows.Forms.Control)">
      <summary>
            Creates a command holder and initializes it.
            </summary>
      <param name="form">Form for which the command holder is created.</param>
      <returns>The command holder created.</returns>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.CommandHolderByForm(System.Windows.Forms.Control)">
      <summary>
            Returns the command holder for the specified form, if it exists.
            </summary>
      <param name="form">Form whose command holder is returned.</param>
      <returns>The command holder for the specified form, or null.</returns>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.GetC1ContextMenu(System.Object)">
      <summary>
            Gets a context menu contained within the C1CommandHolder.
            </summary>
      <param name="control">The control or NotifyIcon for which to retrieve the context menu.</param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.SetC1ContextMenu(System.Object,C1.Win.C1Command.C1ContextMenu)">
      <summary>
            Sets a context menu contained within the C1CommandHolder.
            </summary>
      <param name="control">The control or NotifyIcon for which to set the context menu.</param>
      <param name="value">The context menu to attach to the control.</param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.ShouldSerializeC1ContextMenu(System.Object)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="control">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.ResetC1ContextMenu(System.Object)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="control">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.GetC1Command(System.Object)">
      <summary>
            Determines the command (of the type C1Command or inherited) invoked when the control's default (or, if there is none, Click) event fires.
            </summary>
      <param name="control">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.SetC1Command(System.Object,C1.Win.C1Command.C1Command)">
      <summary>
            Set the command (of the type C1Command or inherited) invoked when the control's default (or, if there is none, Click) event fires.
            </summary>
      <param name="control">
      </param>
      <param name="value">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.ShouldSerializeC1Command(System.Object)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="control">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.ResetC1Command(System.Object)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="control">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.ShouldSerializeImageTransparentColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.ShouldSerializeUIStrings">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.ResetUIStrings">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.ShouldSerializeCustomizerFormClassName">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.ResetCustomizerFormClassName">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.ShouldSerializeNewToolbarFormClassName">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.ResetNewToolbarFormClassName">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.ShouldSerializeSelectMdiChildFormClassName">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.ResetSelectMdiChildFormClassName">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.FindCommand(System.Windows.Forms.Shortcut)">
      <summary>
            Gets the C1Command associated with the shortcut.
            </summary>
      <param name="shortcut">The shortcut keys associated with the command.</param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.CreateCommand">
      <summary>
            Creates a new command and adds it to the Commands collection.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.CreateCommand(System.Type)">
      <summary>
            Creates a new command and adds it to the Commands collection.
            </summary>
      <param name="commandType">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.Customize">
      <summary>
            Invokes toolbar customizer.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.MergeCommandLinks(C1.Win.C1Command.C1CommandLinks,C1.Win.C1Command.C1CommandLinks,C1.Win.C1Command.C1CommandLinks)">
      <summary>
            Merges command links from links1 and links2 into result, using merge rules specified with links, similar to MDI merge.
            </summary>
      <param name="result">
      </param>
      <param name="links1">
      </param>
      <param name="links2">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.MergeCommandLinks(C1.Win.C1Command.C1CommandLinks,C1.Win.C1Command.C1CommandLinks,C1.Win.C1Command.C1CommandLinks,System.Boolean)">
      <summary>
            Merges command links from links1 and links2 into result, using merge rules specified with links, similar to MDI merge.
            </summary>
      <param name="result">
      </param>
      <param name="links1">
      </param>
      <param name="links2">
      </param>
      <param name="restore">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.UnmergeCommandLinks(C1.Win.C1Command.C1CommandLinks)">
      <summary>
            Unmerges the two collections of links merged previously by MergeCommandLinks.
            </summary>
      <param name="result">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.UnmergeCommandLinks(C1.Win.C1Command.C1CommandLinks,C1.Win.C1Command.C1CommandLinks,C1.Win.C1Command.C1CommandLinks)">
      <summary>
            Unmerges the two collections of links merged previously by MergeCommandLinks.
            </summary>
      <param name="result">
      </param>
      <param name="links1">
      </param>
      <param name="links2">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.OnLookAndFeelChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.OnVisualStyleChanged(System.EventArgs)">
      <summary>
            Invokes the VisualStyleChanged event.
            </summary>
      <param name="e">The event arguments.</param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.OnRecentLinksRunCountChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.OnRecentLinksThresholdChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.OnShowNonRecentLinksHoverDelayChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.OnCustomizationStarted(System.EventArgs)">
      <summary>
            called for restoring layout of a single toolbar
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.OnCustomizationFinished(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.OnCustomizeToolBarAdded(C1.Win.C1Command.CustomizeToolBarEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.OnCustomizeToolBarRemoved(C1.Win.C1Command.CustomizeToolBarEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.OnCustomizeLinkAdded(C1.Win.C1Command.CustomizeLinkEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.OnCustomizeLinkRemoved(C1.Win.C1Command.CustomizeLinkEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.SaveDefaultLayout">
      <summary>
            Sets the current layout to be the default (the one the customizer's reset button reverts to).
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.LayoutSaveInAppConfig">
      <summary>
            Saves the layout data into the application config file.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.LayoutLoadFromAppConfig">
      <summary>
            Loads layout data from the application config file.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.LayoutReset">
      <summary>
            Reset layout.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandHolder.ResetToolbar(System.String)">
      <summary>
            Called for restoring layout of a single toolbar.
            </summary>
      <param name="name">
      </param>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandHolder.UseIdleTimer">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandHolder.Commands">
      <summary>
            Gets the collection of commands.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandHolder.MainMenu">
      <summary>
            Returns the main menu for the form.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandHolder.ImageList">
      <summary>
            Gets or sets the image list for command images.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandHolder.ImageTransparentColor">
      <summary>
            Gets or sets the transparent color for images not in image list.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandHolder.Layout">
      <summary>
            Gets or sets the toolbars layout; To enable automatic layout persistence, add this property to dynamic properties.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandHolder.LayoutKeyName">
      <summary>
            Gets or sets a name of layout dynamic key in application config file.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandHolder.AutoSaveLayout">
      <summary>
            Gets or sets the value indicating whether to automatically save the toolbars layout (for this to work, Layout must be added to dynamic properties).
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandHolder.StateUpdateOnIdle">
      <summary>
            Gets or sets the value indicating whether to update commands' status when idle.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandHolder.SmoothImages">
      <summary>
            Draw smooth images for currently unselected items.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandHolder.VisualStyle">
      <summary>
            Gets or sets the visual style of all C1Command controls on the form (can be overridden by individual controls).
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandHolder.UIStrings">
      <summary>
            Gets the array of user interface strings.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandHolder.CustomizerFormClassName">
      <summary>
            Gets or sets the class name of the Customizer form.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandHolder.NewToolbarFormClassName">
      <summary>
            Gets or sets the class name of the Customizer's new toolbar dialog form.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandHolder.SelectMdiChildFormClassName">
      <summary>
            Gets or sets the class name of the MDI child selection dialog form.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandHolder.RecentLinksRunCount">
      <summary>
            Gets or sets the value indicating how many times the app must be run before rarely used menu items start hiding.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandHolder.RecentLinksThreshold">
      <summary>
            Gets or sets the threshold (in percent) for an item to be considered rarely used. The higher the value, the more items are hidden.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandHolder.ShowNonRecentLinksHoverDelay">
      <summary>
            Gets or sets the delay (in seconds) for the mouse to hover over the parent menu item in order to show all rarely used item.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandHolder.ShowInvisibleItems">
      <summary>
            Gets or sets the value indicating whether invisible menu and toolbar items are shown at design time.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandHolder.Animation">
      <summary>
            Gets or sets a value indicating whether to use animation in menus.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandHolder.LayoutLoad">
      <summary>
            Occurs when layout string must be loaded from app config file.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandHolder.LayoutSave">
      <summary>
            Occurs when layout string must be saved in app config file.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandHolder.CustomizationStarted">
      <summary>
            Occurs when customization mode starts.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandHolder.CustomizationFinished">
      <summary>
            Occurs when customization mode finishes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandHolder.CustomizeToolBarAdded">
      <summary>
            Occurs when a new toolbar is added or a hidden toolbar becomes visible during customization.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandHolder.CustomizeToolBarRemoved">
      <summary>
            Occurs when a visible toolbar is hidden during customization.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandHolder.CustomizeLinkAdded">
      <summary>
            Occurs when a new command link is added to an existing toolbar during customization.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandHolder.CustomizeLinkRemoved">
      <summary>
            Occurs when a command link is removed from a toolbar during customization.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandHolder.CommandClick">
      <summary>
            Occurs when the command is invoked by the user.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandHolder.VisualStyleChanged">
      <summary>
            Occurs when the VisualStyle property has changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandHolder.RecentLinksRunCountChanged">
      <summary>
            Occurs when the value of the RecentLinksRunCount property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandHolder.RecentLinksThresholdChanged">
      <summary>
            Occurs when the value of the RecentLinksRunCount property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandHolder.ShowNonRecentLinksHoverDelayChanged">
      <summary>
            Occurs when the value of the ShowNonRecentLinksHoverDelay property changes.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.C1CommandLink">
      <summary>
            C1CommandLink serves as a link to a C1Command component.
            C1CommandLink can be added to menu/tool bars and customized as needed.
            The actual action is performed by the C1Command proxy points to.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandLink.#ctor">
      <summary>
            Initializes a new instance of the C1CommandLink class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandLink.#ctor(C1.Win.C1Command.C1Command)">
      <summary>
            Initializes a new instance of the C1CommandLink class.
            </summary>
      <param name="command">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandLink.Dispose(System.Boolean)">
      <summary>
            Releases the resources used by the C1CommandLink.
            </summary>
      <param name="disposing">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandLink.OnInvoke(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandLink.ShouldSerializeText">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandLink.ResetText">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandLink.ShouldSerializeToolTipText">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandLink.ResetToolTipText">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandLink.OnCommandChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandLink.OnTextChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandLink.OnDelimiterChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandLink.OnButtonLookChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandLink.OnAlwaysRecentChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandLink.OnToolTipChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandLink.OnMergeTypeChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandLink.OnMergeOrderChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandLink.OnNewColumnChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandLink.CommandChanged">
      <summary>
            Occurs when the value of the Command property of the command link changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandLink.ToolTipChanged">
      <summary>
            Event fired when the ToolTip property of commandlink is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandLink.TextChanged">
      <summary>
            Event fired when the Text property of commandlink is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandLink.DelimiterChanged">
      <summary>
            Event fired when the Delimiter property of commandlink is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandLink.ButtonLookChanged">
      <summary>
            Event fired when the ButtonLook property of commandlink is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandLink.AlwaysRecentChanged">
      <summary>
            Event fired when the AlwaysRecent property of commandlink is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandLink.MergeTypeChanged">
      <summary>
            Event fired when the MergeType property of commandlink is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandLink.MergeOrderChanged">
      <summary>
            Event fired when the MergeOrder property of commandlink is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandLink.NewColumnChanged">
      <summary>
            Event fired when the NewColumn property of commandlink is changed.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandLink.Bounds">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandLink.ImageRect">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandLink.TextRect">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandLink.CheckRect">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandLink.Command">
      <summary>
            Gets or sets the linked command.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandLink.Text">
      <summary>
            If set, overrides the Command text.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandLink.ToolTipText">
      <summary>
            If set, overrides the Command tooltip text.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandLink.DefaultItem">
      <summary>
            Gets or sets a value indicating whether this item is default in a dropdown or context menu.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandLink.OwnerDraw">
      <summary>
            Gets or sets a value indicating whether this item is owner-drawn.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandLink.Delimiter">
      <summary>
            Gets or sets a value indicating whether a delimiter should be drawn before this item.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandLink.NewColumn">
      <summary>
            Gets or sets the value indicating whether this link starts a new column in the menu.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandLink.ButtonLook">
      <summary>
            Gets or sets a value controlling how to show the item.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandLink.MergeType">
      <summary>
            Gets or sets a value indicating the behavior of this menu item when its menu is merged with another.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandLink.MergeOrder">
      <summary>
            Gets or sets a value indicating the relative position of the menu item when it is merged with another.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandLink.SortOrder">
      <summary>
            Gets or sets a value determining the relative position of the item.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandLink.AlwaysRecent">
      <summary>
            Gets or sets the value indicating whether the item is always recent (is never hidden when hiding non-recent links is on)
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandLink.Enabled">
      <summary>
            Shortcut to linked command's Enabled property.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandLink.Visible">
      <summary>
            Shortcut to linked command's Visible property.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandLink.IsEnabledParent">
      <summary>
            Returns true if the linked command is enabled and contains a submenu.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandLink.Owner">
      <summary>
            Gets the object (menu, toolbar etc.) containing this command link.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandLink.State">
      <summary>
            Gets the current state of the command link (normal, hot, pressed, open).
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandLink.Index">
      <summary>
            Gets the current index in CommandLinks collection for recent links.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandLink.IsServiceLink">
      <summary>
            Indicates whether this link is a service link (more, customize, scroll button).
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandLink.IsMoreLink">
      <summary>
            Indicates whether this link is a More link.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.C1CommandLinks">
      <summary>
            C1CommandLinks
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandLinks.Clear">
      <summary>
            Removes all elements
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandLinks.Add(System.Object)">
      <summary>
            Adds an object to the end.
            </summary>
      <param name="o">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandLinks.AddRange(System.Collections.ICollection)">
      <summary>
            Adds the elements of an ICollection to the end.
            </summary>
      <param name="c">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandLinks.AddRange(C1.Win.C1Command.C1CommandLink[])">
      <summary>
            Adds the elements of an ICollection to the end.
            </summary>
      <param name="links">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandLinks.Insert(System.Int32,System.Object)">
      <summary>
            Inserts an element into the C1CommandLinks at the specified index.
            </summary>
      <param name="index">
      </param>
      <param name="o">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandLinks.InsertRange(System.Int32,System.Collections.ICollection)">
      <summary>
            Inserts the elements of a collection into the C1CommandLinks at the specified index.
            </summary>
      <param name="index">
      </param>
      <param name="c">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandLinks.SetRange(System.Int32,System.Collections.ICollection)">
      <summary>
            Copies the elements of a collection over a range of elements.
            </summary>
      <param name="index">
      </param>
      <param name="c">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandLinks.Remove(System.Object)">
      <summary>
            Removes the element.
            </summary>
      <param name="o">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandLinks.RemoveAt(System.Int32)">
      <summary>
            Removes the element at the specified index.
            </summary>
      <param name="index">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandLinks.RemoveRange(System.Int32,System.Int32)">
      <summary>
            Removes a range of elements
            </summary>
      <param name="index">
      </param>
      <param name="count">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandLinks.MoveTo(C1.Win.C1Command.C1CommandLink,System.Int32)">
      <summary>
            Moves an element to the specified index.
            </summary>
      <param name="cl">
      </param>
      <param name="newindex">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandLinks.BeginUpdate">
      <summary>
            This method prevents the control from painting until the EndUpdate method is called.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandLinks.EndUpdate">
      <summary>
            Resumes painting after painting is suspended by the BeginUpdate method. 
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandLinks.ResetLinksUsage">
      <summary>
            Resets all counters stored for recent link showing
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandLinks.Item(System.Int32)">
      <summary>
            Gets or sets the element at the specified index.
            </summary>
      <param name="idx">
      </param>
      <returns>
      </returns>
    </member>
    <member name="T:C1.Win.C1Command.C1CommandMdiList">
      <summary>
            C1CommandMdiList.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandMdiList.#ctor">
      <summary>
            Initializes a new instance of the C1CommandMdiList class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandMdiList.Dispose(System.Boolean)">
      <summary>
            Releases the resources used by the C1CommandMdiList.
            </summary>
      <param name="disposing">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandMdiList.OnBeforeSelectWindow(C1.Win.C1Command.BeforeSelectWindowEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandMdiList.OnCommandStateQuery(C1.Win.C1Command.CommandStateQueryEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandMdiList.OnCommandLinkAdded(C1.Win.C1Command.CommandLinkEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandMdiList.OnCommandLinkRemoved(C1.Win.C1Command.CommandLinkEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandMdiList.Invoke(C1.Win.C1Command.ClickEventArgs)">
      <summary>
            This method should not be called directly, as there is no clear meaning in invoking a command which is actually a placeholder for commands created on the fly.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandMdiList.ShowSelectorWindow">
      <summary>
            Populates and shows the popup window with the list of all currently available MDI child windows.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandMdiList.IsEmpty">
      <summary>
            Returns true if there are currently no items in the list.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandMdiList.BeforeSelectWindow">
      <summary>
            Occurs before the Select Window dialog is shown.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandMdiList.Text">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandMdiList.DefaultText">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandMdiList.MaxItems">
      <summary>
            Sets or returns the maximum number of menu items created.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandMdiList.ListHidden">
      <summary>
            Gets or sets the value indicating whether to list hidden windows.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.C1CommandMenu">
      <summary>
            Summary description for C1CommandMenu.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CommandMenu.m_foreColor">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CommandMenu.m_backColor">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CommandMenu.m_foreHiColor">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CommandMenu.m_backHiColor">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CommandMenu.m_backgroundImage">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CommandMenu.m_masterForm">
      <summary>
            This member is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandMenu.#ctor">
      <summary>
            Initializes a new instance of the C1CommandMenu class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandMenu.Dispose(System.Boolean)">
      <summary>
            Releases the resources used by the C1CommandMenu.
            </summary>
      <param name="disposing">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandMenu.Invoke(C1.Win.C1Command.ClickEventArgs)">
      <summary>
            Invokes the command.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandMenu.OnPopup(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandMenu.OnClosed(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandMenu.OnLargeMenuDisplayChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandMenu.OnFontChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandMenu.OnWidthChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandMenu.OnBackColorChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandMenu.OnForeColorChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandMenu.OnBackHiColorChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandMenu.OnForeHiColorChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandMenu.OnBackgroundImageChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandMenu.OnShowToolTipsChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandMenu.OnHideNonRecentLinksChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandMenu.OnSideCaptionChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandMenu.OnVisualStyleChanged(System.EventArgs)">
      <summary>
            Invokes the VisualStyleChanged event.
            </summary>
      <param name="e">The event arguments.</param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandMenu.ResetBackColor">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandMenu.ShouldSerializeBackColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandMenu.ResetForeColor">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandMenu.ShouldSerializeForeColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandMenu.ResetBackHiColor">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandMenu.ShouldSerializeBackHiColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandMenu.ResetForeHiColor">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandMenu.ShouldSerializeForeHiColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandMenu.ResetFont">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandMenu.ShouldSerializeFont">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandMenu.ResetSideCaption">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandMenu.ShouldSerializeSideCaption">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandMenu.IsParent">
      <summary>
            Determines whether this command is a non-empty submenu. 
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandMenu.Popup">
      <summary>
            Occurs before the command's submenu is displayed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandMenu.Closed">
      <summary>
            Occurs after the command's submenu is closed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandMenu.LargeMenuDisplayChanged">
      <summary>
            Event fired when the LargeMenuDisplay property of menu is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandMenu.FontChanged">
      <summary>
            Event fired when the Font property of menu is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandMenu.WidthChanged">
      <summary>
            Event fired when the Width property of menu is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandMenu.SideCaptionChanged">
      <summary>
            Event fired when the SideCaption property of menu is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandMenu.ShowToolTipsChanged">
      <summary>
            Event fired when the ShowToolTips property of menu is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandMenu.BackColorChanged">
      <summary>
            Occurs when the value of the BackColor property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandMenu.ForeColorChanged">
      <summary>
            Occurs when the value of the ForeColor property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandMenu.BackHiColorChanged">
      <summary>
            Occurs when the value of the BackHiColor property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandMenu.ForeHiColorChanged">
      <summary>
            Occurs when the value of the ForeHiColor property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandMenu.BackgroundImageChanged">
      <summary>
            Occurs when the value of the BackgroundImage property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandMenu.HideNonRecentLinksChanged">
      <summary>
            Occurs when the value of the HideNonRecentLinks property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandMenu.MeasureLink">
      <summary>
            Occurs when an owner drawn link needs to be measured.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandMenu.DrawLink">
      <summary>
            Occurs when an owner drawn link needs to be drawn.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandMenu.DrawBar">
      <summary>
            Occurs when an owner drawn menu or toolbar needs to be drawn.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandMenu.CurrentLinkChanged">
      <summary>
            Occurs when the current command link changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandMenu.LayoutLink">
      <summary>
            Occurs when an owner drawn link needs to layout its text, image and control within the link bounds.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1CommandMenu.VisualStyleChanged">
      <summary>
            Occurs when the VisualStyle property has changed.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandMenu.VisualStyle">
      <summary>
            Gets or sets the visual style of the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandMenu.BackColor">
      <summary>
            Gets or sets the base background color of the menu.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandMenu.ForeColor">
      <summary>
            Gets or sets the text color of the menu.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandMenu.BackHiColor">
      <summary>
            Gets or sets the base background color of the highlighted item.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandMenu.ForeHiColor">
      <summary>
            Gets or sets the text color of the highlighted item.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandMenu.BackgroundImage">
      <summary>
            Gets or sets the background image displayed in the menu.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandMenu.BackgroundImageLayout">
      <summary>
            Gets or sets the background image layout in the menu.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandMenu.Width">
      <summary>
            Gets or sets the minimum width of the menu.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandMenu.ImageBarWidth">
      <summary>
            Gets or sets the width of the image/checkbox bar in the menu. If set to 0, the width is calculated automatically.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandMenu.DefaultText">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandMenu.LargeMenuDisplay">
      <summary>
            Determines the way large menus (when all items cannot fit in one column) are displayed.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandMenu.HideNonRecentLinks">
      <summary>
            Gets or sets the value indicating whether to hide rarely used menu items.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandMenu.Font">
      <summary>
            Gets or sets the font of menu.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandMenu.CommandLinks">
      <summary>
            Gets the collection of commandlinks.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandMenu.ShowToolTips">
      <summary>
            Gets or sets the value indicating whether to show tooltip texts when mouse cursor is over menu item.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandMenu.SeparateCheckBar">
      <summary>
            Gets or sets the value indicating whether to show check marks instead of images or in a separate bar.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandMenu.SideCaption">
      <summary>
            Allows to specify a caption (text and/or image) to be shown alongside the left edge of the menu.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandMenu.CloseOnItemClick">
      <summary>
            Gets or sets the value indicating whether to close the menu when an item is clicked.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.C1CommandMsgHook">
      <summary>
            This class is for internal use only.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.LocalWindowsHook">
      <summary>
            This class is for internal use only.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.LocalWindowsHook.m_hhook">
      <summary>
            Hook code.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.LocalWindowsHook.m_filterFunc">
      <summary>
            The FilterFunc value.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.LocalWindowsHook.m_hookType">
      <summary>
            The HookType value.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.LocalWindowsHook.OnHookInvoked(C1.Win.C1Command.HookEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.LocalWindowsHook.#ctor(C1.Win.C1Command.HookType)">
      <summary>
            Initializes a new instance of the LocalWindowsHook class.
            </summary>
      <param name="hook">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.LocalWindowsHook.#ctor(C1.Win.C1Command.HookType,C1.Win.C1Command.LocalWindowsHook.HookProc)">
      <summary>
            Initializes a new instance of the LocalWindowsHook class.
            </summary>
      <param name="hook">
      </param>
      <param name="func">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.LocalWindowsHook.CoreHookProc(System.Int32,System.IntPtr,System.IntPtr)">
      <summary>
            Default filter function.
            </summary>
      <param name="code">Hook code.</param>
      <param name="wParam">WPARAM argument.</param>
      <param name="lParam">LPARAM argument.</param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.LocalWindowsHook.Install">
      <summary>
            Installs the hook.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.LocalWindowsHook.Uninstall">
      <summary>
            Uninstalls the hook.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.LocalWindowsHook.SetWindowsHookEx(C1.Win.C1Command.HookType,C1.Win.C1Command.LocalWindowsHook.HookProc,System.IntPtr,System.Int32)">
      <summary>
            Win32: SetWindowsHookEx()
            </summary>
      <param name="code">
      </param>
      <param name="func">
      </param>
      <param name="hInstance">
      </param>
      <param name="threadID">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.LocalWindowsHook.UnhookWindowsHookEx(System.IntPtr)">
      <summary>
            Win32: UnhookWindowsHookEx()
            </summary>
      <param name="hhook">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.LocalWindowsHook.CallNextHookEx(System.IntPtr,System.Int32,System.IntPtr,System.IntPtr)">
      <summary>
            Win32: CallNextHookEx()
            </summary>
      <param name="hhook">
      </param>
      <param name="code">
      </param>
      <param name="wParam">
      </param>
      <param name="lParam">
      </param>
      <returns>
      </returns>
    </member>
    <member name="E:C1.Win.C1Command.LocalWindowsHook.HookInvoked">
      <summary>
            A HookInvoked event.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.LocalWindowsHook.FilterFunc">
      <summary>
            The FilterFunc value.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.LocalWindowsHook.HookProc">
      <summary>
            Filter function delegate.
            </summary>
      <param name="code">Hook code.</param>
      <param name="wParam">WPARAM argument.</param>
      <param name="lParam">LPARAM argument.</param>
      <returns>
      </returns>
    </member>
    <member name="T:C1.Win.C1Command.LocalWindowsHook.HookEventHandler">
      <summary>
            Represents the method that handles a Hook event.
            </summary>
      <param name="sender">The source of the event. </param>
      <param name="e">A HookEventArgs that contains the event data. </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandMsgHook.C1CommandMsgHookProc(System.Int32,System.IntPtr,System.IntPtr)">
      <summary>
            Initializes a new instance of the C1CommandMsgHookProc class.
            </summary>
      <param name="code">
      </param>
      <param name="wParam">
      </param>
      <param name="lParam">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandMsgHook.#ctor">
      <summary>
            Initializes a new instance of the C1CommandMsgHookProc class.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.C1ContextMenu">
      <summary>
            Context menu.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1ContextMenu.ShowContextMenu(System.Windows.Forms.Control,System.Drawing.Point)">
      <summary>
            Shows the menu at the specified location.
            </summary>
      <param name="control">The control where the context menu is to be shown.</param>
      <param name="pt">The point in the control client area where the context menu is to be shown.</param>
    </member>
    <member name="M:C1.Win.C1Command.C1ContextMenu.CloseContextMenu">
      <summary>
            Closes the currently open context menu if there is one.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1ContextMenu.OnBackColorChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ContextMenu.OnForeColorChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ContextMenu.OnBackHiColorChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ContextMenu.OnForeHiColorChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ContextMenu.OnBackgroundImageChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="P:C1.Win.C1Command.C1ContextMenu.SourceControl">
      <summary>
             Gets the control that is displaying the shortcut menu.
             </summary>
      <remarks>
             This property enables you to determine which control currently displays the shortcut menu defined in the <see cref="T:C1.Win.C1Command.C1ContextMenu" />. 
             If the shortcut menu is not currently displayed, you can use this property to determine which control last displayed the shortcut menu. 
             You can use this property in the <see cref="E:C1.Win.C1Command.C1CommandMenu.Popup" /> event to ensure that the control displays the proper menu items. 
             You can also use this property to pass a reference to the control to a method that performs the tasks associated with a menu command displayed in the shortcut menu. 
             </remarks>
      <example>
        <para>The following code example creates an event handler for the <see cref="E:C1.Win.C1Command.C1CommandMenu.Popup" /> event of the <see cref="T:C1.Win.C1Command.C1ContextMenu" />. 
             The code in the event handler determines which of two controls a PictureBox named pictureBox1 and a TextBox named textBox1 is the control 
             displaying the shortcut menu. </para>
        <para>Depending on which control caused the <see cref="T:C1.Win.C1Command.C1ContextMenu" /> to display its shortcut menu, the control shows or hides the appropriate 
             menu items of <see cref="T:C1.Win.C1Command.C1Command" />. </para>
        <para>This example requires that you have an instance of the <see cref="T:C1.Win.C1Command.C1ContextMenu" /> class, 
             named c1ContextMenu1, defined within the form. c1ContextMenu should have "Copy", "Find", "Change Picture" menu items.
             This example also requires that you have a TextBox and PictureBox added to a form and that the C1ContextMenu property of these controls is set to c1ContextMenu1.
             </para>
        <code>
             private void c1ContextMenu1_Popup(object sender, EventArgs e)
            {
                Control c = c1ContextMenu1.SourceControl;
                // Copy and Find is dispayed for textBox1
                c1CommandCopy.Visible = (c == textBox1);
                c1CommandFind.Visible = (c == textBox1);
                // Change Picture is dispayed for pictureBox1
                c1CommandChangePicture.Visible = (c == pictureBox1);
            }        
             </code>
      </example>
    </member>
    <member name="E:C1.Win.C1Command.C1ContextMenu.Select">
      <summary>
            This event is for internal use only.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.C1DockingTab">
      <summary>
            This class implements the familiar tab control interface: several overlaying pages (each of which can 
            contain arbitrary controls) accessible via tabs at the side of the control.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.#ctor">
      <summary>
            Initializes a new instance of the C1DockingTab class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.ShouldSerializePadding">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.ResetPadding">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.ShouldSerializeItemSize">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.ResetItemSize">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.ResetBackColor">
      <summary>
            Resets the BackColor property to its default value.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.ShouldSerializeBackColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.ResetTabAreaBackColor">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.ShouldSerializeTabAreaBackColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.ShouldSerializeUIStrings">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.ResetUIStrings">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.Dispose(System.Boolean)">
      <summary>
            Releases the resources used by the C1DockingTab.
            </summary>
      <param name="disposing">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnVisibleChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.Refresh">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.CreateControlsInstance">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnPaint(System.Windows.Forms.PaintEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnLayout(System.Windows.Forms.LayoutEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnMouseDown(System.Windows.Forms.MouseEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnMouseMove(System.Windows.Forms.MouseEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnMouseUp(System.Windows.Forms.MouseEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnMouseLeave(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.ProcessCmdKey(System.Windows.Forms.Message@,System.Windows.Forms.Keys)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="msg">
      </param>
      <param name="keyData">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnResize(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnRightToLeftChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnFontChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnForeColorChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnBackColorChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnBackgroundImageChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnParentChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnSystemColorsChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.WndProc(System.Windows.Forms.Message@)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="m">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.IsHot(C1.Win.C1Command.C1DockingTabPage)">
      <summary>
            Returns true if the specified tab is in the hot state, otherwise false.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.BeginUpdate">
      <summary>
            Temporarily suspends the layout logic for the control.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.EndUpdate">
      <summary>
            Resumes normal layout logic.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.Close(C1.Win.C1Command.C1DockingTabPage)">
      <summary>
            Closes the tab page specified.
            </summary>
      <param name="page">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.SlideHidePage">
      <summary>
            Hides the page by sliding it to the side.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.SlideShowPage(C1.Win.C1Command.C1DockingTabPage)">
      <summary>
            Shows the page by sliding it out.
            </summary>
      <param name="page">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.SlideShowPage(System.Int32)">
      <summary>
            Shows the page by sliding it out.
            </summary>
      <param name="pageIndex">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.FindPage(System.String)">
      <summary>
            Returns the page with the specified tab text.
            </summary>
      <param name="text">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.ScrollToSelectedTab">
      <summary>
            Scrolls the tabs if necessary so that the tab of the currently selected page is visible.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.GetTabRowCol(System.Int32,System.Int32@,System.Int32@)">
      <summary>
            Returns the row and column of the specified tab in the tabs of the C1DockingTab.
            </summary>
      <param name="tabIdx">
      </param>
      <param name="row">
      </param>
      <param name="col">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.GetCreatedDockingTabs">
      <summary>
            Returns the collection of all C1DockingTab components that were automatically
            created as the result of user interaction with the current C1DockingTab
            (e.g. when a page is teared off and dropped to float outside of the form).
            </summary>
      <returns>The array of automatically created C1DockingTab controls.</returns>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnSelectedIndexChanging(C1.Win.C1Command.SelectedIndexChangingEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnSelectedTabChanged(System.EventArgs)">
      <summary>
            Raises the <see cref="E:C1.Win.C1Command.C1DockingTab.SelectedIndexChanged" /> event.
            </summary>
      <param name="e">An EventArgs that contains the event data. 
            </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnSelectedIndexChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnPaddingChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnAlignmentChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnTabSizeModeChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnCanMoveTabsChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnHotTrackChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnMultiLineChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnIndentChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnItemSizeChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnTabLookChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnTabLayoutChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnAlignTabsChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnShowToolTipsChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnShowSingleTabChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnShowCaptionChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnCanCloseTabsChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnShowTabsChanged(System.EventArgs)">
      <summary>
            Occurs when the <see cref="P:C1.Win.C1Command.C1DockingTab.ShowTabs" /> property value changes.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnShowTabListChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnCanAutoHideChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnAutoHidingChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnRightToLeftLayoutChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnFloatingChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnTabPageClosing(C1.Win.C1Command.TabPageCancelEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnTabPageClosed(C1.Win.C1Command.TabPageEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnTabStyleChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnAutoCreatedDockingTab(C1.Win.C1Command.AutoCreatedDockingTabEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.OnVisualStyleChanged(System.EventArgs)">
      <summary>
            Invokes the VisualStyleChanged event.
            </summary>
      <param name="e">The event arguments.</param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.SaveLayout(System.Windows.Forms.Form,System.IO.Stream)">
      <summary>
            Saves layout of all C1DockingTab controls on the form to the stream.
            </summary>
      <param name="form">
      </param>
      <param name="stream">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.SaveLayout(System.Windows.Forms.Form,System.String)">
      <summary>
            Saves layout of all C1DockingTab controls on the form to the specified file.
            </summary>
      <param name="form">
      </param>
      <param name="filename">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.RestoreLayout(System.Windows.Forms.Form,System.IO.Stream)">
      <summary>
            Restoress layout of all C1DockingTab controls on the form from the stream.
            </summary>
      <param name="form">
      </param>
      <param name="stream">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.RestoreLayout(System.Windows.Forms.Form,System.String)">
      <summary>
            Restores layout of all C1DockingTab controls on the form from the specified file.
            </summary>
      <param name="form">
      </param>
      <param name="filename">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.Float(C1.Win.C1Command.C1DockingTabPage)">
      <summary>
            Floats the page out from the form.
            </summary>
      <param name="tabPage">The tab page to float out.</param>
      <remarks>
            Floats out the page to a default position. 
            </remarks>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.Float(C1.Win.C1Command.C1DockingTabPage,System.Drawing.Point)">
      <summary>
            Floats the specified tab page out from the form.
            </summary>
      <param name="tabPage">The tab page to float.</param>
      <param name="position">The position.</param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.Float(C1.Win.C1Command.C1DockingTabPage,System.Int32,System.Int32)">
      <summary>
            Floats the specified tab page out from the form.
            </summary>
      <param name="tabPage">The tab page to float.</param>
      <param name="x">The x coordinate of the screen point to float to.</param>
      <param name="y">The y coordinate of the screen point to float to.</param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.Float(System.Drawing.Point)">
      <summary>
            For internal use.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.Float(System.Int32,System.Int32)">
      <summary>
            For internal use.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTab.Float">
      <summary>
            For internal use.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.LayoutExclude">
      <summary>
            List of controls whose children are excluded from SaveLayout / RestoreLayout.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1DockingTab.AutoCreatedDockingTab">
      <summary>
            Fired when a new C1DockingTab is automatically created in response to user actions (e.g. tearing off a tab and dropping it elsewhere).
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1DockingTab.AlignmentChanged">
      <summary>
            Occurs when the Alignment property is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1DockingTab.AutoHidingChanged">
      <summary>
            Occurs when the AutoHiding property is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1DockingTab.CanAutoHideChanged">
      <summary>
            Occurs when the CanAutoHide property is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1DockingTab.CanCloseTabsChanged">
      <summary>
            Occurs when the CanCloseTabs property is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1DockingTab.ShowTabsChanged">
      <summary>
            Occurs when the ShowTabs property is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1DockingTab.ShowTablistChanged">
      <summary>
            Occurs when the ShowTabList property is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1DockingTab.CanMoveTabsChanged">
      <summary>
            Occurs when the CanMoveTabs property is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1DockingTab.DrawTab">
      <summary>
            Allows to override the standard drawing of the tabs with custom drawing.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1DockingTab.HotTrackChanged">
      <summary>
            Occurs when the HotTrack property is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1DockingTab.IndentChanged">
      <summary>
            Occurs when the Indent property is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1DockingTab.ItemSizeChanged">
      <summary>
            Occurs when the ItemSize property is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1DockingTab.MeasureTab">
      <summary>
            Allows to arbitrarily change the size of the tabs.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1DockingTab.MultiLineChanged">
      <summary>
            Occurs when the MultiLine property is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1DockingTab.PaddingChanged">
      <summary>
            Occurs when the Padding property is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1DockingTab.SelectedIndexChanging">
      <summary>
            Event fired when the index of the currently selected page is about to change. Allows to cancel the change.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1DockingTab.SelectedIndexChanged">
      <summary>
            Occurs when the SelectedIndex property is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1DockingTab.SelectedTabChanged">
      <summary>
            Event fired when the SelectedTab is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1DockingTab.ShowToolTipsChanged">
      <summary>
            Occurs when the ShowToolTips property is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1DockingTab.ShowSingleTabChanged">
      <summary>
            Occurs when the ShowSingleTab property is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1DockingTab.ShowCaptionChanged">
      <summary>
            Occurs when the ShowCaption property is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1DockingTab.TabLookChanged">
      <summary>
            Occurs when the TabLook property is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1DockingTab.TabLayoutChanged">
      <summary>
            Occurs when the TabLayout property is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1DockingTab.TabPageClosing">
      <summary>
            Event fired when the currently selected page is about to be closed by the user. Allows to cancel closing the page.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1DockingTab.TabPageClosed">
      <summary>
            Event fired after a page has been closed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1DockingTab.TabSizeModeChanged">
      <summary>
            Occurs when the TabSizeMode property is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1DockingTab.AlignTabsChanged">
      <summary>
            Occurs when the AlignTabs property is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1DockingTab.TabStyleChanged">
      <summary>
            Occurs when the TabStyle property is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1DockingTab.FloatingChanged">
      <summary>
            Occurs when the Floating property is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1DockingTab.RightToLeftLayoutChanged">
      <summary>
            Occurs when the RightToLeftLayout property is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1DockingTab.VisualStyleChanged">
      <summary>
            Occurs when the VisualStyle property has changed.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.TabsShowFocusCues">
      <summary>
            Gets or sets a value indicating whether tabs should display focus rectangles.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.CloseBox">
      <summary>
            Gets or sets position of close box.
            </summary>
      <remarks>
            This property has effect only if <see cref="P:C1.Win.C1Command.C1DockingTab.CanCloseTabs" /> is True.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.DockPreventModifierKey">
      <summary>
            Gets or sets modifier key which, if pressed, will prevent the floating control from docking when it is moved around at runtime.
            </summary>
      <remarks>
            If several modifier keys are specified, pressing any one will work.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.OutlineMode">
      <summary>
            Gets or sets the mode used to draw window outline while moving the window.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.TabPages">
      <summary>
            Gets the collection of tab pages in this control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.SelectedTab">
      <summary>
            Gets or sets the currently selected page.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.SelectedIndex">
      <summary>
            Gets or sets the index of the currently selected page.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.ImageList">
      <summary>
            Gets or sets the image list used to provide images to show on the control’s tabs.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.TabLook">
      <summary>
            Gets or sets the look of the tabs (whether to show text, image or both).
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.TabLayout">
      <summary>
            Gets or sets the layout of text and images on the tabs.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.Padding">
      <summary>
            Gets or sets the amount of space around images and text shown on the tabs.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.Alignment">
      <summary>
            Gets or sets the area of the control (top, bottom, left or right) where the tabs are aligned.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.TabSizeMode">
      <summary>
            Gets or sets the way in which the tabs are sized.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.CanMoveTabs">
      <summary>
            Gets or sets a value indicating whether the end user can rearrange tabs by dragging them around at runtime.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.CanRenameTabs">
      <summary>
            Gets or sets a value indicating whether the end user can rename tabs by double clicking on the tab's text.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.HotTrack">
      <summary>
            Gets or sets a value indicating whether the control's tabs change in appearance when the mouse passes over them.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.AlignTabs">
      <summary>
            Gets or sets the value indicating how tabs are aligned along the side of the page content area.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.Animate">
      <summary>
            Gets or sets a value indicating whether to use animation when showing or hiding the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.MultiLine">
      <summary>
            Gets or sets a value indicating whether more than one row of tabs can be displayed.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.Text">
      <summary>
            Gets the text of the currently selected tab (runtime-only).
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.TabCount">
      <summary>
            Gets the number of pages in the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.RowCount">
      <summary>
            Gets the number of tab rows (runtime-only).
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.Indent">
      <summary>
            Gets or sets the indentation of the first tab from the side of the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.ItemSize">
      <summary>
            Gets or sets the size of the tabs (if empty, the size is calculated automatically based on content).
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.ScrollActive">
      <summary>
            Gets the value indicating whether tabs scrolling arrows are currently shown (runtime-only).
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.AutoHiding">
      <summary>
            Gets or sets the value indicating whether the pages of the tab control are in auto-hiding mode.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.ShowToolTips">
      <summary>
            Gets or sets a value indicating whether the tool tip is shown when the mouse passes over the tab.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.ShowSingleTab">
      <summary>
            Gets or sets a value indicating whether a tab will be shown when there is only one page in the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.CanCloseTabs">
      <summary>
            Gets or sets a value indicating whether individual tab pages can be closed by the end user. If CanCloseTabs is true, a close icon appears either in the caption area (if ShowCaption is true), or in the tabs area otherwise.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.KeepClosedPages">
      <summary>
            Gets or sets a value indicating whether a tab page closed by the end user should be kept in an internal array (used when a previously saved tabs layout is restored). The default is true.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.ShowCaption">
      <summary>
            Gets or sets a value indicating whether caption is shown on the pages.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.CanAutoHide">
      <summary>
            Gets or sets a value indicating whether the pages can auto-hide (if both CanAutoHide and ShowCaption are true, a pin icon appears in the caption area).
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.SplitterWidth">
      <summary>
            Gets or sets the width of automatic splitters drawn between pages of the control when page docking is enabled.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.TextDirection">
      <summary>
            Gets or sets the direction of the text drawn on the tabs.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.Dock">
      <summary>
            Gets or sets the docking of the control in the parent container. Can be specified only for controls not parented in a C1CommandDock.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.BorderStyle">
      <summary>
            Indicates the border style for the DockingTab.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.TabStyle">
      <summary>
            Indicates the tab style for the DockingTab.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.TabsCanFocus">
      <summary>
            Gets or sets a value indicating whether tabs can receive focus on mouse click.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.AcceptsCtrlTab">
      <summary>
            Gets or sets a value indicating whether the control handles Ctrl-Tab and Ctrl-Shift-Tab keys.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.CreatorDockingTab">
      <summary>
            Gets the C1DockingTab which automatically created this instance in response to user actions.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.BackColor">
      <summary>
            Gets or sets the background color for the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.TabAreaBackColor">
      <summary>
            Gets or sets the background color for the tab area.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.Floating">
      <summary>
            Gets the value indicating whether the C1DockingTab is currently floating (runtime-only).
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.UIStrings">
      <summary>
            Gets the array of user interface strings.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.TabAreaBorder">
      <summary>
            Gets or sets the value indicating whether to draw border around the tab area.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.TabsSpacing">
      <summary>
            Gets or sets the distance between tabs (may be negative to overlap tabs).
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.TabAreaSpacing">
      <summary>
            Gets or sets the spacing between the edge of the tab area and the tabs.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.SelectedTabBold">
      <summary>
            Gets or sets the value indicating whether to draw selected tab text with bold font.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.ShowTabList">
      <summary>
            Gets or sets the value indicating whether to show a button with dropdown list of all tabs (ignored in multiline mode).
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.ShowTabs">
      <summary>
            Gets or sets whether tabs area is displayed within the <see cref="T:C1.Win.C1Command.C1DockingTab" />.
            </summary>
      <remarks>
            Use the ShowTabs property to control the display of the tabs. 
            If this property is set to false, the tabs area is not displayed.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.RightToLeftLayout">
      <summary>
            Gets or sets a value indicating whether right-to-left mirror placement is turned on.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.DefaultSize">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.DisplayRectangle">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTab.VisualStyle">
      <summary>
            Gets or sets the visual style of the control.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.TabStyleEnumConverter">
      <summary>
            Provides a type converter to convert TabStyleEnum objects to and from various other representations.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.C1DockingTabPage">
      <summary>
            Represents a single tab page in a C1DockingTab.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.#ctor">
      <summary>
            Initializes a new instance of the C1DockingTabPage class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.ShouldSerializeImage">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.ResetImage">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.ShouldSerializeCaptionText">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.ResetCaptionText">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.ResetBackColor">
      <summary>
            Resets the BackColor property to its default value.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.ShouldSerializeBackColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.ResetTabBackColor">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.ShouldSerializeTabBackColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.ResetTabBackColorSelected">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.ShouldSerializeTabBackColorSelected">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.ResetTabForeColor">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.ShouldSerializeTabForeColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.ResetTabForeColorSelected">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.ShouldSerializeTabForeColorSelected">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.Float">
      <summary>
            Floats the page out from the form to a default position.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.Float(System.Drawing.Point)">
      <summary>
            Floats the page out from the form to the specified position.
            </summary>
      <param name="position">The position.</param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.Float(System.Int32,System.Int32)">
      <summary>
            Floats the page out from the form to the specified coordinates.
            </summary>
      <param name="x">The x coordinate of the screen point to float to.</param>
      <param name="y">The y coordinate of the screen point to float to.</param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.DockTo(C1.Win.C1Command.C1DockingTabPage)">
      <summary>
            Docks the page to the another target page.
            </summary>
      <param name="targetPage">The target page.</param>
      <remarks>
            The page becomes the next page after the target page.
            If you want to Dock this page the first use DockTo(C1DockingTab)/&gt;
            </remarks>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.DockTo(C1.Win.C1Command.C1DockingTab)">
      <summary>
            Docks the page to the target DockingTab.
            </summary>
      <param name="targetDockingTab">The target docking tab.</param>
      <remarks>
            The page becomes the first page at the target DockingTab.
            </remarks>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.Dispose(System.Boolean)">
      <summary>
            Releases the resources used by the C1DockingTabPage.
            </summary>
      <param name="disposing">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.CreateControlsInstance">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.OnParentChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.OnFontChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.OnHandleCreated(System.EventArgs)">
      <summary>
            Raises the HandleCreated event.
            </summary>
      <param name="e">An EventArgs that contains the event data.</param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.OnPaint(System.Windows.Forms.PaintEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.OnPaintBackground(System.Windows.Forms.PaintEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.IsInputKey(System.Windows.Forms.Keys)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="keyData">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.OnBackColorChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.OnForeColorChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.OnKeyDown(System.Windows.Forms.KeyEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.OnMouseDown(System.Windows.Forms.MouseEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.OnMouseMove(System.Windows.Forms.MouseEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.OnMouseUp(System.Windows.Forms.MouseEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.OnMouseLeave(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.OnLayout(System.Windows.Forms.LayoutEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.OnLeave(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.OnEnter(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.OnGotFocus(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.OnLostFocus(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.SetVisibleCore(System.Boolean)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="value">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.OnClosing(System.ComponentModel.CancelEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.OnClosed(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.OnToolTipTextChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.OnCaptionTextChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPage.OnTabVisibleChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="E:C1.Win.C1Command.C1DockingTabPage.CaptionTextChanged">
      <summary>
            Occurs when the caption text changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1DockingTabPage.ToolTipTextChanged">
      <summary>
            Occurs when the tooltip text changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1DockingTabPage.TabVisibleChanged">
      <summary>
            Occurs when the visibility of the tab changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1DockingTabPage.Closing">
      <summary>
            Event fired when the page is about to be closed by the user. Allows to cancel closing the page.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1DockingTabPage.Closed">
      <summary>
            Event fired after the page has been closed.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTabPage.ImageIndex">
      <summary>
            Gets or sets the index of the tab image in the tab control's ImageList.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTabPage.Image">
      <summary>
            Gets or sets the tab image.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTabPage.Text">
      <summary>
            Gets or sets the text shown on the tab.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTabPage.CaptionText">
      <summary>
            Gets or sets the text in the caption area of the page. If not specified, defaults to the value of the Text property.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTabPage.ToolTipText">
      <summary>
            Gets or sets the ToolTip text for the tab.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTabPage.IsSelected">
      <summary>
            Gets the value indicating whether this page is currently selected.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTabPage.TabVisible">
      <summary>
            Gets or sets the value indicating whether the tab is visible.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTabPage.CloseTabBehavior">
      <summary>
            Defines behavior on C1DockingTabPage's CloseBox click.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTabPage.TabBounds">
      <summary>
            Returns the tab bounds of the page.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTabPage.BackColor">
      <summary>
            Gets or sets the background color for the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTabPage.TabBackColor">
      <summary>
            Gets or sets the background color for the tab.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTabPage.TabBackColorSelected">
      <summary>
            Gets or sets the background color for the selected tab.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTabPage.TabForeColor">
      <summary>
            Gets or sets the text color for the nonselected tab.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTabPage.TabForeColorSelected">
      <summary>
            Gets or sets the text color for the selected tab.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTabPage.IsHot">
      <summary>
             Gets a value indicating whether the mouse pointer is located over tab area of the page.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTabPage.DisplayRectangle">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.C1DockingTabPages">
      <summary>
            Represents a collection of C1DockingTabPage elements.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.PagedControlsCollectionBase">
      <summary>
            Represents a a base class for page collections of a paged-like control.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.PagedControlsCollectionBase.#ctor(System.Windows.Forms.Control)">
      <summary>
            Initializes a new instance of thePagedControlsCollectionBase class.
            </summary>
      <param name="owner">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.PagedControlsCollectionBase.check_page(System.Object)">
      <summary>
            This [method|property|class] is for internal use only.
            </summary>
      <param name="o">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.PagedControlsCollectionBase.OnPageAdded(System.Windows.Forms.Control)">
      <summary>
            This [method|property|class] is for internal use only.
            </summary>
      <param name="page">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.PagedControlsCollectionBase.OnPageRemoved(System.Windows.Forms.Control,System.Int32)">
      <summary>
            This [method|property|class] is for internal use only.
            </summary>
      <param name="page">
      </param>
      <param name="pageIdx">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.PagedControlsCollectionBase.BeginUpdate">
      <summary>
            This [method|property|class] is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.PagedControlsCollectionBase.EndUpdate">
      <summary>
            This [method|property|class] is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.PagedControlsCollectionBase.set_page(System.Windows.Forms.Control,System.Int32)">
      <summary>
            This [method|property|class] is for internal use only.
            </summary>
      <param name="value">
      </param>
      <param name="index">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.PagedControlsCollectionBase.get_page(System.Int32)">
      <summary>
            This [method|property|class] is for internal use only.
            </summary>
      <param name="index">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.PagedControlsCollectionBase.Remove(System.Object)">
      <summary>
            Removes the first occurrence of a specific object from the IList.
            </summary>
      <param name="value">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.PagedControlsCollectionBase.Contains(System.Object)">
      <summary>
            Determines whether the IList contains a specific value.
            </summary>
      <param name="value">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.PagedControlsCollectionBase.IndexOf(System.Object)">
      <summary>
            Determines the index of a specific item in the IList.
            </summary>
      <param name="value">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.PagedControlsCollectionBase.Add(System.Object)">
      <summary>
            Adds an item to the IList.
            </summary>
      <param name="value">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.PagedControlsCollectionBase.Clear">
      <summary>
            Removes all items from the IList.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.PagedControlsCollectionBase.Insert(System.Int32,System.Object)">
      <summary>
            Inserts an item to the IList at the specified position.
            </summary>
      <param name="index">
      </param>
      <param name="value">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.PagedControlsCollectionBase.RemoveAt(System.Int32)">
      <summary>
            Removes the IList item at the specified index.
            </summary>
      <param name="index">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.PagedControlsCollectionBase.CopyTo(System.Array,System.Int32)">
      <summary>
            Copies the elements of the ICollection to an Array, starting at a particular Array index.
            </summary>
      <param name="array">
      </param>
      <param name="index">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.PagedControlsCollectionBase.GetEnumerator">
      <summary>
            Returns an enumerator that can iterate through the PagedControlsCollectionBase.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.PagedControlsCollectionBase.CopyTo(System.Array)">
      <summary>
            Copies the PagedControlsCollectionBase to a one-dimensional array.
            </summary>
      <param name="array">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.PagedControlsCollectionBase.CopyTo(System.Int32,System.Array,System.Int32,System.Int32)">
      <summary>
            Copies the ArrayList or a portion of it to a one-dimensional array.
            </summary>
      <param name="index">
      </param>
      <param name="array">
      </param>
      <param name="arrayIndex">
      </param>
      <param name="count">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.PagedControlsCollectionBase.AddRange(System.Collections.ICollection)">
      <summary>
            Adds the elements of an ICollection to the end.
            </summary>
      <param name="c">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.PagedControlsCollectionBase.RemoveRange(System.Int32,System.Int32)">
      <summary>
            Removes a range of elements.
            </summary>
      <param name="index">
      </param>
      <param name="count">
      </param>
    </member>
    <member name="P:C1.Win.C1Command.PagedControlsCollectionBase.Owner">
      <summary>
            This [method|property|class] is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.PagedControlsCollectionBase.IsReadOnly">
      <summary>
            Gets a value indicating whether the IList is read-only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.PagedControlsCollectionBase.IsFixedSize">
      <summary>
            Gets a value indicating whether the IList has a fixed size.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.PagedControlsCollectionBase.IsSynchronized">
      <summary>
            Gets a value indicating whether access to the ICollection is synchronized
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.PagedControlsCollectionBase.Count">
      <summary>
            Gets the number of elements contained in the ICollection.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.PagedControlsCollectionBase.SyncRoot">
      <summary>
            Gets an object that can be used to synchronize access to the ICollection.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.PagedControlsCollectionBase.Enumerator">
      <summary>
            Supports a simple iteration over a collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.PagedControlsCollectionBase.Enumerator.Reset">
      <summary>
            Sets the enumerator to its initial position, which is before the first element in the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.PagedControlsCollectionBase.Enumerator.MoveNext">
      <summary>
            Advances the enumerator to the next element of the collection.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="P:C1.Win.C1Command.PagedControlsCollectionBase.Enumerator.Current">
      <summary>
            Gets the current element in the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPages.check_page(System.Object)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="o">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPages.OnPageAdded(System.Windows.Forms.Control)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="apage">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPages.OnPageRemoved(System.Windows.Forms.Control,System.Int32)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="apage">
      </param>
      <param name="pageIdx">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPages.BeginUpdate">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPages.EndUpdate">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPages.IndexOfKey(System.String)">
      <summary>
            Returns the index of the first occurrence of the C1DockingTabPage with the specified key. 
            </summary>
      <param name="key">
      </param>
      <returns>
      </returns>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTabPages.VisibleCount">
      <summary>
            Returns number of pages where TabVisible property is True.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTabPages.Item(System.Int32)">
      <summary>
            Gets or sets the element at the specified index. 
            </summary>
      <param name="index">
      </param>
      <returns>
      </returns>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTabPages.Item(System.String)">
      <summary>
            Gets a dockingtab page with the specified key from the collection. 
            </summary>
      <param name="key">
      </param>
      <returns>
      </returns>
    </member>
    <member name="T:C1.Win.C1Command.C1MainMenu">
      <summary>
            C1MainMenu.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1MainMenu.#ctor">
      <summary>
            Initializes a new instance of the C1MainMenu class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1MainMenu.Dispose(System.Boolean)">
      <summary>
            Releases the resources used by the C1MainMenu.
            </summary>
      <param name="disposing">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1MainMenu.OnWrapChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1MainMenu.OnCanMergeChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1MainMenu.OnBackHiColorChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1MainMenu.OnForeHiColorChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1MainMenu.OnVisualStyleChanged(System.EventArgs)">
      <summary>
            Invokes the VisualStyleChanged event.
            </summary>
      <param name="e">The event arguments.</param>
    </member>
    <member name="M:C1.Win.C1Command.C1MainMenu.OnParentChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1MainMenu.OnPaint(System.Windows.Forms.PaintEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1MainMenu.OnResize(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1MainMenu.OnLocationChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1MainMenu.OnSystemColorsChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1MainMenu.InitLayout">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1MainMenu.OnLayout(System.Windows.Forms.LayoutEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1MainMenu.OnMouseEnter(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1MainMenu.OnMouseMove(System.Windows.Forms.MouseEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1MainMenu.OnMouseHover(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1MainMenu.OnMouseDown(System.Windows.Forms.MouseEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1MainMenu.OnMouseUp(System.Windows.Forms.MouseEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1MainMenu.OnMouseWheel(System.Windows.Forms.MouseEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1MainMenu.OnMouseLeave(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1MainMenu.OnBackgroundImageChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1MainMenu.OnBackgroundImageLayoutChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1MainMenu.OnBackColorChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1MainMenu.OnForeColorChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1MainMenu.OnShowToolTipsChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1MainMenu.OnRightToLeftChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1MainMenu.ResetBackHiColor">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1MainMenu.ShouldSerializeBackHiColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1MainMenu.ResetForeHiColor">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1MainMenu.ShouldSerializeForeHiColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1MainMenu.ResetFont">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1MainMenu.ShouldSerializeFont">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1MainMenu.ResetBackColor">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1MainMenu.ShouldSerializeBackColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="E:C1.Win.C1Command.C1MainMenu.WrapChanged">
      <summary>
            Occurs when the value of the Wrap property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1MainMenu.ShowToolTipsChanged">
      <summary>
            Occurs when the value of the ShowToolTips property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1MainMenu.CanMergeChanged">
      <summary>
            Occurs when the value of the CanMerge property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1MainMenu.BackHiColorChanged">
      <summary>
            Occurs when the value of the BackHiColor property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1MainMenu.ForeHiColorChanged">
      <summary>
            Occurs when the value of the ForeHiColor property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1MainMenu.CurrentLinkChanged">
      <summary>
            Occurs when the current command link changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1MainMenu.MeasureLink">
      <summary>
            Occurs when it is necessary to measure the owner-drawn link.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1MainMenu.DrawLink">
      <summary>
            Occurs when the owner-drawn C1CommandLink should be redrawn.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1MainMenu.DrawBar">
      <summary>
            Occurs when the owner-drawn menu should be redrawn.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1MainMenu.LayoutLink">
      <summary>
            Occurs when an OwnerDraw command link needs to layout its text, image and control within the link bounds.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1MainMenu.VisualStyleChanged">
      <summary>
            Occurs when the VisualStyle property has changed.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1MainMenu.VisualStyle">
      <summary>
            Gets or sets the visual style of the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1MainMenu.BackHiColor">
      <summary>
            Gets or sets the background color of the highlighted item.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1MainMenu.ForeHiColor">
      <summary>
            Gets or sets the text color of the highlighted item.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1MainMenu.CommandLinks">
      <summary>
            Gets the collection of commandlinks.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1MainMenu.BackImageInImageBar">
      <summary>
            Gets or sets the value indicating whether to show background image in the image bar of dropdown menus.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1MainMenu.Wrap">
      <summary>
            Gets or sets the value indicating whether to wrap the menu or show a \"More...\" button if not all items fit on a single line.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1MainMenu.CanMerge">
      <summary>
            Gets or sets the value indicating whether to merge MDI child menu with MDI parent menu.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1MainMenu.Font">
      <summary>
            Gets or sets the font of the C1MainMenu.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1MainMenu.BackColor">
      <summary>
            Gets or sets the background color of the C1MainMenu.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1MainMenu.ShowToolTips">
      <summary>
            Gets or sets the value indicating whether to show tooltip texts when mouse cursor is over menu item.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1MainMenu.CloseMoreOnItemClick">
      <summary>
            Gets or sets the value indicating whether the menu that is
            automatically created when some items do not fit on the toolbar closes
            when an item on that menu is clicked.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBar.#ctor">
      <summary>
            Initializes a new instance of the C1NavBar class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBar.ResetBackColor">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBar.ShouldSerializeBackColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBar.ShouldSerializeUIStrings">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBar.ResetUIStrings">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBar.SaveDefaultArrangement">
      <summary>
            Save current order and visibility options as the default button arrangement.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBar.OnSelectedPanelChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBar.OnShowOptionsMenuChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBar.OnAllowCollapseChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBar.OnCollapseDirectionChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBar.OnCloseButtonChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBar.OnVisualStyleChanged(System.EventArgs)">
      <summary>
            Invokes the VisualStyleChanged event.
            </summary>
      <param name="e">The event arguments.</param>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBar.PanelHeaderImage">
      <summary>
            Gets or sets the image that is displayed on the header of the active panel.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBar.PanelHeaderImageTransparentColor">
      <summary>
            Gets or sets the color to treat as transparent in PanelHeaderImage images.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBar.BackColor">
      <summary>
            Gets or sets the background color for the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBar.Buttons">
      <summary>
            Gets the collection of buttons in this navigation bar.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBar.Panels">
      <summary>
            Gets the collection of panels in this navigation bar.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBar.SelectedPanel">
      <summary>
            Gets the currently visible panel in this navigation bar.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBar.ButtonArrangement">
      <summary>
            Gets or sets the string defining the order and visibility for buttons in this navigation bar.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBar.DefaultArrangement">
      <summary>
            Gets the string with the default order and visibility options for the buttons.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBar.StackButtonCount">
      <summary>
            Gets or sets the number of buttons displayed in the stack (-1 shows all buttons).
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBar.MaxStackButtons">
      <summary>
            Gets or sets the maximum number of buttons in the stack (unlimited if -1).
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBar.SelectedButtonIndex">
      <summary>
            Gets or sets index of the selected button in the Buttons collection.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBar.SelectedButton">
      <summary>
            Gets or sets the button, which is currently selected in the C1NavBar.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBar.DefaultButtonIndex">
      <summary>
            Gets or sets the index of the button to be selected when the form opens.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBar.VisibleButtonCount">
      <summary>
            Returns the number of visible buttons in the C1NavBar.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBar.CloseButton">
      <summary>
            Gets or sets a value indicating whether the close button is shown in panel caption, and what is closed when it is clicked.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBar.ShowOptionsMenu">
      <summary>
            Gets or sets a value indicating whether the runtime options menu is visible.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBar.ShowVerticalTextOnCollapse">
      <summary>
            Gets or sets a value indicating whether the vertical text is displayed in collapsed C1NavBar control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBar.AllowCollapse">
      <summary>
            Gets or sets a value indicating whether the collapse button is visible.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBar.CollapseDirection">
      <summary>
            Gets or sets a value indicating the direction in which to collapse the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBar.Collapsed">
      <summary>
            Gets or set the value indicating whether the control is currently in a collapsed state.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBar.VisualStyle">
      <summary>
            Gets or sets the visual style of the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBar.PanelHeaderHeight">
      <summary>
            Gets or sets the height of the navigation bar panels' header.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBar.PanelHeaderFont">
      <summary>
            Gets or sets the font used in panel headers.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBar.SectionHeaderHeight">
      <summary>
            Gets or sets the height of the section header within the navigation bar.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBar.SectionHeaderFont">
      <summary>
            Gets or sets the font used in section headers.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBar.SectionHeaderIndent">
      <summary>
            Gets or sets the space before text caption in a section header.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBar.ButtonHeight">
      <summary>
            Gets or sets the height of each stack button within the C1NavBar.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBar.ButtonFont">
      <summary>
            Gets or sets the font used to render text on a stack button.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBar.ImageScalingSize">
      <summary>
            Gets or sets the size of images displayed on stack buttons (24 x 24 pixels by default).
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBar.ImageIndent">
      <summary>
            Gets or sets the space before the image on a stack button.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBar.TextImageGap">
      <summary>
            Gets or sets the space between the image and the text on a stack button.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBar.Theme">
      <summary>
            Represents a set of color properties to customize appearance of C1NavBar. 
            </summary>
      <remarks>
        <para>The theme colors are reset when VisualStyle property is changed to a new value.</para>
        <para>Some theme colors have effect only with certain visual styles.</para>
        <list type="table">
          <listheader>
            <description>List of the <see cref="T:C1.Win.C1Command.C1NavBarTheme" /> properties, which have effect in Classic, OfficeXP, Office2003 visual styles:</description>
          </listheader>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.BorderColor" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.ButtonNormalGradientBegin" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.ButtonNormalGradientEnd" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.ButtonPressedGradientBegin" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.ButtonPressedGradientEnd" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.ButtonHighlightedGradientBegin" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.ButtonHighlightedGradientEnd" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.ButtonCheckedGradientBegin" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.ButtonCheckedGradientEnd" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.ButtonNormalForeColor" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.ButtonPressedForeColor" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.ButtonHighlightedForeColor" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.ButtonCheckedForeColor" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.PanelHeaderGradientBegin" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.PanelHeaderGradientEnd" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.PanelHeaderForeColor" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.GripGradientBegin" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.GripGradientEnd" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.SeparatorNormalColor" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.SeparatorLightColor" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.SectionHeaderGradientBegin" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.SectionHeaderGradientEnd" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.SectionHeaderForeColor" />
            </description>
          </item>
        </list>
        <list type="table">
          <listheader>
            <description>List of the <see cref="T:C1.Win.C1Command.C1NavBarTheme" /> properties, which have effect in WindowsXP visual style:</description>
          </listheader>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.BorderColor" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.ButtonNormalForeColor" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.PanelHeaderForeColor" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.ButtonHighlightedGradientBegin" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.ButtonHighlightedGradientEnd" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.ButtonPressedGradientBegin" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.ButtonPressedGradientEnd" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.SeparatorNormalColor" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.SectionHeaderForeColor" />
            </description>
          </item>
        </list>
            
            Note, Theme does not have effect in Office2007, Office2010 visual styles.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBar.StripHeight">
      <summary>
            Gets or sets the height of the button strip at the bottom of the navigation bar.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBar.UIStrings">
      <summary>
            Gets the array of user interface strings.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1NavBar.ButtonClick">
      <summary>
            Occurs when the user presses the button on the C1NavBar.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1NavBar.CloseButtonClick">
      <summary>
            Occurs when the close button on the panel header is clicked.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1NavBar.CollapseButtonClick">
      <summary>
            Occurs when the collapse button on the panel header is clicked. Allows to cancel collapsing.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1NavBar.PanelClosing">
      <summary>
            Occurs when the currently selected panel is about to be closed by the user. Allows to cancel closing the panel.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1NavBar.PanelHeaderClick">
      <summary>
            Occurs when the panel header is clicked.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1NavBar.PanelHeaderDoubleClick">
      <summary>
            Occurs when the panel header is double clicked.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1NavBar.PanelHeaderImageClick">
      <summary>
            Occurs when the panel header image is clicked.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1NavBar.PanelClosed">
      <summary>
            Occurs after a panel has closed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1NavBar.ShowOptionsMenuChanged">
      <summary>
            Occurs when the ShowOptionsMenu property is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1NavBar.AllowCollapseChanged">
      <summary>
            Occurs when the AllowCollapse property is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1NavBar.CollapseDirectionChanged">
      <summary>
            Occurs when the CollapseDirection property is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1NavBar.CloseButtonChanged">
      <summary>
            Occurs when the CloseButton property is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1NavBar.SelectedPanelChanging">
      <summary>
            Occurs when the currently selected panel is about to be changed. Allows to cancel changing the panel.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1NavBar.SelectedPanelChanged">
      <summary>
            Occurs when the selected panel is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1NavBar.VisualStyleChanged">
      <summary>
            Occurs when the VisualStyle property has changed.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarButton.ID">
      <summary>
            Gets the unique identifier, which is the same for the corresponding button and panel.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarButton.Image">
      <summary>
            Gets or sets the image that is displayed on the button.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarButton.SmallImage">
      <summary>
            Gets or sets the small image that is displayed in the button strip.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarButton.ImageTransparentColor">
      <summary>
            Gets or sets the color to treat as transparent in Image and SmallImage images.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarButton.Text">
      <summary>
            Gets or sets the text displayed on the stack button, or in a tooltip when the button is in the bottom strip.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarButton.PanelHeader">
      <summary>
            Gets or sets the text shown on the panel header.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarButton.Visible">
      <summary>
            Gets or sets the visibility of the button.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBarButton.NavBarButtonTypeConverter.GetPropertiesSupported(System.ComponentModel.ITypeDescriptorContext)">
      <summary>
            Returns whether this object supports properties.
            </summary>
      <param name="context">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBarButton.NavBarButtonTypeConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
      <summary>
            Converts the given value object to the specified type.
            </summary>
      <param name="context">
      </param>
      <param name="culture">
      </param>
      <param name="value">
      </param>
      <param name="type">
      </param>
      <returns>
      </returns>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarButton.NavBarButtonTypeConverter.Text">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBarButton.ButtonCollection.AddNewButton">
      <summary>
            Creates a new button, puts the button into the collection, returns the created button.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBarButton.ButtonCollection.Contains(System.Object)">
      <summary>
            Determines whether the IList contains a specific value.
            </summary>
      <param name="value">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBarButton.ButtonCollection.IndexOf(System.Object)">
      <summary>
            Determines the index of a specific item in the IList.
            </summary>
      <param name="value">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBarButton.ButtonCollection.IndexOfID(System.Int32)">
      <summary>
            Determines the index of the item in the collection
            with the specified value of the ID property.
            </summary>
      <param name="id">The value of the ID property to search for.</param>
      <returns>Index of the button with the specified ID,
            or -1 if no such button exists.</returns>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBarButton.ButtonCollection.Add(System.Object)">
      <summary>
            Adds an item to the IList.
            </summary>
      <param name="value">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBarButton.ButtonCollection.Clear">
      <summary>
            Removes all items from the IList.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBarButton.ButtonCollection.Insert(System.Int32,System.Object)">
      <summary>
            Inserts an item to the IList at the specified position.
            </summary>
      <param name="index">
      </param>
      <param name="value">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBarButton.ButtonCollection.Remove(System.Object)">
      <summary>
            Removes the first occurrence of a specific object from the IList.
            </summary>
      <param name="value">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBarButton.ButtonCollection.RemoveAt(System.Int32)">
      <summary>
            Removes the IList item at the specified index.
            </summary>
      <param name="index">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBarButton.ButtonCollection.CopyTo(System.Array,System.Int32)">
      <summary>
            Copies the elements of the ICollection to an Array, starting at a particular Array index.
            </summary>
      <param name="array">
      </param>
      <param name="index">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBarButton.ButtonCollection.GetEnumerator">
      <summary>
            Returns an enumerator that can iterate through the PagedControlsCollectionBase.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBarButton.ButtonCollection.CopyTo(System.Array)">
      <summary>
            Copies the ButtonCollection to a one-dimensional array.
            </summary>
      <param name="array">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBarButton.ButtonCollection.CopyTo(System.Int32,System.Array,System.Int32,System.Int32)">
      <summary>
            Copies the ArrayList or a portion of it to a one-dimensional array.
            </summary>
      <param name="index">
      </param>
      <param name="array">
      </param>
      <param name="arrayIndex">
      </param>
      <param name="count">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBarButton.ButtonCollection.AddRange(System.Collections.ICollection)">
      <summary>
            Adds the elements of an ICollection to the end.
            </summary>
      <param name="c">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBarButton.ButtonCollection.RemoveRange(System.Int32,System.Int32)">
      <summary>
            Removes a range of elements.
            </summary>
      <param name="index">
      </param>
      <param name="count">
      </param>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarButton.ButtonCollection.IsReadOnly">
      <summary>
            Gets a value indicating whether the IList is read-only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarButton.ButtonCollection.Item(System.Int32)">
      <summary>
            Gets or sets the element at the specified index. 
            </summary>
      <param name="index">
      </param>
      <returns>
      </returns>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarButton.ButtonCollection.IsFixedSize">
      <summary>
            Gets a value indicating whether the IList has a fixed size.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarButton.ButtonCollection.IsSynchronized">
      <summary>
            Gets a value indicating whether access to the ICollection is synchronized
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarButton.ButtonCollection.Count">
      <summary>
            Gets the number of elements contained in the ICollection.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarButton.ButtonCollection.SyncRoot">
      <summary>
            Gets an object that can be used to synchronize access to the ICollection.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.C1NavBarButton.ButtonCollection.Enumerator">
      <summary>
            Supports a simple iteration over a collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBarButton.ButtonCollection.Enumerator.Reset">
      <summary>
            Sets the enumerator to its initial position, which is before the first element in the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBarButton.ButtonCollection.Enumerator.MoveNext">
      <summary>
            Advances the enumerator to the next element of the collection.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarButton.ButtonCollection.Enumerator.Current">
      <summary>
            Gets the current element in the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBarOptionsForm.Dispose(System.Boolean)">
      <summary>
            Clean up any resources being used.
            </summary>
      <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBarPanel.#ctor">
      <summary>
            Initializes a new instance of the C1NavBarPanel class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBarPanel.ResetButton">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBarPanel.ShouldSerializeButton">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBarPanel.ResetBackColor">
      <summary>
            Resets the BackColor property to its default value.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBarPanel.ShouldSerializeBackColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarPanel.PanelVisible">
      <summary>
            Gets or sets the value indicating whether the panel is visible.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarPanel.Button">
      <summary>
            Gets or sets the corresponding button for this panel.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarPanel.BackColor">
      <summary>
            Gets or sets the background color for the control.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBarPanel.PanelCollection.FindID(System.Int32)">
      <summary>
            Looks for the panel in the collection with the specified value of the ID property.
            </summary>
      <param name="id">The value of the ID property to search for.</param>
      <returns>Panel with the specified ID, or null if no such panel exists.</returns>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBarPanel.PanelCollection.IndexOfID(System.Int32)">
      <summary>
            Determines the index of the item in the collection
            with the specified value of the ID property.
            </summary>
      <param name="id">The value of the ID property to search for.</param>
      <returns>Index of the panel with the specified ID,
            or -1 if no such panel exists.</returns>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBarPanel.PanelCollection.check_page(System.Object)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="o">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBarPanel.PanelCollection.OnPageAdded(System.Windows.Forms.Control)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="page">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBarPanel.PanelCollection.OnPageRemoved(System.Windows.Forms.Control,System.Int32)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="page">
      </param>
      <param name="pageIdx">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBarPanel.PanelCollection.IndexOfKey(System.String)">
      <summary>
            Returns the index of the first occurrence of the C1NavBarPanel with the specified key. 
            </summary>
      <param name="key">
      </param>
      <returns>
      </returns>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarPanel.PanelCollection.Item(System.Int32)">
      <summary>
            Gets or sets the element at the specified index. 
            </summary>
      <param name="index">
      </param>
      <returns>
      </returns>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarPanel.PanelCollection.Item(System.String)">
      <summary>
            Gets a C1NavBarPanel with the specified key from the collection. 
            </summary>
      <param name="key">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBarSectionHeader.#ctor">
      <summary>
            Initializes a new instance of the C1NavBarSectionHeader class.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarSectionHeader.Owner">
      <summary>
            Gets the owner C1NavBar control.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1NavBarHorizontalRule.#ctor">
      <summary>
            Initializes a new instance of the C1NavBarHorizontalRule class.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarHorizontalRule.Owner">
      <summary>
            Gets the owner C1NavBar control.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.C1NavBarTheme">
      <summary>
            Represents a set of color properties to customize appearance of C1NavBar. 
            </summary>
      <remarks>
        <para>The theme colors are reset when VisualStyle property is changed to a new value.</para>
        <para>Some theme colors have effect only with certain visual styles.</para>
        <list type="table">
          <listheader>
            <description>List of the <see cref="T:C1.Win.C1Command.C1NavBarTheme" /> properties, which have effect in Classic, OfficeXP, Office2003 visual styles:</description>
          </listheader>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.BorderColor" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.ButtonNormalGradientBegin" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.ButtonNormalGradientEnd" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.ButtonPressedGradientBegin" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.ButtonPressedGradientEnd" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.ButtonHighlightedGradientBegin" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.ButtonHighlightedGradientEnd" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.ButtonCheckedGradientBegin" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.ButtonCheckedGradientEnd" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.ButtonNormalForeColor" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.ButtonPressedForeColor" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.ButtonHighlightedForeColor" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.ButtonCheckedForeColor" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.PanelHeaderGradientBegin" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.PanelHeaderGradientEnd" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.PanelHeaderForeColor" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.GripGradientBegin" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.GripGradientEnd" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.SeparatorNormalColor" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.SeparatorLightColor" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.SectionHeaderGradientBegin" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.SectionHeaderGradientEnd" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.SectionHeaderForeColor" />
            </description>
          </item>
        </list>
        <list type="table">
          <listheader>
            <description>List of the <see cref="T:C1.Win.C1Command.C1NavBarTheme" /> properties, which have effect in WindowsXP visual style:</description>
          </listheader>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.BorderColor" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.ButtonNormalForeColor" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.PanelHeaderForeColor" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.ButtonHighlightedGradientBegin" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.ButtonHighlightedGradientEnd" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.ButtonPressedGradientBegin" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.ButtonPressedGradientEnd" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.SeparatorNormalColor" />
            </description>
          </item>
          <item>
            <description>
              <see cref="P:C1.Win.C1Command.C1NavBarTheme.SectionHeaderForeColor" />
            </description>
          </item>
        </list>
            
            Note, Theme does not have effect in Office2007, Office2010 visual styles.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarTheme.ClassicTheme">
      <summary>
            Gets the theme that corresponds to the Windows Classic style.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarTheme.BlueTheme">
      <summary>
            Gets the theme that corresponds to the default blue color scheme.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarTheme.OliveGreenTheme">
      <summary>
            Gets the theme that corresponds to the Olive Green color scheme.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarTheme.SilverTheme">
      <summary>
            Gets the theme that corresponds to the Silver color scheme.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarTheme.RoyaleTheme">
      <summary>
            Gets the theme that corresponds to the Media Center style.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarTheme.PanelHeaderForeColor">
      <summary>
            Represents foreground color of panel header.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarTheme.PanelHeaderGradientBegin">
      <summary>
            Represents 1st background gradient color of panel header.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarTheme.PanelHeaderGradientEnd">
      <summary>
            Represents 2nd background gradient color of panel header.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarTheme.SectionHeaderForeColor">
      <summary>
            Represents foreground color of section header.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarTheme.SectionHeaderGradientBegin">
      <summary>
            Represents 1st background gradient color of section header.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarTheme.SectionHeaderGradientEnd">
      <summary>
            Represents 2nd background gradient color of section header.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarTheme.SeparatorLightColor">
      <summary>
            Represents color of horizontal rule in C1NavBar section header.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarTheme.SeparatorNormalColor">
      <summary>
            Represents color of horizontal rule between C1NavBar sections.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarTheme.GripGradientBegin">
      <summary>
            Represents 1st gradient color of grip line.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarTheme.GripGradientEnd">
      <summary>
            Represents 2nd gradient color of grip line.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarTheme.ButtonNormalForeColor">
      <summary>
            Represents foreground color of button in normal state.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarTheme.ButtonNormalGradientBegin">
      <summary>
            Represents 1st gradient color of button background in normal state.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarTheme.ButtonNormalGradientEnd">
      <summary>
            Represents 2nd gradient color of button background in normal state.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarTheme.ButtonHighlightedForeColor">
      <summary>
            Represents foreground color of button when it is highlighted.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarTheme.ButtonHighlightedGradientBegin">
      <summary>
            Represents 1st gradient color of button background when it is highlighted.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarTheme.ButtonHighlightedGradientEnd">
      <summary>
            Represents 2nd gradient color of button background when it is highlighted.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarTheme.ButtonPressedForeColor">
      <summary>
            Represents foreground color of button when it is pressed.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarTheme.ButtonPressedGradientBegin">
      <summary>
            Represents 1st gradient color of button background when it is pressed.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarTheme.ButtonPressedGradientEnd">
      <summary>
            Represents 2nd gradient color of button background when it is pressed.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarTheme.ButtonCheckedForeColor">
      <summary>
            Represents foreground color of button when it is in checked state.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarTheme.ButtonCheckedGradientBegin">
      <summary>
            Represents 1st gradient color of button background when it is in checked state.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarTheme.ButtonCheckedGradientEnd">
      <summary>
            Represents 2nd gradient color of button background when it is in checked state.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1NavBarTheme.BorderColor">
      <summary>
            Border color.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.C1OutBar">
      <summary>
            Outlook-style container/tab control.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1OutBar.#ctor">
      <summary>
            Initializes a new instance of the C1OutBar class
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1OutBar.FindPage(System.String)">
      <summary>
            Returns the index of the page with the specified text, or -1 if such a page does not exist.
            </summary>
      <param name="text">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1OutBar.IsPressed(C1.Win.C1Command.C1OutPage)">
      <summary>
            Returns true if the specified page caption is in the pressed state, or false otherwise.
            </summary>
      <param name="page">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1OutBar.IsHot(C1.Win.C1Command.C1OutPage)">
      <summary>
            Returns true if the specified page caption is in the hot state, or false otherwise.
            </summary>
      <param name="page">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1OutBar.ScrollUp(System.Int32)">
      <summary>
            Scrolls embedded toolbar up.
            </summary>
      <param name="delta">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1OutBar.ScrollDown(System.Int32)">
      <summary>
            Scrolls embedded toolbar down 
            </summary>
      <param name="delta">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1OutBar.BeginUpdate">
      <summary>
            Temporarily suspends the layout logic for the control.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1OutBar.EndUpdate">
      <summary>
            Resumes normal layout logic.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1OutBar.ResetBackColor">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1OutBar.ShouldSerializeBackColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1OutBar.ResetBackHiColor">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1OutBar.ShouldSerializeBackHiColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1OutBar.ResetForeHiColor">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1OutBar.ShouldSerializeForeHiColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1OutBar.Dispose(System.Boolean)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="disposing">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1OutBar.CreateControlsInstance">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1OutBar.OnPaint(System.Windows.Forms.PaintEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1OutBar.OnLayout(System.Windows.Forms.LayoutEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1OutBar.OnMouseDown(System.Windows.Forms.MouseEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1OutBar.OnMouseUp(System.Windows.Forms.MouseEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1OutBar.OnMouseMove(System.Windows.Forms.MouseEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1OutBar.OnMouseLeave(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1OutBar.OnForeColorChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1OutBar.OnBackColorChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1OutBar.OnBackgroundImageChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1OutBar.OnResize(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1OutBar.OnFontChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1OutBar.OnDockChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1OutBar.OnParentChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1OutBar.OnSelectedPageChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1OutBar.OnSelectedIndexChanging(C1.Win.C1Command.SelectedIndexChangingEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1OutBar.OnSelectedIndexChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1OutBar.OnLookAndFeelChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1OutBar.OnVisualStyleChanged(System.EventArgs)">
      <summary>
            Invokes the VisualStyleChanged event.
            </summary>
      <param name="e">The event arguments.</param>
    </member>
    <member name="M:C1.Win.C1Command.C1OutBar.OnPageTitleHeightChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1OutBar.OnShowToolTipsChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1OutBar.OnPageClick(C1.Win.C1Command.PageClickEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1OutBar.OnPageLayoutChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1OutBar.OnBackHiColorChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1OutBar.OnForeHiColorChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="E:C1.Win.C1Command.C1OutBar.VisualStyleChanged">
      <summary>
            Occurs when the VisualStyle property has changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1OutBar.PageTitleHeightChanged">
      <summary>
            Event fired when the PageTitleHeight property of the outbar is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1OutBar.DrawArrow">
      <summary>
            Allows owner draw of C1OutBar's page scrolling arrows.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1OutBar.DrawPage">
      <summary>
            Occurs when the owner-drawn C1OutPage should be redrawn. 
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1OutBar.PageClick">
      <summary>
            Occurs when the page title is clicked. 
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1OutBar.MeasurePage">
      <summary>
            Fires when a C1OutBar's page caption must be measured.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1OutBar.SelectedIndexChanging">
      <summary>
            Event fired when the SelectedIndex property of the outbar is changing.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1OutBar.SelectedIndexChanged">
      <summary>
            Event fired when the SelectedIndex property of the outbar is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1OutBar.SelectedPageChanged">
      <summary>
            Event fired when the SelectedPage property of the outbar is changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1OutBar.ShowToolTipsChanged">
      <summary>
            Occurs when the value of the ShowToolTips property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1OutBar.PageLayoutChanged">
      <summary>
            Occurs when the value of the PageLayout property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1OutBar.BackHiColorChanged">
      <summary>
            Occurs when the value of the BackHiColor property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1OutBar.ForeHiColorChanged">
      <summary>
            Occurs when the value of the ForeHiColor property changes.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1OutBar.BackColor">
      <summary>
            Gets or sets the background color for the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1OutBar.BackHiColor">
      <summary>
            Gets or sets the background color of the highlighted item.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1OutBar.ForeHiColor">
      <summary>
            Gets or sets the text color of the highlighted item.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1OutBar.Pages">
      <summary>
            Gets the collection of outbar pages.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1OutBar.SelectedPage">
      <summary>
            Gets or sets the selected page.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1OutBar.SelectedIndex">
      <summary>
            Gets or sets the index of the selected page.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1OutBar.PageTitleHeight">
      <summary>
            Gets or sets the height of each page title.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1OutBar.ShowScrollButtons">
      <summary>
            Gets or sets a value indicating whether scroll buttons for scrolling toolbar links are visible.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1OutBar.Animate">
      <summary>
            Gets or sets a value indicating whether to use animation when changing the selected page.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1OutBar.ShowToolTips">
      <summary>
            Gets or sets a value indicating whether tool tip is shown when the mouse is over the page title bar.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1OutBar.ImageList">
      <summary>
            Gets or sets the ImageList that contains the images shown on page title bars.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1OutBar.PageLayout">
      <summary>
            Gets or sets the layout of text and image on pages' title bars.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1OutBar.Align">
      <summary>
            Gets or sets the alignment of text and image on pages' title bars.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1OutBar.ShowInvisibleItems">
      <summary>
            Gets or sets the value indicating whether invisible pages are shown at design time.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1OutBar.VisualStyle">
      <summary>
            Gets or sets the visual style of the control.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.C1OutPage">
      <summary>
            Represents a single page in a C1OutBar.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1OutPage.#ctor">
      <summary>
            Initializes a new instance of the C1OutPage class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1OutPage.ShouldSerializeImage">
      <summary>
             This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1OutPage.ResetImage">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1OutPage.ResetBackColor">
      <summary>
            Resets the BackColor property to its default value.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1OutPage.ShouldSerializeBackColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1OutPage.OnToolTipTextChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1OutPage.OnEnabledChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1OutPage.CreateControlsInstance">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1OutPage.OnParentChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1OutPage.SetVisibleCore(System.Boolean)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="value">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1OutPage.OnPageVisibleChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="E:C1.Win.C1Command.C1OutPage.PageVisibleChanged">
      <summary>
            Occurs when the PageVisible property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1OutPage.ToolTipTextChanged">
      <summary>
            Occurs when the tooltip text changes.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1OutPage.PageVisible">
      <summary>
            Gets or sets the value indicating whether the page is visible.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1OutPage.CaptionBounds">
      <summary>
            Returns the caption bounds of the page.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1OutPage.Text">
      <summary>
            Gets or sets the caption of the page.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1OutPage.Pressed">
      <summary>
            Gets the pressed state of the page caption.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1OutPage.OwnerDraw">
      <summary>
            Gets or sets the value indicating whether the title of this page is owner-drawn.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1OutPage.IsSelected">
      <summary>
            Returns true if the page is currently selected.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1OutPage.IsHot">
      <summary>
             Gets a value indicating whether the mouse pointer is located over caption area of the page.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1OutPage.ToolTipText">
      <summary>
            Gets or sets the tooltip text for the page.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1OutPage.ImageIndex">
      <summary>
            Gets or sets the index of the page image in the outbar's ImageList.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1OutPage.Image">
      <summary>
            Gets or sets the page image.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1OutPage.BackColor">
      <summary>
            Gets or sets the background color for the control.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.C1OutPage.C1OutPageControlCollection">
      <summary>
            Represents a collection of control objects.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1OutPage.C1OutPageControlCollection.#ctor(C1.Win.C1Command.C1OutPage)">
      <summary>
            Initializes a new instance of the C1OutPageControlCollection class.
            </summary>
      <param name="owner">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1OutPage.C1OutPageControlCollection.Add(System.Windows.Forms.Control)">
      <summary>
            Adds the specified Control object to the collection. 
            </summary>
      <param name="value">
      </param>
    </member>
    <member name="T:C1.Win.C1Command.C1OutPages">
      <summary>
            The collection of outbar pages.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1OutPages.check_page(System.Object)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="o">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1OutPages.OnPageAdded(System.Windows.Forms.Control)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="page">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1OutPages.OnPageRemoved(System.Windows.Forms.Control,System.Int32)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="page">
      </param>
      <param name="pageIdx">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1OutPages.BeginUpdate">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1OutPages.EndUpdate">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1OutPages.IndexOfKey(System.String)">
      <summary>
            Returns the index of the first occurrence of the C1OutPage with the specified key. 
            </summary>
      <param name="key">
      </param>
      <returns>
      </returns>
    </member>
    <member name="P:C1.Win.C1Command.C1OutPages.Item(System.Int32)">
      <summary>
            Gets or sets the element at the specified index. 
            </summary>
      <param name="index">
      </param>
      <returns>
      </returns>
    </member>
    <member name="P:C1.Win.C1Command.C1OutPages.Item(System.String)">
      <summary>
            Gets an outpage with the specified key from the collection. 
            </summary>
      <param name="key">
      </param>
      <returns>
      </returns>
    </member>
    <member name="T:C1.Win.C1Command.C1SelectMdiChildForm">
      <summary>
            Summary description for C1SelectMdiChildForm.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1SelectMdiChildForm.m_windowsList">
      <summary>
            The list of available existing windows.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1SelectMdiChildForm.m_btnOK">
      <summary>
            OK button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1SelectMdiChildForm.m_btnCancel">
      <summary>
            Cancel button.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1SelectMdiChildForm.#ctor">
      <summary>
            Initializes a new instance of the C1SelectMdiChildForm class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1SelectMdiChildForm.Dispose(System.Boolean)">
      <summary>
            Clean up any resources being used.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1SelectMdiChildForm.OnLoad(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="P:C1.Win.C1Command.C1SelectMdiChildForm.ChildForms">
      <summary>
            A list of available forms
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1SelectMdiChildForm.SelectedForm">
      <summary>
            A form being selected.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1SelectMdiChildForm.WindowsList">
      <summary>
            A ListBox control for displaying available windows.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1SelectMdiChildForm.ButtonOk">
      <summary>
            OK button.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1SelectMdiChildForm.ButtonCancel">
      <summary>
            Cancel button.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.C1SelectMdiChildForm.WindowItem">
      <summary>
            Represents one window in the window list
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1SelectMdiChildForm.WindowItem.Form">
      <summary>
            A Form control
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1SelectMdiChildForm.WindowItem.#ctor(System.Windows.Forms.Form)">
      <summary>
            Initializes a new instance of the WindowItem class.
            </summary>
      <param name="f">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1SelectMdiChildForm.WindowItem.ToString">
      <summary>
            Converts WindowItem to it's string representation.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="T:C1.Win.C1Command.C1ToolBar">
      <summary>
            C1ToolBar.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.#ctor">
      <summary>
            Initializes a new instance of the C1ToolBar class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.Dispose(System.Boolean)">
      <summary>
            Releases the resources used by the C1ToolBar.
            </summary>
      <param name="disposing">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnDockChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnParentChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnPaint(System.Windows.Forms.PaintEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnControlAdded(System.Windows.Forms.ControlEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnControlRemoved(System.Windows.Forms.ControlEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnLayout(System.Windows.Forms.LayoutEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnLocationChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnFontChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnSystemColorsChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.InitLayout">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnMouseEnter(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnMouseMove(System.Windows.Forms.MouseEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnMouseHover(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnMouseDown(System.Windows.Forms.MouseEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnMouseUp(System.Windows.Forms.MouseEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnMouseWheel(System.Windows.Forms.MouseEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnMouseLeave(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnBackgroundImageChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnBackgroundImageLayoutChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnBackColorChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnForeColorChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.SetVisibleCore(System.Boolean)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="value">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnTextChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnCursorChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnRightToLeftChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.ResetBackColor">
      <summary>
            Resets the BackColor property to its default value.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.ShouldSerializeBackColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.ResetBackHiColor">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.ShouldSerializeBackHiColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.ResetForeHiColor">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.ShouldSerializeForeHiColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.ResetBorder">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.ShouldSerializeBorder">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnVisualStyleChanged(System.EventArgs)">
      <summary>
            Invokes the VisualStyleChanged event.
            </summary>
      <param name="e">The event arguments.</param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnWrapChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnWrapTextChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnReset(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnButtonAlignChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnButtonWidthChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnMinButtonSizeChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnCustomizeButtonChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnToolBarStyleChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnHorizontalChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnBorderChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnMeasureFloatingCaption(C1.Win.C1Command.MeasureFloatingCaptionEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnDrawFloatingCaption(C1.Win.C1Command.DrawFloatingCaptionEventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnBackHiColorChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnForeHiColorChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnButtonLookHorzChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnButtonLookVertChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnButtonLookEnforceHorzChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnButtonLookEnforceVertChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnButtonLayoutHorzChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnButtonLayoutVertChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1ToolBar.OnShowToolTipsChanged(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="E:C1.Win.C1Command.C1ToolBar.Reset">
      <summary>
            Event fired when Reset command from Customize dialog is clicked.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1ToolBar.ButtonLookHorzChanged">
      <summary>
            Occurs when the value of the ButtonLookHors property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1ToolBar.ButtonLookVertChanged">
      <summary>
            Occurs when the value of the ButtonLookVert property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1ToolBar.ButtonLookEnforceHorzChanged">
      <summary>
            Occurs when the value of the ButtonLookEnforceHors property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1ToolBar.ButtonLookEnforceVertChanged">
      <summary>
            Occurs when the value of the ButtonLookEnforceVert property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1ToolBar.ButtonLayoutHorzChanged">
      <summary>
            Occurs when the value of the ButtonLayoutHors property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1ToolBar.ButtonLayoutVertChanged">
      <summary>
            Occurs when the value of the ButtonLayoutVert property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1ToolBar.ButtonAlignChanged">
      <summary>
            Occurs when the value of the ButtonAlign property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1ToolBar.ButtonWidthChanged">
      <summary>
            Occurs when the value of the ButtonWidth property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1ToolBar.MinButtonSizeChanged">
      <summary>
            Occurs when the value of the MinButtonSize property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1ToolBar.CustomizeButtonChanged">
      <summary>
            Occurs when the value of the CustomizeButton property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1ToolBar.BorderChanged">
      <summary>
            Occurs when the value of the Border property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1ToolBar.ToolBarStyleChanged">
      <summary>
            Occurs when the value of the ToolBarStyle property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1ToolBar.HorizontalChanged">
      <summary>
            Occurs when the value of the Horizontal property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1ToolBar.ShowToolTipsChanged">
      <summary>
            Occurs when the value of the ShowToolTips property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1ToolBar.BackHiColorChanged">
      <summary>
            Occurs when the value of the BackHiColor property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1ToolBar.ForeHiColorChanged">
      <summary>
            Occurs when the value of the ForeHiColor property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1ToolBar.MeasureLink">
      <summary>
            Occurs when an owner drawn link needs to be measured.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1ToolBar.LayoutLink">
      <summary>
            Occurs when an owner drawn link needs to layout its text, image and control within the link bounds.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1ToolBar.DrawLink">
      <summary>
            Occurs when an owner drawn link needs to be drawn.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1ToolBar.DrawBar">
      <summary>
            Occurs when an owner drawn menu or toolbar needs to be drawn.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1ToolBar.MeasureFloatingCaption">
      <summary>
            Occurs when a floating toolbar's caption needs to be measured. Allows to customize the toolbar's size.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1ToolBar.DrawFloatingCaption">
      <summary>
            Occurs when a floating toolbar's caption needs to be drawn. Allows to owner draw the caption.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1ToolBar.CurrentLinkChanged">
      <summary>
            Occurs when the current command link changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1ToolBar.WrapChanged">
      <summary>
            Occurs when the value of the Wrap property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1ToolBar.WrapTextChanged">
      <summary>
            Occurs when the value of the WrapText property changes.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1ToolBar.VisualStyleChanged">
      <summary>
            Occurs when the VisualStyle property has changed.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1ToolBar.VisualStyle">
      <summary>
            Gets or sets the visual style of the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1ToolBar.BackColor">
      <summary>
            Gets or sets the background color for the control.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1ToolBar.BackHiColor">
      <summary>
            Gets or sets the background color of the highlighted item.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1ToolBar.ForeHiColor">
      <summary>
            Gets or sets the text color of the highlighted item.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1ToolBar.Floating">
      <summary>
            Indicates whether the toolbar is floating.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1ToolBar.Horizontal">
      <summary>
            Gets or sets the orientation of the toolbar.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1ToolBar.Movable">
      <summary>
            Gets or sets the value indicating whether the toolbar can be moved by the user.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1ToolBar.ButtonLayoutHorz">
      <summary>
            Gets or sets the layout of the buttons when the toolbar is horizontal.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1ToolBar.ButtonLayoutVert">
      <summary>
            Gets or sets the layout of the buttons when the toolbar is vertical.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1ToolBar.ButtonLookHorz">
      <summary>
            Gets or sets the default look of the buttons when the toolbar is horizontal.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1ToolBar.ButtonLookVert">
      <summary>
            Gets or sets the default look of the buttons when the toolbar is vertical.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1ToolBar.MinButtonSize">
      <summary>
            Gets or sets the minimum size (width and height) of button in toolbar.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1ToolBar.ButtonWidth">
      <summary>
            Gets or sets the width for all buttons (applies to horizontal toolbars only; if 0, buttons are individually sized).
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1ToolBar.CustomizeButton">
      <summary>
            Gets or sets the visibility of customize button.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1ToolBar.ButtonLookEnforceHorz">
      <summary>
            Gets or sets the value indicating whether to force all buttons to comply with ButtonLookHorz.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1ToolBar.ButtonLookEnforceVert">
      <summary>
            Gets or sets the value indicating whether to force all buttons to comply with ButtonLookVert.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1ToolBar.CommandLinks">
      <summary>
            Gets the collection of commandlinks.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1ToolBar.ButtonLayoutCurrent">
      <summary>
            Gets the current layout of the buttons.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1ToolBar.ButtonLookCurrent">
      <summary>
            Gets the default look of the buttons in the current toolbar orientation.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1ToolBar.ButtonLookEnforceCurrent">
      <summary>
            Gets the value indicating whether to force all buttons to comply with ButtonLookCurrent.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1ToolBar.BackImageInImageBar">
      <summary>
            Gets or sets the value indicating whether to show background image in the image bar of dropdown menus.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1ToolBar.Border">
      <summary>
            Gets or sets the appearance of the toolbar’s border.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1ToolBar.AutoSize">
      <summary>
            Gets or sets the value indicating whether the toolbar automatically adjusts it size to fit all items.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1ToolBar.ButtonAlign">
      <summary>
            Gets or sets the button alignment for vertical toolbars.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1ToolBar.ToolBarStyle">
      <summary>
            Gets or sets the style of the toolbar.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1ToolBar.ShowToolTips">
      <summary>
            Gets or sets the value indicating whether to show tooltip texts when mouse cursor is over toolbutton.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1ToolBar.CustomizeOptions">
      <summary>
            Gets or sets the value indicating what can be customized in this toolbar.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1ToolBar.Wrap">
      <summary>
            Gets or sets the value indicating whether to wrap the toolbar or show a \"More...\" button if not all items fit on a single line.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1ToolBar.WrapText">
      <summary>
            Gets or sets the value indicating whether to wrap text in links when ButtonWidth is greater than zero and text doesn't fit.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1ToolBar.CustomizeMode">
      <summary>
            Indicates whether the toolbar is in customize mode.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1ToolBar.CloseMoreOnItemClick">
      <summary>
            Gets or sets the value indicating whether the menu that is
            automatically created when some items do not fit on the toolbar closes
            when an item on that menu is clicked.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.C1SideCaption">
      <summary>
            Represents a class for SideCaption property of a C1CommandMenu
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1SideCaption.#ctor">
      <summary>
            Initializes a new instance of the C1SideCaption class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1SideCaption.ResetFont">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1SideCaption.ShouldSerializeFont">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1SideCaption.ResetForeColor">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1SideCaption.ShouldSerializeForeColor">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1SideCaption.ResetBarGradientBegin">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1SideCaption.ShouldSerializeBarGradientBegin">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1SideCaption.ResetBarGradientEnd">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1SideCaption.ShouldSerializeBarGradientEnd">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1SideCaption.ShouldSerializeImage">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1SideCaption.ResetImage">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1SideCaption.ShouldSerializeIcon">
      <summary>
            This method is for internal use only.
            </summary>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1SideCaption.ResetIcon">
      <summary>
            This method is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1SideCaption.Text">
      <summary>
            Gets or sets the text shown in the SideCaption.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1SideCaption.Width">
      <summary>
            Gets or sets the width of the SideCaption.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1SideCaption.GradientDirection">
      <summary>
            Gets or sets the gradient direction (horizontal, vertical, forward diagonal, or backward diagonal) in the SideCaption.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1SideCaption.TextDirection">
      <summary>
            Gets or sets the direction of the text in the SideCaption.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1SideCaption.TextAlignment">
      <summary>
            Gets or sets the text alignment (near, far, or center) in the SideCaption.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1SideCaption.CaptionLayout">
      <summary>
            Gets or sets the text layout for the SideCaption.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1SideCaption.Font">
      <summary>
            Gets or sets the font of the SideCaption.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1SideCaption.ForeColor">
      <summary>
            Gets or sets the text color of the SideCaption.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1SideCaption.BarGradientBegin">
      <summary>
            Gets or sets the beginning color of the gradient for the SideCaption.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1SideCaption.BarGradientEnd">
      <summary>
            Gets or sets the ending color of the gradient for the SideCaption.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1SideCaption.Image">
      <summary>
            Gets or sets the SideCaption image.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1SideCaption.ImageIndex">
      <summary>
            Gets or sets the index of the image for the SideCaption in C1CommandHolder.ImageList.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1SideCaption.Icon">
      <summary>
            Gets or sets the SideCaption icon.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.C1SideCaption.SideCaptionTypeConverter">
      <summary>
            Provides a type converter to convert C1Command to and from various other representations.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1SideCaption.SideCaptionTypeConverter.GetPropertiesSupported(System.ComponentModel.ITypeDescriptorContext)">
      <summary>
            Returns whether this object supports properties.
            </summary>
      <param name="context">
      </param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1SideCaption.SideCaptionTypeConverter.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
      <summary>
            Converts the given value object to the specified type.
            </summary>
      <param name="context">
      </param>
      <param name="culture">
      </param>
      <param name="value">
      </param>
      <param name="type">
      </param>
      <returns>
      </returns>
    </member>
    <member name="P:C1.Win.C1Command.C1SideCaption.SideCaptionTypeConverter.Text">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.C1TopicBar">
      <summary>
            Represents a Topic Bar
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1TopicBar.#ctor">
      <summary>
            Initializes a new instance of the C1TopicBar class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1TopicBar.#ctor(System.Windows.Forms.ImageList)">
      <summary>
            Initializes a new instance of the C1TopicBar class
            </summary>
      <param name="imageList">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1TopicBar.HitTest">
      <summary>
            Returns information about the control at at the current mouse position.
            </summary>
      <returns>A <see cref="T:C1.Win.C1Command.C1TopicBarHitTestInfo" /> object that contains information about the point.</returns>
    </member>
    <member name="M:C1.Win.C1Command.C1TopicBar.HitTest(System.Int32,System.Int32)">
      <summary>
            Returns information about the control at a specified point on the control surface.
            </summary>
      <param name="x">The horizontal position of the client coordinate.</param>
      <param name="y">The vertical position of the client coordinate.</param>
      <returns>A <see cref="T:C1.Win.C1Command.C1TopicBarHitTestInfo" /> object that contains information about the point.</returns>
      <remarks>
            This method is useful when handling the <see cref="E:System.Windows.Forms.Control.MouseMove" />, <see cref="E:System.Windows.Forms.Control.MouseDown" /> events or similar. 
            It allows you to determine whether the mouse is over a specific link, page title, etc.
            </remarks>
    </member>
    <member name="M:C1.Win.C1Command.C1TopicBar.HitTest(System.Drawing.Point)">
      <summary>
            Returns information about the control at a specified point on the control surface.
            </summary>
      <param name="p">
        <see cref="T:System.Drawing.Point" /> in client coordinates.</param>
      <returns>A <see cref="T:C1.Win.C1Command.C1TopicBarHitTestInfo" /> object that contains information about the point.</returns>
      <remarks>
            This method is useful when handling the <see cref="E:System.Windows.Forms.Control.MouseMove" />, <see cref="E:System.Windows.Forms.Control.MouseDown" /> events or similar. 
            It allows you to determine whether the mouse is over a specific link, page title, etc.
            </remarks>
      <example>
            The code below shows hit test information whenever the user moves the mouse:
            <code>
            Private Sub C1TopicBar1_MouseMove(ByVal sender As System.Object, ByVal e As System.Windows.Forms.MouseEventArgs) Handles C1TopicBar1.MouseMove
            Dim ht As C1.Win.C1Command.C1TopicBarHitTestInfo = C1TopicBar1.HitTest(e.Location)
            Label1.Text = ht.Type.ToString()
            If (Not IsNothing(ht.Page)) Then
               Label2.Text = ht.Page.Text
            Else
               Label2.Text = ""
            End If
            If (Not IsNothing(ht.Link)) Then
               Label3.Text = ht.Link.Text
            Else
               Label3.Text = ""
            End If
            End Sub        
            </code></example>
    </member>
    <member name="M:C1.Win.C1Command.C1TopicBar.CollapseAll">
      <summary>
            Collapses the all pages
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1TopicBar.ExpandAll">
      <summary>
            Expands the all pages
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1TopicBar.FindPage(System.String)">
      <summary>
            Returns the first page with the specified text, or null if such a page does not exist.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1TopicBar.FindPageByTag(System.Object)">
      <summary>
            Returns the first page with the specified value, or null if such a page does not exist.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1TopicBar.FindLinkByTag(System.Object)">
      <summary>
            Returns the first link with the specified value, or null if such a page does not exist.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1TopicBar.FindPageIndex(System.String)">
      <summary>
            Returns index of the first page with the specified text, or -1 if such a page does not exist.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1TopicBar.FindLink(System.String)">
      <summary>
            Returns the first link with the specified text, or null if such a link does not exist.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1TopicBar.OnVisualStyleChanged(System.EventArgs)">
      <summary>
            Invokes the VisualStyleChanged event.
            </summary>
      <param name="e">The event arguments.</param>
    </member>
    <member name="P:C1.Win.C1Command.C1TopicBar.Align">
      <summary>
            Gets or sets the alignment of the topicbar pages caption.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1TopicBar.Animation">
      <summary>
            Gets or sets the value indicating whether to use animation when collapsing/expanding pages.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1TopicBar.ImageList">
      <summary>
            Gets or sets the ImageList that contains the images shown on page title bars.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1TopicBar.PagePadding">
      <summary>
            Gets or sets the padding between a page border and a link.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1TopicBar.PageOffset">
      <summary>
            Gets or sets the space between pages.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1TopicBar.Pages">
      <summary>
            Gets the collection of topic bar pages.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1TopicBar.ShowToolTips">
      <summary>
            Gets or sets a value indicating whether tool tip is shown when the mouse is over the page title bar.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1TopicBar.ShowInvisibleItems">
      <summary>
            Gets or sets the value indicating whether invisible pages and links are shown at design time.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1TopicBar.VisualStyle">
      <summary>
            Gets or sets the visual style of the control.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1TopicBar.LinkClick">
      <summary>
            Occurs when a link on this topic bar is clicked.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1TopicBar.PageExpanded">
      <summary>
            Occurs when a page is expanded.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1TopicBar.PageCollapsed">
      <summary>
            Occurs when a page is collapsed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1TopicBar.VisualStyleChanged">
      <summary>
            Occurs when the VisualStyle property has changed.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.C1TopicLink">
      <summary>
            Represents a topic bar link.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1TopicLink.ImageIndex">
      <summary>
            Gets or sets the index of the link image in the ImageList of the page.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1TopicLink.Index">
      <summary>
            Index of this link in the topic page. 
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1TopicLink.Text">
      <summary>
            Gets or sets the text of the link.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1TopicLink.ToolTipText">
      <summary>
            Gets or sets the tooltip text for the link.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1TopicLink.Visible">
      <summary>
            Gets or sets the value indicating whether the link is visible.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1TopicLink.TopicPage">
      <summary>
            Gets the owner topicpage.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1TopicLink.TopicBar">
      <summary>
            Gets the owner topicbar.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1TopicLink.Tag">
      <summary>
            Gets or sets arbitrary data that can be associated with the link.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1TopicLink.Enabled">
      <summary>
            Gets or sets the value indicating whether the link is enabled.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.C1TopicLinkCollection">
      <summary>
            Represents a collection of C1TopicLink objects.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1TopicLinkCollection.Add(C1.Win.C1Command.C1TopicLink)">
      <summary>
            Adds the specified link to the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1TopicLinkCollection.IndexOf(C1.Win.C1Command.C1TopicLink)">
      <summary>
            Searches for the specified link and returns the zero-based index.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1TopicLinkCollection.Insert(System.Int32,C1.Win.C1Command.C1TopicLink)">
      <summary>
            Inserts the link into the specified zero-based position.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1TopicLinkCollection.Remove(C1.Win.C1Command.C1TopicLink)">
      <summary>
            Removes the first occurrence of a specified link from the collection.
            </summary>
      <param name="value">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1TopicLinkCollection.Contains(C1.Win.C1Command.C1TopicLink)">
      <summary>
            Determines whether the collection contains a specified link.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1TopicLinkCollection.Item(System.Int32)">
      <summary>
            Gets or sets the link at the specified index.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.C1TopicPage">
      <summary>
            Summary description for CTopicPage.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1TopicPage.#ctor">
      <summary>
            Initializes a new instance of the C1TopicPage class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1TopicPage.FindLink(System.String)">
      <summary>
            Returns the first link with the specified text, or null if such a link does not exist.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1TopicPage.FindLinkByTag(System.Object)">
      <summary>
            Returns the first link with the specified value, or null if such a link does not exist.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1TopicPage.Collapse">
      <summary>
            Collapses the page
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1TopicPage.Expand">
      <summary>
            Expands the page
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1TopicPage.Index">
      <summary>
            Gets the index of this page in the topic bar.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1TopicPage.Align">
      <summary>
            Gets or sets the alignment of links on the page.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1TopicPage.Collapsed">
      <summary>
            Gets or sets the value indicating whether the page is collapsed or not.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1TopicPage.SpecialStyle">
      <summary>
            Gets or sets the value indicating whether the page has a special dark title.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1TopicPage.ShowButton">
      <summary>
            Gets or sets the value indicating whether the expand/collapse button is shown on the page title.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1TopicPage.ImageList">
      <summary>
            Gets or sets the ImageList that contains the images shown in page links.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1TopicPage.ImageIndex">
      <summary>
            Gets or sets the index of the page title image in the topic bar’s ImageList.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1TopicPage.Links">
      <summary>
            Gets the collection of links on the page.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1TopicPage.Text">
      <summary>
            Gets or sets the caption of the page.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1TopicPage.ToolTipText">
      <summary>
            Gets or sets the tooltip text for the page.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1TopicPage.PageVisible">
      <summary>
            Gets or sets the value indicating whether the page is visible.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1TopicPage.WrapText">
      <summary>
            Gets or sets the value indicating whether wide link texts wrap.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1TopicPage.Tag">
      <summary>
            Gets or sets arbitrary data that can be associated with the page.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1TopicPage.TopicBar">
      <summary>
            Gets the owner topicbar.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.C1TopicPageCollection">
      <summary>
            Represents a collection of C1TopicPage objects.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1TopicPageCollection.Add(C1.Win.C1Command.C1TopicPage)">
      <summary>
            Adds the specified page to the collection.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1TopicPageCollection.IndexOf(C1.Win.C1Command.C1TopicPage)">
      <summary>
            Searches for the specified page and returns the zero-based index.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1TopicPageCollection.Insert(System.Int32,C1.Win.C1Command.C1TopicPage)">
      <summary>
            Inserts the page into the specified zero-based position.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1TopicPageCollection.Remove(C1.Win.C1Command.C1TopicPage)">
      <summary>
            Removes the first occurrence of a specified page from the collection.
            </summary>
      <param name="value">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1TopicPageCollection.Contains(C1.Win.C1Command.C1TopicPage)">
      <summary>
            Determines whether the collection contains a specified page.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1TopicPageCollection.Item(System.Int32)">
      <summary>
            Gets or sets the page at the specified index.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.C1CustomizerForm">
      <summary>
            Represents a main customization form.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_tabToolbars">
      <summary>
            A TabPage, describing toolbars.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_tabCommands">
      <summary>
            A TabPage, describing available commands.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_btnOK">
      <summary>
            OK button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_btnCancel">
      <summary>
            Cancel button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_btnNew">
      <summary>
            A button for adding a new toolbar.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_btnDelete">
      <summary>
            A button for deleting toolbar.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_categoriesList">
      <summary>
             A list of all command categories.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_lblCategories">
      <summary>
            A Label control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_lblCommands">
      <summary>
            A Label control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_lblDescription">
      <summary>
            A Label control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_textAndImage">
      <summary>
            A RadioButton control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_textOnly">
      <summary>
            A RadioButton control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_imageOnly">
      <summary>
            A RadioButton control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_textBelow">
      <summary>
            A RadioButton control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_textAbove">
      <summary>
            A RadioButton control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_textOnRight">
      <summary>
            A RadioButton control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_textOnLeft">
      <summary>
            A RadioButton control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_btnSave">
      <summary>
            A RadioButton control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_btnRename">
      <summary>
            A button for saving toolbar layout.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_btnRestore">
      <summary>
            A button to restoring toolbar layout.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_commandsParentPanel">
      <summary>
            A Panel control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_commandsPanel">
      <summary>
            A Panel control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_lblCommandsHelp">
      <summary>
            A Label control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_statusBar">
      <summary>
            A StatusBar control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_statusText">
      <summary>
            A StatusBarPanel control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_btnReset">
      <summary>
            A button to reset toolbar layout.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_toolbarsList">
      <summary>
            A list of all customizable toolbars.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_grpButtonLook">
      <summary>
            A GroupBox control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_grpButtonLayout">
      <summary>
            A GroupBox control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_chkCustomizeButton">
      <summary>
            A CheckBox switching visibility of Customize link in the selected toolbar.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_tabPages">
      <summary>
            A TabControl control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_grpDescription">
      <summary>
            A GroupBox control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_grpButtonAlign">
      <summary>
            A GroupBox control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_alignFar">
      <summary>
            A RadioButton control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_alignCenter">
      <summary>
            A RadioButton control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_alignNear">
      <summary>
            A RadioButton control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_tabOptions">
      <summary>
            A TabPage control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_grpVisualStyle">
      <summary>
            A GroupBox control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_chkSmooth">
      <summary>
            A CheckBox control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_grpColorFont">
      <summary>
            A GroupBox control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_btnFont">
      <summary>
            A button to call Font dialog.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_btnBackColor">
      <summary>
            A button to call Color dialog.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_btnForeColor">
      <summary>
            A button to call Color dialog.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_fontDialog">
      <summary>
            A Font dialog.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_colorDialog">
      <summary>
            A Color dialog.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_updownThreshold">
      <summary>
            A NumericUpDown control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_menuList">
      <summary>
            A CheckedListBox control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_comboStyle">
      <summary>
            A ComboBox control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_lblRecentThreshold">
      <summary>
            A Label control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1CustomizerForm.m_lblHideOldLinks">
      <summary>
            A Label control.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1CustomizerForm.#ctor">
      <summary>
            Initializes a new instance of the C1CustomizerForm class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1CustomizerForm.OnLoad(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CustomizerForm.OnClosed(System.EventArgs)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.C1CustomizerForm.FindFirst(System.String)">
      <summary>
            Finds first command in C1CommandHolder belongs to category.
            </summary>
      <param name="catname">Category name.</param>
      <returns>
      </returns>
    </member>
    <member name="M:C1.Win.C1Command.C1CustomizerForm.FindNext(System.String)">
      <summary>
            Finds next command in C1CommandHolder belongs to category.
            </summary>
      <param name="catname">Category name.</param>
      <returns>
      </returns>
    </member>
    <member name="P:C1.Win.C1Command.C1CustomizerForm.OwnerForm">
      <summary>
            The form, which is owner for this form.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.C1NewToolbarForm">
      <summary>
            Represents dialog for entering a new toolbar name.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1NewToolbarForm.m_toolbarName">
      <summary>
            The Textbox control
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1NewToolbarForm.m_prompt">
      <summary>
            The Label control
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1NewToolbarForm.m_btnOK">
      <summary>
            OK button
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1NewToolbarForm.m_btnCancel">
      <summary>
            Cancel button
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1NewToolbarForm.#ctor">
      <summary>
            Initializes a new instance of the C1NewToolbarForm class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1NewToolbarForm.Dispose(System.Boolean)">
      <summary>
            Releases the resources used by the C1NewToolbarForm.
            </summary>
      <param name="disposing">
      </param>
    </member>
    <member name="P:C1.Win.C1Command.C1NewToolbarForm.ToolbarName">
      <summary>
            The name of new toolbar
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.AllDockingTabs">
      <summary>
            This class is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.AllDockingTabs.#ctor">
      <summary>
            Initializes a new instance of the AllDockingTabs class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.AllDockingTabs.Restore(System.Windows.Forms.Form)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="form">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.AllDockingTabs.FindNamedControl(System.Windows.Forms.Control,System.String,System.Type)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="parent">
      </param>
      <param name="text">
      </param>
      <param name="type">
      </param>
      <returns>
      </returns>
    </member>
    <member name="P:C1.Win.C1Command.AllDockingTabs.CommandDocks">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.C1CommandDockReflector">
      <summary>
            This class is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandDockReflector.#ctor">
      <summary>
            Initializes a new instance of the C1CommandDockReflector class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1CommandDockReflector.Restore(C1.Win.C1Command.C1CommandDock)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="dock">
      </param>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandDockReflector.Id">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandDockReflector.Name">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandDockReflector.Size">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandDockReflector.DockingTabs">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1CommandDockReflector.DragDockingTabs">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.C1DockingTabReflector">
      <summary>
            This class is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabReflector.#ctor">
      <summary>
            Initializes a new instance of the C1DockingTabReflector class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabReflector.Restore(C1.Win.C1Command.C1CommandDock)">
      <summary>
            This method is for internal use only.
            </summary>
      <param name="dock">
      </param>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTabReflector.DockingTabPages">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTabReflector.Name">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTabReflector.CreatorDockingTab">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTabReflector.Size">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTabReflector.OldBounds">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTabReflector.Location">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTabReflector.ParentLocation">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTabReflector.AutoHiding">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTabReflector.Floating">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTabReflector.SelectedIndex">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.C1DockingTabPageReflector">
      <summary>
            This class is for internal use only.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1DockingTabPageReflector.#ctor">
      <summary>
            Initializes a new instance of the C1DockingTabPageReflector class.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTabPageReflector.Name">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTabPageReflector.TabVisible">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1DockingTabPageReflector.ChildIndex">
      <summary>
            This property is for internal use only.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.ButtonLookFlags">
      <summary>
            Use the members of this enumeration to set the values of ButtonLook, ButtonLookHorz, 
            and ButtonLookVert properties of the C1CommandLink class.
            <para>
            The members of this enumeration can be combined using the bitwise OR operation.
            </para></summary>
    </member>
    <member name="F:C1.Win.C1Command.ButtonLookFlags.Default">
      <summary>
            Use the default value for the toolbar.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.ButtonLookFlags.Text">
      <summary>
            Show button text.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.ButtonLookFlags.Image">
      <summary>
            Show button image.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.ButtonLookFlags.TextAndImage">
      <summary>
            Show button text and image.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.ButtonLayoutEnum">
      <summary>
            Specifies the relative location of text and image on toolbar buttons.
            <para>
            Use members of this enumeration to set the ButtonLayoutHorz and ButtonLayoutVert properties of C1Toolbar.
            </para></summary>
    </member>
    <member name="F:C1.Win.C1Command.ButtonLayoutEnum.TextOnRight">
      <summary>
            Text is on the right of the image.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.ButtonLayoutEnum.TextOnLeft">
      <summary>
            Text is on the left of the image.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.ButtonLayoutEnum.TextBelow">
      <summary>
            Text is below the image.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.ButtonLayoutEnum.TextAbove">
      <summary>
            Text is above the image.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.CloseBoxPositionEnum">
      <summary>
            Specifies position of close box in locked mode.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.CloseBoxPositionEnum.Default">
      <summary>
            Default position of close box.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.CloseBoxPositionEnum.ActivePage">
      <summary>
            Close box on the active tab.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.CloseBoxPositionEnum.AllPages">
      <summary>
            Close boxes on the all tabs.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.CloseTabBehavior">
      <summary>
            Defines behavior on C1DockingTabPage's CloseBox click.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.CloseTabBehavior.Close">
      <summary>
            Closes the <see cref="T:C1.Win.C1Command.C1DockingTabPage" /> and removes it from <see cref="T:C1.Win.C1Command.C1DockingTabPages" /> collection.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.CloseTabBehavior.Hide">
      <summary>
            Set the <see cref="P:C1.Win.C1Command.C1DockingTabPage.TabVisible" /> property to false.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.ItemStateEnum">
      <summary>
            Specifies the current state of a menu or toolbar item.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.ItemStateEnum.Normal">
      <summary>
            Normal (default) state.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.ItemStateEnum.Hot">
      <summary>
            Mouse is currently over the item.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.ItemStateEnum.Pressed">
      <summary>
            Item has been pressed (either by the left mouse button or by the space key).
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.ItemStateEnum.Open">
      <summary>
             Item contains a submenu, which is currently open.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.ItemStateEnum.Dragged">
      <summary>
             Item is being dragged during customizing or at design-time.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.LargeMenuDisplayEnum">
      <summary>
            Specifies the behavior of a menu when all its items do not fit on the screen.
            <para>
            Use the members of this enumeration to set the value of the LargeMenuDisplay property in the C1CommandMenu class.
            </para></summary>
    </member>
    <member name="F:C1.Win.C1Command.LargeMenuDisplayEnum.Wrap">
      <summary>
            When a menu does not fit on the screen, it is wrapped.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.LargeMenuDisplayEnum.Scroll">
      <summary>
            When a menu does not fit on the screen, it is scrolled.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.UIStringsEnum">
      <summary>
            Enumerates customizable user interface strings.
            Elements of this enumeration can be used to index strings in the UIStrings array. 
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.UIStringsEnum.More">
      <summary>
            Text of the "More..." command.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.UIStringsEnum.CustomizeToolbarOptions">
      <summary>
            Text of the "Toolbar Options" command.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.UIStringsEnum.CustomizeAddRemoveItems">
      <summary>
            Text of the "Add or Remove Buttons" command.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.UIStringsEnum.CustomizeReset">
      <summary>
            Text of the "Reset" command.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.UIStringsEnum.CustomizeCustomize">
      <summary>
            Text of the "Customize..." command.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.UIStringsEnum.CustomizeRenameToolbarDlg">
      <summary>
            Text of the Rename toolbar dialog command.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.UIStringsEnum.CustomizeDuplicateToolbarName">
      <summary>
            Allows to translate/customize the "duplicate toolbar" message in the customizer.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.UIStringsEnum.MoreMdiChildren">
      <summary>
            Text of the "More Windows" menu item created by C1CommandMdiList.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.UIStringsEnum.CustomizeDeleteToolbarFmt">
      <summary>
            'Allows to translate/customize the "confirm toolbar delete" question in the customizer.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.UIStringsEnum.CustomizeStatusSaving">
      <summary>
            Allows to translate/customize the status strings in the customizer.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.UIStringsEnum.CustomizeStatusRestoring">
      <summary>
            Allows to translate/customize the status strings in the customizer.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.UIStringsEnum.CustomizeStatusResetting">
      <summary>
            Allows to translate/customize the status strings in the customizer.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.UIStringsEnum.CustomizeStatusDone">
      <summary>
            Allows to translate/customize the status strings in the customizer.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.UIStringsEnum.DockingTabCloseTooltip">
      <summary>
            Tooltip for the close C1DockingTab button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.UIStringsEnum.DockingTabPinTooltip">
      <summary>
            Tooltip for the auto hide C1DockingTab button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.UIStringsEnum.DockingTabScrollNextTooltip">
      <summary>
            Tooltip for the scroll right C1DockingTab button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.UIStringsEnum.DockingTabScrollPrevTooltip">
      <summary>
            Tooltip for the scroll left C1DockingTab button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.UIStringsEnum.DockingTabListTooltip">
      <summary>
            Tooltip for the tab list C1DockngTab button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.UIStringsEnum.NavBarShowButtonsMenuText">
      <summary>
            Show item in the NavBar menu.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.UIStringsEnum.NavBarHideButtonsMenuText">
      <summary>
            Hide item in the NavBar menu.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.UIStringsEnum.NavBarOptionsMenuText">
      <summary>
            Options item in the NavBar menu.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.UIStringsEnum.NavBarAddRemoveMenuText">
      <summary>
            Add/remove item in the NavBar menu.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.UIStringsEnum.NavBarCustomizeButtonsToolTipText">
      <summary>
            Customize item in the NavBar menu.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.UIStringsEnum.NavBarCollapsedBarText">
      <summary>
            Collapsed bar text in the NavBar.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.LookAndFeelEnum">
      <summary>
            Specifies the look and feel of a control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.LookAndFeelEnum.OfficeXP">
      <summary>
            Office XP look and feel.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.LookAndFeelEnum.Classic">
      <summary>
            Classic look and feel.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.LookAndFeelEnum.WindowsXP">
      <summary>
            Windows XP look and feel.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.LookAndFeelEnum.Office2003">
      <summary>
            Office 2003 look and feel.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.LookAndFeelEnum.Office2007">
      <summary>
            Office 2007 look and feel.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.LookAndFeelEnum.Office2010">
      <summary>
            Office 2010 look and feel.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.ToolBarStyleEnum">
      <summary>
            Specifies the style of the toolbar.
            <para>
            Use members of this enumeration to set the value of the ToolBarStyle property of a C1ToolBar.
            </para></summary>
    </member>
    <member name="F:C1.Win.C1Command.ToolBarStyleEnum.Default">
      <summary>
            Specifies a toolbar in the default state.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.ToolBarStyleEnum.DropDownMenu">
      <summary>
            Specifies drawing toolbar like DropDownMenu.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.CustomizeOptionsFlags">
      <summary>
            Specifies which customization options are available at runtime.
            <para>
            Use members of this enumeration to set the value of the CustomizeOptions property of a C1ToolBar.
            </para><para>
            The members of this enumeration can be combined using the bitwise OR operation.
            </para></summary>
    </member>
    <member name="F:C1.Win.C1Command.CustomizeOptionsFlags.AllowNone">
      <summary>
            Nothing is allowed.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.CustomizeOptionsFlags.AllowDelete">
      <summary>
            Toolbars can be deleted.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.CustomizeOptionsFlags.AllowAddItem">
      <summary>
            A new C1CommandLink can be added to a toolbar.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.CustomizeOptionsFlags.AllowRemoveItem">
      <summary>
            Command links in toolbar can be removed.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.CustomizeOptionsFlags.AllowToggleCustomizeButton">
      <summary>
            Customize button can be enabled or disabled.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.CustomizeOptionsFlags.AllowAll">
      <summary>
            Allow all actions.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.TabTextDirectionEnum">
      <summary>
            Specifies the text direction.
            <para>
            Use members of this enumeration to set the value of the TabTextDirection property of a C1DockingTab.
            </para></summary>
    </member>
    <member name="F:C1.Win.C1Command.TabTextDirectionEnum.Default">
      <summary>
            The default direction: Horizontal for tabs on the top or bottom, VerticalLeft for tabs on the left, VerticalRight for tabs on the right.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.TabTextDirectionEnum.Horizontal">
      <summary>
            Text is drawn horizontally.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.TabTextDirectionEnum.VerticalLeft">
      <summary>
            Text is drawn vertically, turned 90 degrees counter-clockwise.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.TabTextDirectionEnum.VerticalRight">
      <summary>
            Text is drawn vertically, turned 90 degrees clockwise.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.TabSizeModeEnum">
      <summary>
            Specifies how tabs of a C1DockingTab are sized.
            <para>
            Use members of this enumeration to set the value of the TabSizeMode property of a C1DockingTab.
            </para></summary>
    </member>
    <member name="F:C1.Win.C1Command.TabSizeModeEnum.Normal">
      <summary>
            The default sizing mode.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.TabSizeModeEnum.Fit">
      <summary>
            All tabs are squeezed to fit into the width of the tab control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.TabSizeModeEnum.FillToEnd">
      <summary>
            Tabs are stretched to take the whole width of the tab control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.TabSizeModeEnum.User">
      <summary>
            The user can specify the tab size in the MeasureTab event.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.TabStyleEnum">
      <summary>
            Specifies how the tabs of a C1DockingTab control look.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.TabStyleEnum.VS2003">
      <summary>
            Visual Studio 2003 look.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.TabStyleEnum.Default">
      <summary>
            Use VS2003 instead.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.TabStyleEnum.WindowsXP">
      <summary>
            Windows XP look.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.TabStyleEnum.Classic">
      <summary>
            Classic look.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.TabStyleEnum.Sloping">
      <summary>
            Tabs are sloped on one side.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.TabStyleEnum.Rounded">
      <summary>
            Tabs are rounded.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.TabStyleEnum.Office2003">
      <summary>
            Tabs are drawn using the Office 2003 style.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.TabStyleEnum.Office2007">
      <summary>
            Tabs are drawn using the Office 2007 style.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.TabStyleEnum.Office2010">
      <summary>
            Tabs are drawn using the Office 2010 style.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.OutlineModeEnum">
      <summary>
            Specifies how the window outline is drawn when the window is being moved.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.OutlineModeEnum.Transparent">
      <summary>
            Draws the outline with a checkered pattern of black and transparent pixels.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.OutlineModeEnum.Reversible">
      <summary>
            Draws a reversible line with a complimentary color.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.HotFrameEnum">
      <summary>
            Specifies how the hot frame is drawn on a command link with an embedded control.
            <para>
            Use members of this enumeration to set the value of the HotFrame property of a C1CommandControl.
            </para></summary>
    </member>
    <member name="F:C1.Win.C1Command.HotFrameEnum.Full">
      <summary>
            The frame encloses the whole link.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.HotFrameEnum.Control">
      <summary>
            The frame encloses just the control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.HotFrameEnum.None">
      <summary>
            Hot frame/selection is not drawn.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.FloatHide">
      <summary>
            Defines behavior of floating windows when the application loses focus.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.FloatHide.Default">
      <summary>
            Default float hide behavior.
            </summary>
      <remarks>
            Same to <see cref="F:C1.Win.C1Command.FloatHide.Never" /> float hide behavior.
            </remarks>
    </member>
    <member name="F:C1.Win.C1Command.FloatHide.FocusLost">
      <summary>
            Hides floating docking tabs when the application loses focus.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.FloatHide.Never">
      <summary>
            Never hides floating docking tabs.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.C1NavBarOutlookButton">
      <summary>
            Enumerates the Outlook-style navigation bar buttons.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1NavBarOutlookButton.None">
      <summary>
            Unspecified button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1NavBarOutlookButton.Custom">
      <summary>
            Custom button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1NavBarOutlookButton.Mail">
      <summary>
            Mail button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1NavBarOutlookButton.Calendar">
      <summary>
            Calendar button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1NavBarOutlookButton.Contacts">
      <summary>
            Contacts button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1NavBarOutlookButton.Tasks">
      <summary>
            Tasks button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1NavBarOutlookButton.Notes">
      <summary>
            Notes button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1NavBarOutlookButton.Folder">
      <summary>
            Folder button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1NavBarOutlookButton.Shortcut">
      <summary>
            Shortcut button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1NavBarOutlookButton.Journal">
      <summary>
            Journal button.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.C1NavBarCloseButtonEnum">
      <summary>
            Specifies the visibility and function of the navigation bar's close button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1NavBarCloseButtonEnum.None">
      <summary>
            The close button is not shown.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1NavBarCloseButtonEnum.CloseCurrentPanel">
      <summary>
            Clicking the close button closes the currently selected panel.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1NavBarCloseButtonEnum.CloseWholeBar">
      <summary>
            Clicking the close button closes the whole navigation bar.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.CollapseDirectionEnum">
      <summary>
            Specifies the direction in which the navigation bar is collapsed.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.CollapseDirectionEnum.Auto">
      <summary>
            The direction in which the bar collapses is determined by the value of the control's Dock property.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.CollapseDirectionEnum.ToLeft">
      <summary>
            The bar collapses to the left.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.CollapseDirectionEnum.ToRight">
      <summary>
            The bar collapses to the right.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.CollapseDirectionEnum.ToTop">
      <summary>
            The bar collapses to the top.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.CollapseDirectionEnum.ToBottom">
      <summary>
            The bar collapses to the bottom.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.C1AnimationEnum">
      <summary>
            Specifies how it is determined whether to animate the menus or not.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1AnimationEnum.System">
      <summary>
            Animation is determined by the system-wide setting.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1AnimationEnum.Off">
      <summary>
            Animation is off.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1AnimationEnum.On">
      <summary>
            Animation is on.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.VisualStyle">
      <summary>
            Determines the visual style of a control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.VisualStyle.Custom">
      <summary>
            Custom style.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.VisualStyle.System">
      <summary>
            Standard system style.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.VisualStyle.Office2010Blue">
      <summary>
            MS Office 2010 Blue color scheme.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.VisualStyle.Office2010Black">
      <summary>
            MS Office 2010 Black color scheme.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.VisualStyle.Office2010Silver">
      <summary>
            MS Office 2010 Silver color scheme.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.VisualStyle.Office2007Blue">
      <summary>
            MS Office 2007 Blue color scheme.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.VisualStyle.Office2007Black">
      <summary>
            MS Office 2007 Black color scheme.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.VisualStyle.Office2007Silver">
      <summary>
            MS Office 2007 Silver color scheme.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.VisualStyle.Office2003Blue">
      <summary>
            MS Office 2003 Blue color scheme.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.VisualStyle.Office2003Olive">
      <summary>
            MS Office 2003 Olive color scheme.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.VisualStyle.Office2003Silver">
      <summary>
            MS Office 2003 Silver color scheme.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.VisualStyle.OfficeXP">
      <summary>
            MS Office XP color scheme.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.VisualStyle.Classic">
      <summary>
            Classic color scheme.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.VisualStyle.WindowsXP">
      <summary>
            Windows XP color scheme.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.DockingStyle">
      <summary>
            Specifies docking behavior of C1DockingTab controls.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.DockingStyle.Default">
      <summary>
            Default docking style.
            </summary>
      <remarks>
            If the DockingStyle property is set to Default, 
            docking works without the selectors: when you drag a floating panel over another panel, 
            a gray frame appears to show you the position that the instance of C1DockingTab will 
            have once you release it.
            </remarks>
    </member>
    <member name="F:C1.Win.C1Command.DockingStyle.VS2005">
      <summary>
            Visual studio 2005 - like docking style.
            </summary>
      <remarks>
            The VS2005 docking style feedback is similar to Visual Studio 2005, using docking zone 
            selectors to specify where the dragged view will be docked if you release it.
            </remarks>
    </member>
    <member name="F:C1.Win.C1Command.DockingStyle.VS2008">
      <summary>
            Visual studio 2008 - like docking style.
            </summary>
      <remarks>
            The VS2008 docking style feedback is similar to Visual Studio 2008, using docking zone 
            selectors to specify where the dragged view will be docked if you release it.
            </remarks>
    </member>
    <member name="F:C1.Win.C1Command.DockingStyle.VS2010">
      <summary>
            Visual studio 2010 - like docking style.
            </summary>
      <remarks>
            The VS2010 docking style feedback is similar to Visual Studio 2010, using docking zone 
            selectors to specify where the dragged view will be docked if you release it.
            </remarks>
    </member>
    <member name="T:C1.Win.C1Command.ClickSourceEnum">
      <summary>
            Members of this enumeration are used to determine the value of the ClickSource property in the ClickEventArgs class.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.ClickSourceEnum.None">
      <summary>
            Click source undefined.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.ClickSourceEnum.Menu">
      <summary>
            Click source is a menu.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.ClickSourceEnum.Shortcut">
      <summary>
            Click source is a shortcut key.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.ClickSourceEnum.External">
      <summary>
            Click source is external.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.ContextInfo">
      <summary>
            Describes data related to the invokation of a context menu
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.ContextInfo.#ctor(System.Windows.Forms.Control,System.Drawing.Point)">
      <summary>
            Initializes a new instance of the ContextInfo class
            </summary>
      <param name="source">The control where the context menu was shown.</param>
      <param name="location">The location inside the Source control where the context menu was shown.</param>
    </member>
    <member name="F:C1.Win.C1Command.ContextInfo.Source">
      <summary>
            Returns the control where the context menu was shown.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.ContextInfo.Location">
      <summary>
            Return the location inside the Source control where the context menu was shown.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.ClickEventArgs">
      <summary>
            Arguments for the command Click event handler.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.ClickEventArgs.Empty">
      <summary>
            ClickEventArgs with empty arguments
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.ClickEventArgs.#ctor(C1.Win.C1Command.ClickSourceEnum,C1.Win.C1Command.C1CommandLink,C1.Win.C1Command.ContextInfo)">
      <summary>
            Initializes a new instance of the ClickEventArgs class
            </summary>
      <param name="clickSource">Determines whether the click event was caused by a menu selection, shortcut key, or other means.</param>
      <param name="callerLink">Gets the command link which caused the click event to occur.</param>
      <param name="contextInfo">Gets the context info for clicks which originated in a context menu.</param>
    </member>
    <member name="F:C1.Win.C1Command.ClickEventArgs.ClickSource">
      <summary>
            Determines whether the click event was caused by a menu selection, shortcut key, or other means.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.ClickEventArgs.CallerLink">
      <summary>
            Gets the command link which caused the click event to occur.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.ClickEventArgs.ContextInfo">
      <summary>
            Gets the context info for clicks which originated in a context menu.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.ClickEventHandler">
      <summary>
            Represents the method that will handle a Click event. 
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A ClickEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Command.CommandClickEventArgs">
      <summary>
            Arguments for the CommandClick event handler.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.CommandClickEventArgs.Empty">
      <summary>
            CommandClickEventArgs with empty arguments
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.CommandClickEventArgs.#ctor(C1.Win.C1Command.C1Command,C1.Win.C1Command.ClickSourceEnum,C1.Win.C1Command.C1CommandLink,C1.Win.C1Command.ContextInfo)">
      <summary>
            Initializes a new instance of the CommandClickEventArgs class
            </summary>
      <param name="command">The command invoked by the user.</param>
      <param name="clickSource">Determines what caused the click event to occur.</param>
      <param name="callerLink">Gets the command link which was clicked by the user.</param>
      <param name="contextInfo">Gets the context info for events fired from a context menu.</param>
    </member>
    <member name="M:C1.Win.C1Command.CommandClickEventArgs.#ctor(C1.Win.C1Command.C1Command,C1.Win.C1Command.ClickEventArgs)">
      <summary>
            Initializes a new instance of the CommandClickEventArgs class
            </summary>
      <param name="command">The command invoked by the user.</param>
      <param name="e">A BeforeSelectWindowEventArgs object that contains the event data.</param>
    </member>
    <member name="F:C1.Win.C1Command.CommandClickEventArgs.Command">
      <summary>
            Gets the command clicked.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.CommandClickEventHandler">
      <summary>
            Represents the method that will handle a CommandClick event.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A CommandClickEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Command.CheckedChangedEventArgs">
      <summary>
            Arguments for the check auto toggle event handler.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.CheckedChangedEventArgs.#ctor(System.Boolean)">
      <summary>
            Initializes a new instance of the CheckedChangedEventArgs class
            </summary>
      <param name="newValue">A new value of Checked property.</param>
    </member>
    <member name="F:C1.Win.C1Command.CheckedChangedEventArgs.NewValue">
      <summary>
            Returns a new value of Checked property.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.CheckedChangedEventHandler">
      <summary>
            Represents the method that will handle a CheckedChanged event.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A CheckedChangedEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Command.CommandStateQueryEventArgs">
      <summary>
            Arguments for the command status query event handler.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.CommandStateQueryEventArgs.Visible">
      <summary>
            Determines whether the command is visible.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.CommandStateQueryEventArgs.Enabled">
      <summary>
            Determines whether the command is enabled.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.CommandStateQueryEventArgs.Checked">
      <summary>
            Determines whether the command is checked.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.CommandStateQueryEventArgs.Pressed">
      <summary>
            Determines whether the command is pressed.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.CommandStateQueryEventHandler">
      <summary>
            Represents the method that will handle a CommandStateQuery event.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A CommandStateQueryEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Command.BeforeSelectWindowEventArgs">
      <summary>
            Arguments for the before select MDI child window event handler.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.BeforeSelectWindowEventArgs.#ctor(System.Windows.Forms.Form)">
      <summary>
            Initializes a new instance of the BeforeSelectWindowEventArgs class
            </summary>
      <param name="selectWindowForm">The window or dialog box selected.</param>
    </member>
    <member name="F:C1.Win.C1Command.BeforeSelectWindowEventArgs.SelectWindowForm">
      <summary>
            The window or dialog box selected.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.BeforeSelectWindowEventHandler">
      <summary>
            Represents the method that will handle a BeforeSelectWindow event.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A BeforeSelectWindowEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Command.CustomizeToolBarEventArgs">
      <summary>
            Provides data for the CustomizeToolBar event. 
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.CustomizeToolBarEventArgs.ToolBar">
      <summary>
            Gets the tool bar.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.CustomizeToolBarEventArgs.#ctor(C1.Win.C1Command.C1ToolBar)">
      <summary>
            Initializes a new instance of the CustomizeToolBarEventArgs class
            </summary>
      <param name="toolBar">The tool bar just customized</param>
    </member>
    <member name="T:C1.Win.C1Command.CustomizeToolBarEventHandler">
      <summary>
            Represents the method that will handle a CustomizeToolBar event.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A CustomizeToolBarEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Command.CustomizeLinkEventArgs">
      <summary>
            Provides data for the CustomizeLink event.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.CustomizeLinkEventArgs.ToolBar">
      <summary>
            Gets the tool bar for the command link.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.CustomizeLinkEventArgs.CommandLink">
      <summary>
            Gets the command link.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.CustomizeLinkEventArgs.#ctor(C1.Win.C1Command.C1ToolBar,C1.Win.C1Command.C1CommandLink)">
      <summary>
            Initializes a new instance of the CustomizeLinkEventArgs class
            </summary>
      <param name="toolBar">Gets the tool bar for the command link.</param>
      <param name="commandLink">Gets the command link.</param>
    </member>
    <member name="T:C1.Win.C1Command.CustomizeLinkEventHandler">
      <summary>
            Represents the method that will handle a CustomizeLink event.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A CustomizeLinkEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Command.DropDownEventArgs">
      <summary>
            Provides data for the DropDown event.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.DropDownEventArgs.CallerLink">
      <summary>
            Gets the command link which was selected by the user.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.DropDownEventArgs.LinkRect">
      <summary>
            Gets the screen coordinates of command link.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.DropDownEventArgs.#ctor(C1.Win.C1Command.C1CommandLink,System.Drawing.Rectangle)">
      <summary>
            Initializes a new instance of the DropDownEventArgs class
            </summary>
      <param name="callerLink">Gets the command link which was selected by the user.</param>
      <param name="linkRect">Gets the screen coordinates of command link.</param>
    </member>
    <member name="T:C1.Win.C1Command.DropDownEventHandler">
      <summary>
            Represents the method that will handle a DropDown event.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A DropDownEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Command.MeasureLinkEventArgs">
      <summary>
            Provides data for the MeasureLink event.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.MeasureLinkEventArgs.#ctor(System.Drawing.Graphics,C1.Win.C1Command.C1CommandLink,System.Int32,System.Int32)">
      <summary>
            Initializes a new instance of the MeasureLinkEventArgs class
            </summary>
      <param name="graphics">Specifies the Graphics object to use for measuring.</param>
      <param name="link">Specifies the link that needs to be measured.</param>
      <param name="width">Specifies the width of the link.</param>
      <param name="height">Specifies the height of the link.</param>
    </member>
    <member name="P:C1.Win.C1Command.MeasureLinkEventArgs.Link">
      <summary>
            Specifies the link that needs to be measured.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.MeasureLinkEventArgs.Graphics">
      <summary>
            Specifies the Graphics object to use for measuring.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.MeasureLinkEventArgs.Width">
      <summary>
            Specifies the width of the link.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.MeasureLinkEventArgs.Height">
      <summary>
            Specifies the height of the link.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.MeasureLinkEventHandler">
      <summary>
            Represents the method that will handle a MeasureLink event.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A MeasureLinkEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Command.LayoutLinkEventArgs">
      <summary>
            Provides data for the LayoutLink event.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.LayoutLinkEventArgs.Link">
      <summary>
            Specifies the command link for the layout.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.LayoutLinkEventArgs.Bounds">
      <summary>
            The bounding rectangle of the command link.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.LayoutLinkEventArgs.TextRect">
      <summary>
            Text rectangle layout.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.LayoutLinkEventArgs.ImageRect">
      <summary>
            Image rectangle layout.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.LayoutLinkEventArgs.ControlLocation">
      <summary>
            The location of the nested control (valid only if the command is a C1CommandControl).
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.LayoutLinkEventArgs.#ctor(C1.Win.C1Command.C1CommandLink,System.Drawing.Rectangle,System.Drawing.Rectangle,System.Drawing.Rectangle,System.Drawing.Point)">
      <summary>
            Initializes a new instance of the LayoutLinkEventArgs class
            </summary>
      <param name="link">Specifies the command link for the layout.</param>
      <param name="bounds">The bounding rectangle of the command link.</param>
      <param name="textrect">Text rectangle layout.</param>
      <param name="imagerect">Image rectangle layout.</param>
      <param name="controllocation">The location of the nested control</param>
    </member>
    <member name="T:C1.Win.C1Command.LayoutLinkEventHandler">
      <summary>
            Represents the method that will handle a LayoutLink event.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A LayoutLinkEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Command.DrawLinkEventArgs">
      <summary>
            Provides data for the DrawLink event.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.DrawLinkEventArgs.Link">
      <summary>
            The command link to draw.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.DrawLinkEventArgs.Graphics">
      <summary>
            The Graphics object to draw on.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.DrawLinkEventArgs.Bounds">
      <summary>
            The bounding rectangle of the link.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.DrawLinkEventArgs.Done">
      <summary>
            Flag indicating whether the user completed the drawing. If set to true, no further drawing is performed. 
            Otherwise, the default drawing is done.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.DrawLinkEventArgs.#ctor(System.Drawing.Graphics,C1.Win.C1Command.C1CommandLink,System.Drawing.Rectangle,System.Boolean)">
      <summary>
            Initializes a new instance of the DrawLinkEventArgs class
            </summary>
      <param name="graphics">The Graphics object to draw on.</param>
      <param name="link">The command link to draw.</param>
      <param name="bounds">The bounding rectangle of the link.</param>
      <param name="done">Flag indicating whether the user completed the drawing.</param>
    </member>
    <member name="T:C1.Win.C1Command.DrawLinkEventHandler">
      <summary>
            Represents the method that will handle a DrawLink event.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A DrawLinkEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Command.DrawBarEventArgs">
      <summary>
            Provides data for the DrawBar event.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.DrawBarEventArgs.Graphics">
      <summary>
            The Graphics object to draw on.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.DrawBarEventArgs.Bounds">
      <summary>
            The bounding rectangle of the bar (menu or toolbar).
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.DrawBarEventArgs.Path">
      <summary>
            The graphics path bounding the bar (menu or toolbar).
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.DrawBarEventArgs.Done">
      <summary>
            Flag indicating whether the user completed the drawing. If set to true, no further drawing is performed.
            Otherwise, the default drawing is done.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.DrawBarEventArgs.#ctor(System.Drawing.Graphics,System.Drawing.Rectangle,System.Drawing.Drawing2D.GraphicsPath,System.Boolean)">
      <summary>
            Initializes a new instance of the DrawBarEventArgs class
            </summary>
      <param name="graphics">The Graphics object to draw on.</param>
      <param name="bounds">The bounding rectangle of the bar</param>
      <param name="path">The graphics path bounding the bar</param>
      <param name="done">Flag indicating whether the user completed the drawing.</param>
    </member>
    <member name="T:C1.Win.C1Command.DrawBarEventHandler">
      <summary>
            Represents the method that will handle a DrawBar event.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A DrawBarEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Command.MeasurePageEventArgs">
      <summary>
            Provides data for the MeasurePage event.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.MeasurePageEventArgs.Page">
      <summary>
            The C1OutPage object to measure.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.MeasurePageEventArgs.Graphics">
      <summary>
            Specifies the Graphics object to use for measuring.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.MeasurePageEventArgs.Height">
      <summary>
            The height of the page caption.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.MeasurePageEventArgs.#ctor(System.Drawing.Graphics,C1.Win.C1Command.C1OutPage,System.Int32)">
      <summary>
            Initializes a new instance of the MeasurePageEventArgs class
            </summary>
      <param name="graphics">Specifies the Graphics object to use for measuring.</param>
      <param name="page">The C1OutPage object to measure.</param>
      <param name="height">The height of the page caption.</param>
    </member>
    <member name="T:C1.Win.C1Command.MeasurePageEventHandler">
      <summary>
            Represents the method that will handle a MeasurePage event.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A MeasurePageEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Command.DrawPageEventArgs">
      <summary>
            Provides data for the DrawPage event.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.DrawPageEventArgs.Page">
      <summary>
            The page to draw.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.DrawPageEventArgs.Graphics">
      <summary>
            The Graphics object to draw on.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.DrawPageEventArgs.Bounds">
      <summary>
            The bounding rectangle of the page.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.DrawPageEventArgs.Done">
      <summary>
            Flag indicating whether the user completed the drawing. If set to true, no further drawing is performed.
            Otherwise, the default drawing is done.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.DrawPageEventArgs.#ctor(System.Drawing.Graphics,C1.Win.C1Command.C1OutPage,System.Drawing.Rectangle,System.Boolean)">
      <summary>
            Initializes a new instance of the DrawPageEventArgs class.
            </summary>
      <param name="graphics">The Graphics object to draw on.</param>
      <param name="page">The page to draw.</param>
      <param name="bounds">The bounding rectangle of the page.</param>
      <param name="done">Flag indicating whether the user completed the drawing.</param>
    </member>
    <member name="T:C1.Win.C1Command.DrawPageEventHandler">
      <summary>
            Represents the method that will handle a DrawPage event.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A DrawPageEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Command.PageClickEventArgs">
      <summary>
            Provides data for the PageClick event.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.PageClickEventArgs.Page">
      <summary>
            The page clicked.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.PageClickEventArgs.#ctor(C1.Win.C1Command.C1OutPage)">
      <summary>
            Initializes a new instance of the DrawPageEventArgs class.
            </summary>
      <param name="page">The page clicked.</param>
    </member>
    <member name="T:C1.Win.C1Command.PageClickEventHandler">
      <summary>
            Represents the method that will handle a DrawPage event.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A PageClickEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Command.MeasureTabEventArgs">
      <summary>
            Provides data for the MeasureTab event.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.MeasureTabEventArgs.Page">
      <summary>
            The C1DockingTabPage object to measure.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.MeasureTabEventArgs.Graphics">
      <summary>
            Specifies the Graphics object to use for measuring.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.MeasureTabEventArgs.Width">
      <summary>
            The width of the tab.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.MeasureTabEventArgs.#ctor(System.Drawing.Graphics,C1.Win.C1Command.C1DockingTabPage,System.Int32)">
      <summary>
            Initializes a new instance of the MeasureTabEventArgs class.
            </summary>
      <param name="graphics">Specifies the Graphics object to use for measuring.</param>
      <param name="page">The C1DockingTabPage object to measure.</param>
      <param name="width">The width of the tab..</param>
    </member>
    <member name="T:C1.Win.C1Command.MeasureTabEventHandler">
      <summary>
            Represents the method that will handle a MeasureTab event.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A MeasureTabEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Command.AutoCreatedDockingTabEventArgs">
      <summary>
            Provides data for the AutoCreatedDockingTab event.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.AutoCreatedDockingTabEventArgs.NewDockingTab">
      <summary>
            Gets the new DockingTab.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.AutoCreatedDockingTabEventArgs.#ctor(C1.Win.C1Command.C1DockingTab)">
      <summary>
            Initializes a new instance of the AutoCreatedDockingTabEventArgs class
            </summary>
      <param name="newDockingTab">The new DockingTab.</param>
    </member>
    <member name="T:C1.Win.C1Command.AutoCreatedDockingTabHandler">
      <summary>
            Represents the method that will handle a AutoCreatedDockingTab event.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A AutoCreatedDockingTabEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Command.DrawTabEventArgs">
      <summary>
            Provides data for the DrawTab event.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.DrawTabEventArgs.Page">
      <summary>
            The tab page to draw.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.DrawTabEventArgs.Graphics">
      <summary>
            The Graphics object to draw on.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.DrawTabEventArgs.Bounds">
      <summary>
            The bounding rectangle of the tab.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.DrawTabEventArgs.Done">
      <summary>
            Flag indicating whether the user completed the drawing. If set to true, no further drawing is performed.
            Otherwise, the default drawing is done.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.DrawTabEventArgs.#ctor(System.Drawing.Graphics,C1.Win.C1Command.C1DockingTabPage,System.Drawing.Rectangle,System.Boolean)">
      <summary>
            Initializes a new instance of the DrawTabEventArgs class
            </summary>
      <param name="graphics">The Graphics object to draw on.</param>
      <param name="page">The tab page to draw.</param>
      <param name="bounds">The bounding rectangle of the tab.</param>
      <param name="done">Flag indicating whether the user completed the drawing.</param>
    </member>
    <member name="T:C1.Win.C1Command.DrawTabEventHandler">
      <summary>
            Represents the method that will handle a DrawTab event.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A DrawTabEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Command.DrawArrowEventArgs">
      <summary>
            Provides data for the DrawArrow event.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.DrawArrowEventArgs.Graphics">
      <summary>
            The Graphics object to draw on.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.DrawArrowEventArgs.Bounds">
      <summary>
            The bounding rectangle of the Arrow button.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.DrawArrowEventArgs.Enabled">
      <summary>
            Gets the value if button is enabled.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.DrawArrowEventArgs.Pressed">
      <summary>
            Gets the value if button is pressed.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.DrawArrowEventArgs.Up">
      <summary>
            Indicates if Up button is drawn or not.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.DrawArrowEventArgs.Done">
      <summary>
            Flag indicating whether the user completed the drawing.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.DrawArrowEventArgs.#ctor(System.Drawing.Graphics,System.Drawing.Rectangle,System.Boolean,System.Boolean,System.Boolean,System.Boolean)">
      <summary>
            Initializes a new instance of the DrawArrowEventArgs class
            </summary>
      <param name="graphics">The Graphics object to draw on.</param>
      <param name="bounds">The bounding rectangle of the Arrow button.</param>
      <param name="enabled">Gets the value if button is enabled.</param>
      <param name="pressed">Gets the value if button is pressed.</param>
      <param name="up">Indicates if Up button is drawn or not.</param>
      <param name="done">Flag indicating whether the user completed the drawing.</param>
    </member>
    <member name="T:C1.Win.C1Command.DrawArrowEventHandler">
      <summary>
            Represents the method that will handle a DrawArrow event.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A DrawArrowEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Command.MeasureFloatingCaptionEventArgs">
      <summary>
            Provides data for the MeasureFloatingCaption event.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.MeasureFloatingCaptionEventArgs.Graphics">
      <summary>
            The Graphics object to use for measuring.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.MeasureFloatingCaptionEventArgs.Height">
      <summary>
            The height of the floating caption.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.MeasureFloatingCaptionEventArgs.#ctor(System.Drawing.Graphics,System.Int32)">
      <summary>
            Initializes a new instance of the MeasureFloatingCaptionEventArgs class
            </summary>
      <param name="graphics">The Graphics object to use for measuring.</param>
      <param name="height">The height of the floating caption.</param>
    </member>
    <member name="T:C1.Win.C1Command.MeasureFloatingCaptionEventHandler">
      <summary>
            Represents the method that will handle a MeasureFloatingCaption event.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A MeasureFloatingCaptionEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Command.DrawFloatingCaptionEventArgs">
      <summary>
            Provides data for the DrawFloatingCaption event.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.DrawFloatingCaptionEventArgs.Graphics">
      <summary>
            The Graphics object to draw on.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.DrawFloatingCaptionEventArgs.Bounds">
      <summary>
            The bounding rectangle of the floating caption.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.DrawFloatingCaptionEventArgs.Done">
      <summary>
            Flag indicating whether the user completed the drawing. If set to true, no further drawing is performed.
            Otherwise, the default drawing is done.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.DrawFloatingCaptionEventArgs.#ctor(System.Drawing.Graphics,System.Drawing.Rectangle,System.Boolean)">
      <summary>
            Initializes a new instance of the DrawFloatingCaptionEventArgs class
            </summary>
      <param name="graphics">The Graphics object to draw on.</param>
      <param name="bounds">The bounding rectangle of the floating caption.</param>
      <param name="done">Flag indicating whether the user completed the drawing.</param>
    </member>
    <member name="T:C1.Win.C1Command.DrawFloatingCaptionEventHandler">
      <summary>
            Represents the method that will handle a DrawFloatingCaption event.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A DrawFloatingCaptionEventArgs object that contains the event data..</param>
    </member>
    <member name="T:C1.Win.C1Command.CommandLinkEventArgs">
      <summary>
            Provides data for the CommandLinkAdded or CommandLinkRemoved event.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.CommandLinkEventArgs.CommandLink">
      <summary>
            Gets the command link.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.CommandLinkEventArgs.#ctor(C1.Win.C1Command.C1CommandLink)">
      <summary>
            Initializes a new instance of the CommandLinkEventArgs class
            </summary>
      <param name="commandLink">
      </param>
    </member>
    <member name="T:C1.Win.C1Command.CommandLinkEventHandler">
      <summary>
            Represents the method that will handle a CommandLinkAdded or CommandLinkRemoved event.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A CommandLinkEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Command.SelectedIndexChangingEventArgs">
      <summary>
            Provides data for the SelectedIndexChanging event.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.SelectedIndexChangingEventArgs.NewIndex">
      <summary>
            Indicates the new index of the selected tab page.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.SelectedIndexChangingEventArgs.CanCancel">
      <summary>
            Indicates whether the change of the selected index can be cancelled by the event handler. 
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.SelectedIndexChangingEventArgs.#ctor(System.Int32,System.Boolean)">
      <summary>
            Initializes a new instance of the SelectedIndexChangingEventArgs class
            </summary>
      <param name="newIndex">Indicates the new index of the selected tab page.</param>
      <param name="canCancel">Indicates whether the change of the selected index can be cancelled by the event handler.</param>
    </member>
    <member name="T:C1.Win.C1Command.SelectedIndexChangingEventHandler">
      <summary>
            Represents the method that will handle a SelectedIndexChanging event.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A SelectedIndexChanging object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Command.TabPageEventArgs">
      <summary>
            Provides data for the TabPageClosed event.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.TabPageEventArgs.TabPage">
      <summary>
            Indicates the tab page.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.TabPageEventArgs.#ctor(C1.Win.C1Command.C1DockingTabPage)">
      <summary>
            Initializes a new instance of the TabPageEventArgs class
            </summary>
      <param name="tabPage">Indicates the tab page.</param>
    </member>
    <member name="T:C1.Win.C1Command.TabPageEventHandler">
      <summary>
            Represents the method that will handle a TabPageClosed event.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A TabPageEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Command.TabPageCancelEventArgs">
      <summary>
            Provides data for the TabPageClosing event.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.TabPageCancelEventArgs.TabPage">
      <summary>
            Indicates the tab page.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.TabPageCancelEventArgs.#ctor(C1.Win.C1Command.C1DockingTabPage)">
      <summary>
            Initializes a new instance of the TabPageCancelEventArgs class
            </summary>
      <param name="tabPage">Indicates the tab page</param>
    </member>
    <member name="T:C1.Win.C1Command.TabPageCancelEventHandler">
      <summary>
            Represents the method that will handle a TabPageClosing event.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A TabPageCancelEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Command.PanelEventArgs">
      <summary>
            Provides data for the PanelClosed event.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.PanelEventArgs.Panel">
      <summary>
            Indicates the tab page.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.PanelEventArgs.#ctor(C1.Win.C1Command.C1NavBarPanel)">
      <summary>
            Initializes a new instance of the PanelEventArgs class
            </summary>
      <param name="panel">Indicates the navbar panel.</param>
    </member>
    <member name="T:C1.Win.C1Command.PanelEventHandler">
      <summary>
            Represents the method that will handle a PanelClosed event.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A PanelEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Command.PanelCancelEventArgs">
      <summary>
            Provides data for the PanelClosing event.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.PanelCancelEventArgs.Panel">
      <summary>
            Indicates the nav bar panel.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.PanelCancelEventArgs.#ctor(C1.Win.C1Command.C1NavBarPanel)">
      <summary>
            Initializes a new instance of thePanelCancelEventArgs class
            </summary>
      <param name="panel">Indicates the nav bar panel</param>
    </member>
    <member name="T:C1.Win.C1Command.PanelCancelEventHandler">
      <summary>
            Represents the method that will handle a PanelClosing event.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A PanelCancelEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Command.C1TopicBarHitTestTypeEnum">
      <summary>
            Type of topic bar element at a specific point on the control.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1TopicBarHitTestTypeEnum.None">
      <summary>
            The point is out of the C1TopicBar's area.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1TopicBarHitTestTypeEnum.Empty">
      <summary>
            The point is in the C1TopicBar's empty area.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1TopicBarHitTestTypeEnum.PageTitle">
      <summary>
            The point is on empty space of a page title area (not over a text, image or expand/collapse indicator).
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1TopicBarHitTestTypeEnum.PageTitleIndicator">
      <summary>
            The point is on expand/collapse indicator of a page title.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1TopicBarHitTestTypeEnum.PageTitleImage">
      <summary>
            The point is on image of a page title.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1TopicBarHitTestTypeEnum.PageTitleText">
      <summary>
            The point is on text of a page title.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1TopicBarHitTestTypeEnum.PageBody">
      <summary>
            The point is on a page body (but not on a link).
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1TopicBarHitTestTypeEnum.Link">
      <summary>
            The point is on empty space of a link (not over a text or image).
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1TopicBarHitTestTypeEnum.LinkImage">
      <summary>
            The point is on image of a link.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1TopicBarHitTestTypeEnum.LinkText">
      <summary>
            The point is on text of a link.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.C1TopicBarHitTestInfo">
      <summary>
            Contains information, such as the page and the link
            in the <see cref="T:C1.Win.C1Command.C1TopicBar" /> control.
            </summary>
      <remarks>
            Use the <see cref="M:C1.Win.C1Command.C1TopicBar.HitTest" /> method to obtain a <see cref="T:C1.Win.C1Command.C1TopicBarHitTestInfo" /> value.
            </remarks>
    </member>
    <member name="F:C1.Win.C1Command.C1TopicBarHitTestInfo.Type">
      <summary>
            Gets a <see cref="T:C1.Win.C1Command.C1TopicBarHitTestTypeEnum" /> that describes the type of element described by a <see cref="T:C1.Win.C1Command.C1TopicBarHitTestInfo" />.
            </summary>
      <remarks>
            This property allows you to determine whether the point corresponds to a topic link or
            to special elements such as title image or text, and so on.</remarks>
    </member>
    <member name="F:C1.Win.C1Command.C1TopicBarHitTestInfo.Page">
      <summary>
            Get the <see cref="T:C1.Win.C1Command.C1TopicPage" /> at the point being tested (if the point does not correspond to a page, it returns null).
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.C1TopicBarHitTestInfo.Link">
      <summary>
            Get the <see cref="T:C1.Win.C1Command.C1TopicLink" /> at the point being tested (if the point does not correspond to a page, it returns null).
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.RadialStrings">
      <summary>
            Static class containing UI strings used by the designer.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.C1RadialMenu">
      <summary>
            Represents a radial menu.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1RadialMenu.#ctor">
      <summary>
            Initializes a new instance of the C1RadialMenu class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1RadialMenu.ShouldSerializeCursor">
      <summary>This method is for internal use only.</summary>
    </member>
    <member name="M:C1.Win.C1Command.C1RadialMenu.ResetCursor">
      <summary>This method is for internal use only.</summary>
    </member>
    <member name="M:C1.Win.C1Command.C1RadialMenu.ShowMenu(System.Windows.Forms.Form,System.Drawing.Point,System.Boolean)">
      <summary>
            Shows the radial menu at the specified screen point.
            If the menu is currently visible at another location, it is hidden first.
            </summary>
      <param name="form">The owner form.</param>
      <param name="pt">The radial menu center's screen coordinates.</param>
      <param name="expand">Indicates whether the menu should show initially expanded.</param>
    </member>
    <member name="M:C1.Win.C1Command.C1RadialMenu.HideMenu">
      <summary>
            Hides the radial menu if it is currently visible.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.C1RadialMenu.Move(System.Int32,System.Int32)">
      <summary>
            Moves the radial menu if it is currently visible,
            does nothing otherwise.
            </summary>
      <param name="dx">The X offset.</param>
      <param name="dy">The Y offset.</param>
    </member>
    <member name="E:C1.Win.C1Command.C1RadialMenu.PropertyChanged">
      <summary>
            Occurs when a public property has changed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1RadialMenu.MenuClosed">
      <summary>
            Occurs when the menu is closed.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.C1RadialMenu.ItemClick">
      <summary>
            Occurs when a menu item is clicked.
            </summary>
      <remarks>
            If the clicked item has its own Click event associated with it,
            that event occurs immediately before this one.
            </remarks>
    </member>
    <member name="P:C1.Win.C1Command.C1RadialMenu.Radius">
      <summary>
            Gets or sets the menu radius in the expanded state.
            <para>The default value is 160.</para></summary>
    </member>
    <member name="P:C1.Win.C1Command.C1RadialMenu.ButtonRadius">
      <summary>
            Gets or sets the radius of the central button.
            <para>The default value is 28.</para></summary>
    </member>
    <member name="P:C1.Win.C1Command.C1RadialMenu.InnerRadius">
      <summary>
            Gets or sets the radius separating the area filled with items' background color on the outside
            from the inner area filled with radial menu's own background.
            <para>The default value is 70.</para></summary>
    </member>
    <member name="P:C1.Win.C1Command.C1RadialMenu.BorderWidth">
      <summary>
            Gets or sets the width of the radial menu's outer border.
            <para>The default value is 24.</para></summary>
    </member>
    <member name="P:C1.Win.C1Command.C1RadialMenu.TooltipPosition">
      <summary>
            Gets or sets the position of the tooltip relative to the radial menu.
            <para>The default value is "left".</para></summary>
    </member>
    <member name="P:C1.Win.C1Command.C1RadialMenu.UseAnimation">
      <summary>
            Gets or sets a value indicating whether the menu should use animation effects.
            <para>The default value is true.</para></summary>
    </member>
    <member name="P:C1.Win.C1Command.C1RadialMenu.Items">
      <summary>
            Gets or sets the collection of items contained in this menu.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1RadialMenu.Image">
      <summary>
            Gets or sets the image shown in the radial menu's center button.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1RadialMenu.Cursor">
      <summary>
            Gets or sets the cursor used by the radial menu.
            <para>The default value is "hand".</para></summary>
    </member>
    <member name="P:C1.Win.C1Command.C1RadialMenu.AutoHide">
      <summary>
            Gets or sets a value indicating whether the radial menu should automatically hide
            on certain events (such as lost focus etc).
            <para>The default is true.</para></summary>
    </member>
    <member name="P:C1.Win.C1Command.C1RadialMenu.Enabled">
      <summary>
            Gets or sets a value indicating whether the radial menu is enabled.
            <para>The default is true.</para></summary>
    </member>
    <member name="P:C1.Win.C1Command.C1RadialMenu.Visible">
      <summary>
            Gets a value indicating whether the radial menu is currently visible.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1RadialMenu.Expanded">
      <summary>
            Gets a value indicating whether the radial menu is currently expanded.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.C1RadialMenu.CenterOnScreen">
      <summary>
            Gets or sets the screen coordinates of the menu's center.
            If the menu is currently invisible (<see cref="P:C1.Win.C1Command.C1RadialMenu.Visible" /> is false),
            setting this property throws an exception.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.RadialMenuItemClickEventArgs">
      <summary>
            Arguments for the RadialMenuItemClickEventHandler event handler.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.RadialMenuItemClickEventArgs.#ctor(System.Object)">
      <summary>
            Initializes a new instance of the RadialMenuItemClickEventArgs class.
            </summary>
      <param name="item">The item that was clicked.</param>
    </member>
    <member name="P:C1.Win.C1Command.RadialMenuItemClickEventArgs.Item">
      <summary>
            Gets the item that was clicked.
            This may be either an object of a type derived from <see cref="T:C1.Win.C1Command.RadialMenuItemBase" />,
            or a <see cref="T:C1.Win.C1Command.C1CommandLink" />.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.RadialMenuItemClickEventArgs.RadialMenuItem">
      <summary>
            If the clicked item was a <see cref="P:C1.Win.C1Command.RadialMenuItemClickEventArgs.RadialMenuItem" />, returns that item;
            otherwise, returns null.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.RadialMenuItemClickEventArgs.CommandLink">
      <summary>
            If the clicked item was a <see cref="T:C1.Win.C1Command.C1CommandLink" />, returns that link;
            otherwise, returns null.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.RadialMenuItemClickEventHandler">
      <summary>
            Represents the method that will handle a RadialMenuItemClick event.
            </summary>
      <param name="sender">The source of the event.</param>
      <param name="e">A RadialMenuItemClickEventArgs object that contains the event data.</param>
    </member>
    <member name="T:C1.Win.C1Command.TooltipPosition">
      <summary>
            Specifies where to show menu tooltips relative to the radial menu.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.TooltipPosition.None">
      <summary>
            Tooltips are not shown.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.TooltipPosition.Left">
      <summary>
            Tooltips are shown to the left of the menu.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.TooltipPosition.Top">
      <summary>
            Tooltips are shown above the menu.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.TooltipPosition.Right">
      <summary>
            Tooltips are shown to the right of the menu.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.TooltipPosition.Bottom">
      <summary>
            Tooltips are shown below the menu.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.RadialMenuItemBase">
      <summary>
            Abstract base class for items in a radial menu.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.RadialMenuItemBase.ShouldSerializeName">
      <summary>This method is for internal use only.</summary>
    </member>
    <member name="M:C1.Win.C1Command.RadialMenuItemBase.ResetName">
      <summary>This method is for internal use only.</summary>
    </member>
    <member name="M:C1.Win.C1Command.RadialMenuItemBase.ShouldSerializeText">
      <summary>This method is for internal use only.</summary>
    </member>
    <member name="M:C1.Win.C1Command.RadialMenuItemBase.ResetText">
      <summary>This method is for internal use only.</summary>
    </member>
    <member name="M:C1.Win.C1Command.RadialMenuItemBase.ShouldSerializeToolTip">
      <summary>This method is for internal use only.</summary>
    </member>
    <member name="M:C1.Win.C1Command.RadialMenuItemBase.ResetToolTip">
      <summary>This method is for internal use only.</summary>
    </member>
    <member name="M:C1.Win.C1Command.RadialMenuItemBase.ShouldSerializeImage">
      <summary>This method is for internal use only.</summary>
    </member>
    <member name="M:C1.Win.C1Command.RadialMenuItemBase.ResetImage">
      <summary>This method is for internal use only.</summary>
    </member>
    <member name="M:C1.Win.C1Command.RadialMenuItemBase.ShouldSerializeChecked">
      <summary>This method is for internal use only.</summary>
    </member>
    <member name="M:C1.Win.C1Command.RadialMenuItemBase.ResetChecked">
      <summary>This method is for internal use only.</summary>
    </member>
    <member name="M:C1.Win.C1Command.RadialMenuItemBase.ShouldSerializeEnabled">
      <summary>This method is for internal use only.</summary>
    </member>
    <member name="M:C1.Win.C1Command.RadialMenuItemBase.ResetEnabled">
      <summary>This method is for internal use only.</summary>
    </member>
    <member name="P:C1.Win.C1Command.RadialMenuItemBase.Name">
      <summary>
            Gets or sets the name used to identify the item.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.RadialMenuItemBase.Text">
      <summary>
            Gets or sets the text associated with the menu item.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.RadialMenuItemBase.ToolTip">
      <summary>
            Gets or sets the tooltip associated with the menu item.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.RadialMenuItemBase.Image">
      <summary>
            Gets or sets the image associated with the menu item.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.RadialMenuItemBase.Checked">
      <summary>
            Gets or sets a value indicating whether the menu item is checked.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.RadialMenuItemBase.Enabled">
      <summary>
            Gets or sets a value indicating whether the menu item is enabled.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.RadialMenuItem">
      <summary>
            Represents a radial menu item, possibly with a sub-menu.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.RadialMenuItem.#ctor">
      <summary>
            Initializes a new instance of the RadialMenuItem class.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.RadialMenuItem.OnClick(System.EventArgs)">
      <summary>
            Invokes the Click event.
            </summary>
      <param name="e">
      </param>
    </member>
    <member name="M:C1.Win.C1Command.RadialMenuItem.ShouldSerializeName">
      <summary>This method is for internal use only.</summary>
    </member>
    <member name="M:C1.Win.C1Command.RadialMenuItem.ResetName">
      <summary>This method is for internal use only.</summary>
    </member>
    <member name="M:C1.Win.C1Command.RadialMenuItem.ShouldSerializeText">
      <summary>This method is for internal use only.</summary>
    </member>
    <member name="M:C1.Win.C1Command.RadialMenuItem.ResetText">
      <summary>This method is for internal use only.</summary>
    </member>
    <member name="M:C1.Win.C1Command.RadialMenuItem.ShouldSerializeToolTip">
      <summary>This method is for internal use only.</summary>
    </member>
    <member name="M:C1.Win.C1Command.RadialMenuItem.ResetToolTip">
      <summary>This method is for internal use only.</summary>
    </member>
    <member name="M:C1.Win.C1Command.RadialMenuItem.ShouldSerializeImage">
      <summary>This method is for internal use only.</summary>
    </member>
    <member name="M:C1.Win.C1Command.RadialMenuItem.ResetImage">
      <summary>This method is for internal use only.</summary>
    </member>
    <member name="M:C1.Win.C1Command.RadialMenuItem.ShouldSerializeChecked">
      <summary>This method is for internal use only.</summary>
    </member>
    <member name="M:C1.Win.C1Command.RadialMenuItem.ResetChecked">
      <summary>This method is for internal use only.</summary>
    </member>
    <member name="M:C1.Win.C1Command.RadialMenuItem.ShouldSerializeEnabled">
      <summary>This method is for internal use only.</summary>
    </member>
    <member name="M:C1.Win.C1Command.RadialMenuItem.ResetEnabled">
      <summary>This method is for internal use only.</summary>
    </member>
    <member name="P:C1.Win.C1Command.RadialMenuItem.Items">
      <summary>
            Gets or sets the collection of items contained in this menu item.
            </summary>
    </member>
    <member name="E:C1.Win.C1Command.RadialMenuItem.Click">
      <summary>
            Occurs when the menu item is clicked.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.RadialMenuItem.Parent">
      <summary>
            Gets the parent of this menu item (which may be either a <see cref="T:C1.Win.C1Command.C1RadialMenu" />,
            or another <see cref="T:C1.Win.C1Command.RadialMenuItem" />).
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.RadialMenuItem.BackColor">
      <summary>
            Gets or sets the background color of this menu item.
            Empty (default) means that item background specified for the whole menu is used.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.RadialMenuItem.HighlightColor">
      <summary>
            Gets or sets the highlight color of this menu item.
            Empty (default) means that item highlight color specified for the whole menu is used.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.RadialMenuItem.CheckColor">
      <summary>
            Gets or sets the check mark color of this menu item.
            Empty (default) means that item check mark color specified for the whole menu is used.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.RadialMenuItem.BorderColor">
      <summary>
            Gets or sets the non-expandable border color of this menu item.
            Empty (default) means that item non-expandable border color specified for the whole menu is used.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.RadialMenuItem.HotExpandableBorderColor">
      <summary>
            Gets or sets the hot expandable border color of this menu item.
            Empty (default) means that style specified for the whole menu is used.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.RadialMenuItem.UserData">
      <summary>
            Gets or sets an arbitrary object that can be associated with this menu item.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.RadialMenuCommandItem">
      <summary>
            Represents a radial menu item associated with a <see cref="T:C1.Win.C1Command.C1Command" />.
            </summary>
    </member>
    <member name="M:C1.Win.C1Command.RadialMenuCommandItem.ShouldSerializeCommand">
      <summary>This method is for internal use only.</summary>
    </member>
    <member name="M:C1.Win.C1Command.RadialMenuCommandItem.ResetCommand">
      <summary>This method is for internal use only.</summary>
    </member>
    <member name="M:C1.Win.C1Command.RadialMenuCommandItem.ShouldSerializeText">
      <summary>This method is for internal use only.</summary>
    </member>
    <member name="M:C1.Win.C1Command.RadialMenuCommandItem.ResetText">
      <summary>This method is for internal use only.</summary>
    </member>
    <member name="M:C1.Win.C1Command.RadialMenuCommandItem.ShouldSerializeToolTip">
      <summary>This method is for internal use only.</summary>
    </member>
    <member name="M:C1.Win.C1Command.RadialMenuCommandItem.ResetToolTip">
      <summary>This method is for internal use only.</summary>
    </member>
    <member name="M:C1.Win.C1Command.RadialMenuCommandItem.ShouldSerializeImage">
      <summary>This method is for internal use only.</summary>
    </member>
    <member name="M:C1.Win.C1Command.RadialMenuCommandItem.ResetImage">
      <summary>This method is for internal use only.</summary>
    </member>
    <member name="M:C1.Win.C1Command.RadialMenuCommandItem.ShouldSerializeChecked">
      <summary>This method is for internal use only.</summary>
    </member>
    <member name="M:C1.Win.C1Command.RadialMenuCommandItem.ResetChecked">
      <summary>This method is for internal use only.</summary>
    </member>
    <member name="M:C1.Win.C1Command.RadialMenuCommandItem.ShouldSerializeEnabled">
      <summary>This method is for internal use only.</summary>
    </member>
    <member name="M:C1.Win.C1Command.RadialMenuCommandItem.ResetEnabled">
      <summary>This method is for internal use only.</summary>
    </member>
    <member name="P:C1.Win.C1Command.RadialMenuCommandItem.Command">
      <summary>
            Gets or sets the <see cref="T:C1.Win.C1Command.C1Command" /> associated with this menu item.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.RadialMenuCommandItem.Text">
      <summary>
            Overridden. Gets or sets the text shown by this menu item.
            Note that setting this property does not change the text
            on the associated C1Command.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.RadialMenuCommandItem.ToolTip">
      <summary>
            Overridden. Gets or sets the tooltip shown by this menu item.
            Note that setting this property does not change the tooltip
            on the associated C1Command.
            </summary>
    </member>
    <member name="P:C1.Win.C1Command.RadialMenuCommandItem.Image">
      <summary>
            Overridden. Gets or sets the image shown by this menu item.
            Note that setting this property does not change the image
            on the associated C1Command.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.C1Stack">
      <summary>
            Simple stack with indexed access, based on System.Collections.ArrayList
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.HookEventArgs">
      <summary>
            Provides data for a Hook event.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.HookEventArgs.HookCode">
      <summary>
            Hook code.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.HookEventArgs.wParam">
      <summary>
            WPARAM argument.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.HookEventArgs.lParam">
      <summary>
            LPARAM argument.
            </summary>
    </member>
    <member name="T:C1.Win.C1Command.HookType">
      <summary>
            Hook Types
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.HookType.WH_MSGFILTER">
      <summary>
            Installs a hook procedure that monitors messages generated as a result of an input event in a dialog box, message box, menu, or scroll bar.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.HookType.WH_JOURNALRECORD">
      <summary>
            Installs a hook procedure that records input messages posted to the system message queue. 
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.HookType.WH_JOURNALPLAYBACK">
      <summary>
            Installs a hook procedure that posts messages previously recorded by a WH_JOURNALRECORD hook procedure.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.HookType.WH_KEYBOARD">
      <summary>
            Installs a hook procedure that monitors keystroke messages.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.HookType.WH_GETMESSAGE">
      <summary>
            Installs a hook procedure that monitors messages posted to a message queue.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.HookType.WH_CALLWNDPROC">
      <summary>
            Installs a hook procedure that monitors messages before the system sends them to the destination window procedure.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.HookType.WH_CBT">
      <summary>
            Installs a hook procedure that receives notifications useful to a computer-based training (CBT) application. 
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.HookType.WH_SYSMSGFILTER">
      <summary>
            Installs a hook procedure that monitors messages generated as a result of an input event in a dialog box, message box, menu, or scroll bar.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.HookType.WH_MOUSE">
      <summary>
            Installs a hook procedure that monitors mouse messages.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.HookType.WH_HARDWARE">
      <summary>
            This hook is not currently implemented in Win32.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.HookType.WH_DEBUG">
      <summary>
            Installs a hook procedure useful for debugging other hook procedures. 
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.HookType.WH_SHELL">
      <summary>
            Installs a hook procedure that receives notifications useful to shell applications.
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.HookType.WH_FOREGROUNDIDLE">
      <summary>
            Installs a hook procedure that will be called when the application's foreground thread is about to become idle. 
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.HookType.WH_CALLWNDPROCRET">
      <summary>
            Installs a hook procedure that monitors messages after they have been processed by the destination window procedure. 
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.HookType.WH_KEYBOARD_LL">
      <summary>
            Installs a hook procedure that monitors low-level keyboard input events. 
            </summary>
    </member>
    <member name="F:C1.Win.C1Command.HookType.WH_MOUSE_LL">
      <summary>
            Installs a hook procedure that monitors low-level mouse input events.
            </summary>
    </member>
  </members>
</doc>